// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:hadi_hia/app.dart';

void main() {
  testWidgets('App title appears on startup', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const HadiHiaApp());

    // Verify that our app title appears
    expect(find.text('HadiHia'), findsOneWidget);

    // Verify that the user type selection page is shown
    expect(find.text('How would you like to use HadiHia?'), findsOneWidget);
  });
}
