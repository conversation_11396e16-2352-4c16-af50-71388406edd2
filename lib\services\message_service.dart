import 'package:flutter/foundation.dart';
import '../models/message_model.dart';
import '../models/user_model.dart';

class MessageService extends ChangeNotifier {
  final List<Message> _messages = [];
  final List<Conversation> _conversations = [];
  bool _isLoading = false;

  List<Message> get messages => _messages;
  List<Conversation> get conversations => _conversations;
  bool get isLoading => _isLoading;

  MessageService() {
    _loadDummyData();
  }

  void _loadDummyData() {
    _isLoading = true;
    notifyListeners();

    // Add dummy conversations
    _conversations.addAll([
      Conversation(
        id: '1',
        customerId: 'customer1',
        customerName: 'Ahmed',
        customerImage: 'https://randomuser.me/api/portraits/men/1.jpg',
        sellerId: 'seller1',
        sellerName: 'Fatima\'s Crafts',
        sellerImage: 'https://randomuser.me/api/portraits/women/1.jpg',
        lastMessageTime: DateTime.now().subtract(const Duration(hours: 1)),
        lastMessageContent: 'Your order has been shipped!',
        unreadCount: 2,
        orderId: 'order1',
      ),
      Conversation(
        id: '2',
        customerId: 'customer1',
        customerName: 'Ahmed',
        customerImage: 'https://randomuser.me/api/portraits/men/1.jpg',
        sellerId: 'seller2',
        sellerName: 'Moroccan Treasures',
        sellerImage: 'https://randomuser.me/api/portraits/men/3.jpg',
        lastMessageTime: DateTime.now().subtract(const Duration(days: 1)),
        lastMessageContent: 'Thank you for your purchase!',
        unreadCount: 0,
        productId: 'product1',
      ),
      Conversation(
        id: '3',
        customerId: 'customer2',
        customerName: 'Layla',
        customerImage: 'https://randomuser.me/api/portraits/women/2.jpg',
        sellerId: 'seller1',
        sellerName: 'Fatima\'s Crafts',
        sellerImage: 'https://randomuser.me/api/portraits/women/1.jpg',
        lastMessageTime: DateTime.now().subtract(const Duration(days: 2)),
        lastMessageContent: 'Do you have this in blue?',
        unreadCount: 1,
        productId: 'product2',
      ),
    ]);

    // Add dummy messages for conversation 1
    _messages.addAll([
      Message(
        id: '1',
        senderId: 'seller1',
        receiverId: 'customer1',
        content: 'Hello! Thank you for your order.',
        timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 2)),
        isRead: true,
        orderId: 'order1',
      ),
      Message(
        id: '2',
        senderId: 'customer1',
        receiverId: 'seller1',
        content: 'When will it be shipped?',
        timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 1)),
        isRead: true,
        orderId: 'order1',
      ),
      Message(
        id: '3',
        senderId: 'seller1',
        receiverId: 'customer1',
        content: 'We\'ll ship it tomorrow.',
        timestamp: DateTime.now().subtract(const Duration(days: 1)),
        isRead: true,
        orderId: 'order1',
      ),
      Message(
        id: '4',
        senderId: 'customer1',
        receiverId: 'seller1',
        content: 'Great, thank you!',
        timestamp: DateTime.now().subtract(const Duration(hours: 23)),
        isRead: true,
        orderId: 'order1',
      ),
      Message(
        id: '5',
        senderId: 'seller1',
        receiverId: 'customer1',
        content: 'Your order has been shipped!',
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        isRead: false,
        orderId: 'order1',
      ),
      Message(
        id: '6',
        senderId: 'seller1',
        receiverId: 'customer1',
        content:
            'You can track it using the following link: https://tracking.example.com',
        timestamp: DateTime.now().subtract(const Duration(minutes: 59)),
        isRead: false,
        orderId: 'order1',
      ),
    ]);

    _isLoading = false;
    notifyListeners();
  }

  List<Message> getMessagesForConversation(String conversationId) {
    return _messages.where((message) {
        final conversation = _conversations.firstWhere(
          (conv) => conv.id == conversationId,
          orElse: () => throw Exception('Conversation not found'),
        );

        return (message.senderId == conversation.customerId &&
                message.receiverId == conversation.sellerId) ||
            (message.senderId == conversation.sellerId &&
                message.receiverId == conversation.customerId);
      }).toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  List<Conversation> getConversationsForUser(String userId, UserType userType) {
    return _conversations.where((conversation) {
        if (userType == UserType.customer) {
          return conversation.customerId == userId;
        } else {
          return conversation.sellerId == userId;
        }
      }).toList()
      ..sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));
  }

  Future<void> sendMessage(Message message) async {
    _isLoading = true;
    notifyListeners();

    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 500));

    // Add message to list
    _messages.add(message);

    // Update conversation
    final conversationIndex = _conversations.indexWhere(
      (conv) =>
          (conv.customerId == message.senderId &&
              conv.sellerId == message.receiverId) ||
          (conv.customerId == message.receiverId &&
              conv.sellerId == message.senderId),
    );

    if (conversationIndex != -1) {
      final conversation = _conversations[conversationIndex];
      _conversations[conversationIndex] = Conversation(
        id: conversation.id,
        customerId: conversation.customerId,
        customerName: conversation.customerName,
        customerImage: conversation.customerImage,
        sellerId: conversation.sellerId,
        sellerName: conversation.sellerName,
        sellerImage: conversation.sellerImage,
        lastMessageTime: message.timestamp,
        lastMessageContent: message.content,
        unreadCount:
            message.receiverId == conversation.customerId
                ? conversation.unreadCount + 1
                : conversation.unreadCount + 1,
        productId: conversation.productId,
        orderId: conversation.orderId,
      );
    } else {
      // Create new conversation
      // In a real app, you would need to fetch user details
      _conversations.add(
        Conversation(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          customerId: message.senderId,
          customerName: 'Customer',
          customerImage: 'https://randomuser.me/api/portraits/men/1.jpg',
          sellerId: message.receiverId,
          sellerName: 'Seller',
          sellerImage: 'https://randomuser.me/api/portraits/women/1.jpg',
          lastMessageTime: message.timestamp,
          lastMessageContent: message.content,
          unreadCount: 1,
          productId: message.productId,
          orderId: message.orderId,
        ),
      );
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> markConversationAsRead(
    String conversationId,
    String userId,
  ) async {
    _isLoading = true;
    notifyListeners();

    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 500));

    // Update messages
    for (var i = 0; i < _messages.length; i++) {
      if (_messages[i].receiverId == userId && !_messages[i].isRead) {
        _messages[i] = _messages[i].copyWith(isRead: true);
      }
    }

    // Update conversation
    final conversationIndex = _conversations.indexWhere(
      (conv) => conv.id == conversationId,
    );
    if (conversationIndex != -1) {
      final conversation = _conversations[conversationIndex];
      _conversations[conversationIndex] = conversation.copyWith(unreadCount: 0);
    }

    _isLoading = false;
    notifyListeners();
  }
}
