import*as e from"../../core/i18n/i18n.js";import*as t from"../trace/trace.js";import*as n from"../../core/common/common.js";import*as r from"../../core/sdk/sdk.js";const s={threadS:"Thread {PH1}"},i=e.i18n.registerUIStrings("models/timeline_model/TimelineJSProfile.ts",s),o=e.i18n.getLocalizedString.bind(void 0,i);class l{static isNativeRuntimeFrame(e){return"native V8Runtime"===e.url}static nativeGroup(e){return e.startsWith("Parse")?"Parse":e.startsWith("Compile")||e.startsWith("Recompile")?"Compile":null}static createFakeTraceFromCpuProfile(e,n){const r=[],i=o(s.threadS,{PH1:n});return l("TracingStartedInPage",{data:{sessionId:"1"}},0,0,"M"),l("thread_name",{name:i},0,0,"M","__metadata"),e?(l("J<PERSON>oot",{},e.startTime,e.endTime-e.startTime,"X","toplevel"),l("CpuProfile",{data:{cpuProfile:e}},e.endTime,0,"X"),r):r;function l(e,s,i,o,l,a){const h={cat:a||"disabled-by-default-devtools.timeline",name:e,ph:l||"X",pid:t.Types.TraceEvents.ProcessID(1),tid:n,ts:t.Types.Timing.MicroSeconds(i),args:s};return o&&(h.dur=t.Types.Timing.MicroSeconds(o)),r.push(h),h}}}var a=Object.freeze({__proto__:null,TimelineJSProfileProcessor:l});class h{}class d extends h{visibleTypes;constructor(e){super(),this.visibleTypes=new Set(e)}accept(e){return!(!t.Types.Extensions.isSyntheticExtensionEntry(e)&&!t.Types.TraceEvents.isSyntheticTraceEntry(e))||this.visibleTypes.has(d.eventType(e))}static eventType(e){return t.Helpers.Trace.eventHasCategory(e,"blink.console")?"ConsoleTime":t.Helpers.Trace.eventHasCategory(e,"blink.user_timing")?"UserTiming":e.name}}var c=Object.freeze({__proto__:null,TimelineModelFilter:h,TimelineVisibleEventsFilter:d,TimelineInvisibleEventsFilter:class extends h{#e;constructor(e){super(),this.#e=new Set(e)}accept(e){return!this.#e.has(d.eventType(e))}},ExclusiveNameFilter:class extends h{#t;constructor(e){super(),this.#t=new Set(e)}accept(e){return!this.#t.has(e.name)}}});class u{totalTime;selfTime;id;event;parent;groupId;isGroupNodeInternal;depth;constructor(e,t){this.totalTime=0,this.selfTime=0,this.id=e,this.event=t,this.groupId="",this.isGroupNodeInternal=!1,this.depth=0}isGroupNode(){return this.isGroupNodeInternal}hasChildren(){throw"Not implemented"}setHasChildren(e){throw"Not implemented"}children(){throw"Not implemented"}searchTree(e,t){t=t||[],this.event&&e(this.event)&&t.push(this);for(const n of this.children().values())n.searchTree(e,t);return t}}class p extends u{root;hasChildrenInternal;childrenInternal;parent;constructor(e,t,n){super(e,t),this.root=n&&n.root,this.hasChildrenInternal=!1,this.childrenInternal=null,this.parent=n}hasChildren(){return this.hasChildrenInternal}setHasChildren(e){this.hasChildrenInternal=e}children(){return this.childrenInternal||this.buildChildren()}buildChildren(){const e=[];for(let t=this;t.parent&&!t.isGroupNode();t=t.parent)e.push(t);e.reverse();const n=new Map,r=this,s=this.root;if(!s)return this.childrenInternal=n,this.childrenInternal;const i=s.startTime,o=s.endTime,l=s.doNotAggregate?function(t){++d,c===e.length&&d<=e.length+2&&m(t,0);--d}:void 0,a=s.doNotAggregate?void 0:f,h=s.getEventGroupIdCallback();let d=0,c=0,u=null;function m(t,s){if(d===e.length+2){if(!u)return;return u.setHasChildren(!0),void(u.selfTime-=s)}let i,o="";a?(i=a(t),o=h?h(t):"",o&&(i+="/"+o)):i=Symbol("uniqueId");let l=n.get(i);l||(l=new p(i,t,r),l.groupId=o,n.set(i,l)),l.selfTime+=s,l.totalTime+=s,u=l}return t.Helpers.Trace.forEachEvent(s.events,{onStartEvent:function(n){const{startTime:r,endTime:s}=t.Helpers.Timing.eventTimingsMilliSeconds(n);if(++d,d>e.length+2)return;if(!function(n){const{endTime:r}=t.Helpers.Timing.eventTimingsMilliSeconds(n);if(c===e.length)return!0;if(c!==d-1)return!1;if(!r)return!1;if(!a)return n===e[c].event&&++c,!1;let s=a(n);const i=h?h(n):"";i&&(s+="/"+i);s===e[c].id&&++c;return!1}(n))return;const l=(void 0!==s?Math.min(s,o):o)-Math.max(i,r);l<0&&console.error("Negative event duration");m(n,l)},onEndEvent:function(){--d,c>d&&(c=d)},onInstantEvent:l,startTime:t.Helpers.Timing.millisecondsToMicroseconds(i),endTime:t.Helpers.Timing.millisecondsToMicroseconds(o),eventFilter:s.filter,ignoreAsyncEvents:!1}),this.childrenInternal=n,n}getRoot(){return this.root}}class m extends u{childrenInternal;isGroupNodeInternal;constructor(e,t,n){super(e,n),this.childrenInternal=new Map,this.parent=t,this.isGroupNodeInternal=!0}addChild(e,t,n){this.childrenInternal.set(e.id,e),this.selfTime+=t,this.totalTime+=n,e.parent=this}hasChildren(){return!0}children(){return this.childrenInternal}}class T extends u{parent;root;depth;cachedChildren;hasChildrenInternal;constructor(e,t,n,r,s){super(t,n),this.parent=s,this.root=e,this.depth=(s.depth||0)+1,this.cachedChildren=null,this.hasChildrenInternal=r}hasChildren(){return this.hasChildrenInternal}setHasChildren(e){this.hasChildrenInternal=e}children(){if(this.cachedChildren)return this.cachedChildren;const e=[0],n=[],r=[],s=new Map,i=this.root.startTime,o=this.root.endTime;let l=i;const a=this;return t.Helpers.Trace.forEachEvent(this.root.events,{onStartEvent:function(s){const{startTime:l,endTime:a}=t.Helpers.Timing.eventTimingsMilliSeconds(s),h=(void 0!==a?Math.min(a,o):o)-Math.max(l,i);h<0&&console.assert(!1,"Negative duration of an event");e[e.length-1]-=h,e.push(h);const d=f(s);n.push(d),r.push(s)},onEndEvent:function(i){const{startTime:h,endTime:d}=t.Helpers.Timing.eventTimingsMilliSeconds(i),c=e.pop(),u=n.pop();let p;for(r.pop(),p=a;p.depth>1;p=p.parent)if(p.id!==n[n.length+1-p.depth])return;if(p.id!==u||n.length<a.depth)return;const m=n[n.length-a.depth];if(p=s.get(m),!p){const e=r[r.length-a.depth],t=r.length>a.depth;p=new T(a.root,m,e,t,a),s.set(m,p)}const f=void 0!==d?Math.min(d,o):o,g=f-Math.max(h,l);p.selfTime+=c||0,p.totalTime+=g,l=f},startTime:t.Helpers.Timing.millisecondsToMicroseconds(i),endTime:t.Helpers.Timing.millisecondsToMicroseconds(o),eventFilter:this.root.filter,ignoreAsyncEvents:!1}),this.cachedChildren=this.root.filterChildren(s),this.cachedChildren}searchTree(e,t){return t=t||[],this.event&&e(this.event)&&t.push(this),t}}function f(e){if(t.Types.TraceEvents.isProfileCall(e)){return`f:${l.isNativeRuntimeFrame(e.callFrame)?l.nativeGroup(e.callFrame.functionName):e.callFrame.functionName}@${e.callFrame.scriptId||e.callFrame.url||""}`}return t.Types.TraceEvents.isTraceEventTimeStamp(e)?`${e.name}:${e.args.data.message}`:e.name}var g=Object.freeze({__proto__:null,Node:u,TopDownNode:p,TopDownRootNode:class extends p{filter;events;startTime;endTime;eventGroupIdCallback;doNotAggregate;totalTime;selfTime;constructor(e,t,n,r,s,i){super("",null,null),this.root=this,this.events=e,this.filter=e=>t.every((t=>t.accept(e))),this.startTime=n,this.endTime=r,this.eventGroupIdCallback=i,this.doNotAggregate=s,this.totalTime=r-n,this.selfTime=this.totalTime}children(){return this.childrenInternal||this.grouppedTopNodes()}grouppedTopNodes(){const e=super.children();for(const t of e.values())this.selfTime-=t.totalTime;if(!this.eventGroupIdCallback)return e;const t=new Map;for(const n of e.values()){if(!n.event)continue;const e=this.eventGroupIdCallback(n.event);let r=t.get(e);r||(r=new m(e,this,n.event),t.set(e,r)),r.addChild(n,n.selfTime,n.totalTime)}return this.childrenInternal=t,t}getEventGroupIdCallback(){return this.eventGroupIdCallback}},BottomUpRootNode:class extends u{childrenInternal;events;textFilter;filter;startTime;endTime;eventGroupIdCallback;totalTime;constructor(e,t,n,r,s,i){super("",null),this.childrenInternal=null,this.events=e,this.textFilter=t,this.filter=e=>n.every((t=>t.accept(e))),this.startTime=r,this.endTime=s,this.eventGroupIdCallback=i,this.totalTime=s-r}hasChildren(){return!0}filterChildren(e){for(const[t,n]of e)n.event&&n.depth<=1&&!this.textFilter.accept(n.event)&&e.delete(t);return e}children(){return this.childrenInternal||(this.childrenInternal=this.filterChildren(this.grouppedTopNodes())),this.childrenInternal}ungrouppedTopNodes(){const e=this,n=this.startTime,r=this.endTime,s=new Map,i=[r-n],o=[],l=new Map;t.Helpers.Trace.forEachEvent(this.events,{onStartEvent:function(e){const{startTime:s,endTime:a}=t.Helpers.Timing.eventTimingsMilliSeconds(e),h=(void 0!==a?Math.min(a,r):r)-Math.max(s,n);i[i.length-1]-=h,i.push(h);const d=f(e),c=!l.has(d);c&&l.set(d,h);o.push(c)},onEndEvent:function(t){const n=f(t);let r=s.get(n);r||(r=new T(e,n,t,!1,e),s.set(n,r));r.selfTime+=i.pop()||0,o.pop()&&(r.totalTime+=l.get(n)||0,l.delete(n));o.length&&r.setHasChildren(!0)},startTime:t.Helpers.Timing.millisecondsToMicroseconds(this.startTime),endTime:t.Helpers.Timing.millisecondsToMicroseconds(this.endTime),eventFilter:this.filter,ignoreAsyncEvents:!1}),this.selfTime=i.pop()||0;for(const e of s)e[1].selfTime<=0&&s.delete(e[0]);return s}grouppedTopNodes(){const e=this.ungrouppedTopNodes();if(!this.eventGroupIdCallback)return e;const t=new Map;for(const n of e.values()){if(!n.event)continue;const e=this.eventGroupIdCallback(n.event);let r=t.get(e);r||(r=new m(e,this,n.event),t.set(e,r)),r.addChild(n,n.selfTime,n.selfTime)}return t}},GroupNode:m,BottomUpNode:T,eventStackFrame:function(e){if(t.Types.TraceEvents.isProfileCall(e))return e.callFrame;const n=e.args?.data?.stackTrace?.[0];return n?{...n,scriptId:String(n.scriptId)}:null},generateEventID:f});class I extends r.LayerTreeBase.LayerTreeBase{tileById;paintProfilerModel;constructor(e){super(e),this.tileById=new Map,this.paintProfilerModel=e&&e.model(r.PaintProfiler.PaintProfilerModel)}async setLayers(e,t,n){const r=new Set;if(e)this.extractNodeIdsToResolve(r,{},e);else if(t)for(let e=0;e<t.length;++e)this.extractNodeIdsToResolve(r,{},t[e]);await this.resolveBackendNodeIds(r);const s=this.layersById;if(this.layersById=new Map,this.setContentRoot(null),e){const t=this.innerSetLayers(s,e);this.setRoot(t)}else if(t){const e=t.map(this.innerSetLayers.bind(this,s)),n=this.contentRoot();if(!n)throw new Error("Content root is not set.");this.setRoot(n);for(let t=0;t<e.length;++t)e[t].id()!==n.id()&&n.addChild(e[t])}this.setPaints(n)}setTiles(e){this.tileById=new Map;for(const t of e)this.tileById.set(t.id,t)}pictureForRasterTile(e){const t=this.tileById.get("cc::Tile/"+e);if(!t)return n.Console.Console.instance().error(`Tile ${e} is missing`),Promise.resolve(null);const r=this.layerById(t.layer_id);return r?r.pictureForRect(t.content_rect):(n.Console.Console.instance().error(`Layer ${t.layer_id} for tile ${e} is not found`),Promise.resolve(null))}setPaints(e){for(let t=0;t<e.length;++t){const n=this.layersById.get(e[t].layerId());n&&n.addPaintEvent(e[t])}}innerSetLayers(e,t){let n=e.get(t.layer_id);n?n.reset(t):n=new v(this.paintProfilerModel,t),this.layersById.set(t.layer_id,n),t.owner_node&&n.setNode(this.backendNodeIdToNode().get(t.owner_node)||null),!this.contentRoot()&&n.drawsContent()&&this.setContentRoot(n);for(let r=0;t.children&&r<t.children.length;++r)n.addChild(this.innerSetLayers(e,t.children[r]));return n}extractNodeIdsToResolve(e,t,n){const r=n.owner_node;r&&!this.backendNodeIdToNode().has(r)&&e.add(r);for(let r=0;n.children&&r<n.children.length;++r)this.extractNodeIdsToResolve(e,t,n.children[r])}}class v{parentLayerId;parentInternal;layerId;nodeInternal;offsetXInternal;offsetYInternal;widthInternal;heightInternal;childrenInternal;quadInternal;scrollRectsInternal;gpuMemoryUsageInternal;paints;compositingReasons;compositingReasonIds;drawsContentInternal;paintProfilerModel;constructor(e,t){this.parentLayerId=null,this.parentInternal=null,this.layerId="",this.nodeInternal=null,this.offsetXInternal=-1,this.offsetYInternal=-1,this.widthInternal=-1,this.heightInternal=-1,this.childrenInternal=[],this.quadInternal=[],this.scrollRectsInternal=[],this.gpuMemoryUsageInternal=-1,this.paints=[],this.compositingReasons=[],this.compositingReasonIds=[],this.drawsContentInternal=!1,this.paintProfilerModel=e,this.reset(t)}reset(e){this.nodeInternal=null,this.layerId=String(e.layer_id),this.offsetXInternal=e.position[0],this.offsetYInternal=e.position[1],this.widthInternal=e.bounds.width,this.heightInternal=e.bounds.height,this.childrenInternal=[],this.parentLayerId=null,this.parentInternal=null,this.quadInternal=e.layer_quad||[],this.createScrollRects(e),this.compositingReasons=e.compositing_reasons||[],this.compositingReasonIds=e.compositing_reason_ids||[],this.drawsContentInternal=Boolean(e.draws_content),this.gpuMemoryUsageInternal=e.gpu_memory_usage,this.paints=[]}id(){return this.layerId}parentId(){return this.parentLayerId}parent(){return this.parentInternal}isRoot(){return!this.parentId()}children(){return this.childrenInternal}addChild(e){const t=e;t.parentInternal&&console.assert(!1,"Child already has a parent"),this.childrenInternal.push(t),t.parentInternal=this,t.parentLayerId=this.layerId}setNode(e){this.nodeInternal=e}node(){return this.nodeInternal}nodeForSelfOrAncestor(){let e=this;for(;e;e=e.parent())if(e.node())return e.node();return null}offsetX(){return this.offsetXInternal}offsetY(){return this.offsetYInternal}width(){return this.widthInternal}height(){return this.heightInternal}transform(){return null}quad(){return this.quadInternal}anchorPoint(){return[.5,.5,0]}invisible(){return!1}paintCount(){return 0}lastPaintRect(){return null}scrollRects(){return this.scrollRectsInternal}stickyPositionConstraint(){return null}gpuMemoryUsage(){return this.gpuMemoryUsageInternal}snapshots(){return this.paints.map((async e=>{if(!this.paintProfilerModel)return null;const t=await async function(e,t){const n=t.picture();if(!n||!e)return null;const r=await e.loadSnapshot(n.serializedPicture);return r?{rect:n.rect,snapshot:r}:null}(this.paintProfilerModel,e);if(!t)return null;return{rect:{x:t.rect[0],y:t.rect[1],width:t.rect[2],height:t.rect[3]},snapshot:t.snapshot}}))}async pictureForRect(e){return Promise.all(this.paints.map((e=>e.picture()))).then((n=>{const r=n.filter((n=>{return n&&(r=n.rect,s=e,t(r[0],r[0]+r[2],s[0],s[0]+s[2])&&t(r[1],r[1]+r[3],s[1],s[1]+s[3]));var r,s})).map((e=>({x:e.rect[0],y:e.rect[1],picture:e.serializedPicture})));if(!r.length||!this.paintProfilerModel)return null;const s=r.reduce(((e,t)=>Math.min(e,t.x)),1/0),i=r.reduce(((e,t)=>Math.min(e,t.y)),1/0),o={x:e[0]-s,y:e[1]-i,width:e[2],height:e[3]};return this.paintProfilerModel.loadSnapshotFromFragments(r).then((e=>e?{rect:o,snapshot:e}:null))}));function t(e,t,n,r){return console.assert(e<=t&&n<=r,"segments should be specified as ordered pairs"),t>n&&e<r}}scrollRectsFromParams(e,t){return{rect:{x:e[0],y:e[1],width:e[2],height:e[3]},type:t}}createScrollRects(e){const t=[];e.non_fast_scrollable_region&&t.push(this.scrollRectsFromParams(e.non_fast_scrollable_region,"NonFastScrollable")),e.touch_event_handler_region&&t.push(this.scrollRectsFromParams(e.touch_event_handler_region,"TouchEventHandler")),e.wheel_event_handler_region&&t.push(this.scrollRectsFromParams(e.wheel_event_handler_region,"WheelEventHandler")),e.scroll_event_handler_region&&t.push(this.scrollRectsFromParams(e.scroll_event_handler_region,"RepaintsOnScroll")),this.scrollRectsInternal=t}addPaintEvent(e){this.paints.push(e)}requestCompositingReasons(){return Promise.resolve(this.compositingReasons)}requestCompositingReasonIds(){return Promise.resolve(this.compositingReasonIds)}drawsContent(){return this.drawsContentInternal}}var y=Object.freeze({__proto__:null,TracingLayerTree:I,TracingFrameLayerTree:class{#n;#r;#s=[];constructor(e,t){this.#n=e,this.#r=t.entry,this.#s=t.paints}async layerTreePromise(){const e=this.#r.args.snapshot,t=e.device_viewport_size,n=e.active_tiles,r=e.active_tree.root_layer,s=e.active_tree.layers,i=new I(this.#n);return i.setViewportSize(t),i.setTiles(n),await i.setLayers(r,s,this.#s||[]),i}paints(){return this.#s}},TracingLayer:v});export{a as TimelineJSProfile,c as TimelineModelFilter,g as TimelineProfileTree,y as TracingLayerTree};
