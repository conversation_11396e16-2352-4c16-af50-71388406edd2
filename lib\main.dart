import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'app.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Supabase.initialize(
    url: 'https://vwryhuiffrwpplvylbfb.supabase.co',
    anon<PERSON>ey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cnlodWlmZnJ3cHBsdnlsYmZiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjEyNjAsImV4cCI6MjA2MTkzNzI2MH0.7q_xO8JAUDNYUF7pHtDm09PJ8xroniSKpEgns4ujJSE',
  );

  runApp(const HadiHiaApp());
}
