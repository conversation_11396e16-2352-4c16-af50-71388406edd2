import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/auth_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/wallet_card.dart';
import '../../widgets/welcome_notification.dart';
import '../auth/user_type_selection.dart';

class AccountPage extends StatefulWidget {
  const AccountPage({super.key});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  bool _showWelcomeNotification = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('My Account'), centerTitle: true),
      body: Consumer<AuthService>(
        builder: (context, authService, _) {
          if (authService.currentUser == null) {
            return const Center(
              child: Text('Please log in to view your account'),
            );
          }

          final user = authService.currentUser!;

          return Stack(
            children: [
              SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Header
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 40,
                          backgroundImage: NetworkImage(user.profileImage),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(user.name, style: AppTextStyles.heading2),
                              const SizedBox(height: 4),
                              Text(
                                user.email,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withAlpha(
                                    25,
                                  ), // 0.1 opacity
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'Customer',
                                  style: TextStyle(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () {},
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Wallet
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: WalletCard(
                        balance: user.walletBalance,
                        primaryColor: AppColors.primary,
                        isCustomer: true,
                        onWithdraw: () {},
                        onTransactions: () {},
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Account Settings
                    _buildSectionCard(
                      title: 'Account Settings',
                      child: Column(
                        children: [
                          _buildSettingsItem(
                            icon: Icons.person,
                            title: 'Personal Information',
                            onTap: () {},
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.location_on,
                            title: 'Addresses',
                            onTap: () {},
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.payment,
                            title: 'Payment Methods',
                            onTap: () {},
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.notifications,
                            title: 'Notifications',
                            onTap: () {},
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Support
                    _buildSectionCard(
                      title: 'Support',
                      child: Column(
                        children: [
                          _buildSettingsItem(
                            icon: Icons.help,
                            title: 'Help Center',
                            onTap: () {},
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.chat,
                            title: 'Contact Support',
                            onTap: () {},
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.info,
                            title: 'About HadiHia',
                            onTap: () {},
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Logout Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _handleLogout(context, authService),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppColors.error,
                          side: const BorderSide(color: AppColors.error),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text('Logout'),
                      ),
                    ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),

              // Welcome notification
              if (_showWelcomeNotification)
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: WelcomeNotification(
                    message: 'Welcome back to HadiHia!',
                    backgroundColor: AppColors.primary,
                    onDismiss: () {
                      setState(() {
                        _showWelcomeNotification = false;
                      });
                    },
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionCard({required String title, required Widget child}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: AppTextStyles.heading3),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, color: AppColors.primary, size: 20),
            const SizedBox(width: 16),
            Expanded(child: Text(title, style: AppTextStyles.bodyLarge)),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textSecondary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleLogout(
    BuildContext context,
    AuthService authService,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Logout'),
            content: const Text('Are you sure you want to logout?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Logout'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      if (!context.mounted) return;

      await authService.logout();

      if (!context.mounted) return;

      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const UserTypeSelectionPage()),
        (route) => false,
      );
    }
  }
}
