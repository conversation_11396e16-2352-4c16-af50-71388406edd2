import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../widgets/custom_image_slider.dart';
import '../../widgets/enhanced_image_slider.dart';

class ImageSliderExample extends StatelessWidget {
  const ImageSliderExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sample image URLs for demonstration
    final List<String> imageUrls = [
      'https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
      'https://images.unsplash.com/photo-1607083206968-13611e3d76db?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
      'https://images.unsplash.com/photo-1590736969955-71cc94c4628b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
      'https://images.unsplash.com/photo-1516483638261-f4dbaf036963?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
      'https://images.unsplash.com/photo-1563237023-b1e970526dcb?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
    ];

    // Sample slider items for enhanced slider
    final List<SliderItem> sliderItems = [
      SliderItem(
        imageUrl: 'https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
        title: 'Summer Sale',
        subtitle: 'Up to 50% off on all products',
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Summer Sale tapped')),
          );
        },
      ),
      SliderItem(
        imageUrl: 'https://images.unsplash.com/photo-1607083206968-13611e3d76db?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
        title: 'New Arrivals',
        subtitle: 'Check out our latest products',
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('New Arrivals tapped')),
          );
        },
      ),
      SliderItem(
        imageUrl: 'https://images.unsplash.com/photo-1590736969955-71cc94c4628b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
        title: 'Handcrafted',
        subtitle: 'Authentic Moroccan crafts',
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Handcrafted tapped')),
          );
        },
      ),
      SliderItem(
        imageUrl: 'https://images.unsplash.com/photo-1516483638261-f4dbaf036963?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
        title: 'Moroccan Experience',
        subtitle: 'Explore the beauty of Morocco',
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Moroccan Experience tapped')),
          );
        },
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Image Slider Examples'),
        backgroundColor: AppColors.primary,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Image Slider
              const Text(
                'Basic Image Slider',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              CustomImageSlider(
                imageUrls: imageUrls,
                height: 200,
                borderRadius: 12,
                indicatorActiveColor: AppColors.primary,
                indicatorInactiveColor: Colors.grey.withAlpha(76),
              ),
              
              const SizedBox(height: 32),
              
              // Enhanced Image Slider
              const Text(
                'Enhanced Image Slider',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              EnhancedImageSlider(
                items: sliderItems,
                height: 220,
                borderRadius: 16,
                indicatorActiveColor: AppColors.primary,
                indicatorInactiveColor: Colors.grey.withAlpha(76),
              ),
              
              const SizedBox(height: 32),
              
              // Usage Instructions
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'How to Use',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '1. Import the slider widget:\n'
                        '   import \'../../widgets/enhanced_image_slider.dart\';\n\n'
                        '2. Create a list of SliderItem objects with your content\n\n'
                        '3. Add the EnhancedImageSlider to your UI:\n'
                        '   EnhancedImageSlider(\n'
                        '     items: yourSliderItems,\n'
                        '     height: 200,\n'
                        '     borderRadius: 16,\n'
                        '   )',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
