import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as s from"../../core/sdk/sdk.js";import*as r from"../bindings/bindings.js";import*as n from"../text_utils/text_utils.js";import*as i from"../workspace/workspace.js";import*as o from"../../core/platform/platform.js";import*as a from"../../core/i18n/i18n.js";import*as d from"../../ui/legacy/components/utils/utils.js";import*as l from"../breakpoints/breakpoints.js";import*as c from"../../ui/components/icon_button/icon_button.js";import*as h from"../../ui/legacy/legacy.js";import*as u from"../../core/root/root.js";import*as p from"../../ui/visual_logging/visual_logging.js";const m={unableToReadFilesWithThis:"`PlatformFileSystem` cannot read files."},g=a.i18n.registerUIStrings("models/persistence/PlatformFileSystem.ts",m),f=a.i18n.getLocalizedString.bind(void 0,g);class S{pathInternal;typeInternal;constructor(e,t){this.pathInternal=e,this.typeInternal=t}getMetadata(e){return Promise.resolve(null)}initialFilePaths(){return[]}initialGitFolders(){return[]}path(){return this.pathInternal}embedderPath(){throw new Error("Not implemented")}type(){return this.typeInternal}async createFile(e,t){return Promise.resolve(null)}deleteFile(e){return Promise.resolve(!1)}deleteDirectoryRecursively(e){return Promise.resolve(!1)}requestFileBlob(e){return Promise.resolve(null)}async requestFileContent(e){return{error:f(m.unableToReadFilesWithThis)}}setFileContent(e,t,s){throw new Error("Not implemented")}renameFile(e,t,s){s(!1)}addExcludedFolder(e){}removeExcludedFolder(e){}fileSystemRemoved(){}isFileExcluded(e){return!1}excludedFolders(){return new Set}searchInPath(e,t){return Promise.resolve([])}indexContent(e){queueMicrotask((()=>{e.done()}))}mimeFromPath(e){throw new Error("Not implemented")}canExcludeFolder(e){return!1}contentType(e){throw new Error("Not implemented")}tooltipForURL(e){throw new Error("Not implemented")}supportsAutomapping(){throw new Error("Not implemented")}}var y=Object.freeze({__proto__:null,PlatformFileSystem:S});const P={fileSystemErrorS:"File system error: {PH1}",blobCouldNotBeLoaded:"Blob could not be loaded.",cantReadFileSS:"Can't read file: {PH1}: {PH2}",linkedToS:"Linked to {PH1}"},v=a.i18n.registerUIStrings("models/persistence/IsolatedFileSystem.ts",P),F=a.i18n.getLocalizedString.bind(void 0,v);class w extends S{manager;embedderPathInternal;domFileSystem;excludedFoldersSetting;excludedFoldersInternal;excludedEmbedderFolders=[];initialFilePathsInternal=new Set;initialGitFoldersInternal=new Set;fileLocks=new Map;constructor(t,s,r,n,i){super(s,i),this.manager=t,this.embedderPathInternal=r,this.domFileSystem=n,this.excludedFoldersSetting=e.Settings.Settings.instance().createLocalSetting("workspace-excluded-folders",{}),this.excludedFoldersInternal=new Set(this.excludedFoldersSetting.get()[s]||[])}static async create(e,s,r,n,i,o){const a=t.InspectorFrontendHost.InspectorFrontendHostInstance.isolatedFileSystem(i,o);if(!a)return null;const d=new w(e,s,r,a,n);return d.initializeFilePaths().then((()=>d)).catch((e=>(console.error(e),null)))}static errorMessage(e){return F(P.fileSystemErrorS,{PH1:e.message})}serializedFileOperation(e,t){const s=Promise.resolve(this.fileLocks.get(e)).then((()=>t.call(null)));return this.fileLocks.set(e,s),s}getMetadata(t){const{promise:s,resolve:r}=o.PromiseUtilities.promiseWithResolvers();return this.domFileSystem.root.getFile(e.ParsedURL.ParsedURL.encodedPathToRawPathString(t),void 0,(function(e){e.getMetadata(r,n)}),n),s;function n(e){const s=w.errorMessage(e);console.error(s+" when getting file metadata '"+t),r(null)}}initialFilePaths(){return[...this.initialFilePathsInternal]}initialGitFolders(){return[...this.initialGitFoldersInternal]}embedderPath(){return this.embedderPathInternal}initializeFilePaths(){return new Promise((s=>{let r=1;const n=function(i){for(let s=0;s<i.length;++s){const o=i[s];if(o.isDirectory){if(o.fullPath.endsWith("/.git")){const t=o.fullPath.lastIndexOf("/"),s=e.ParsedURL.ParsedURL.substr(o.fullPath,1,t);this.initialGitFoldersInternal.add(e.ParsedURL.ParsedURL.rawPathToEncodedPathString(s))}if(this.isFileExcluded(e.ParsedURL.ParsedURL.concatenate(e.ParsedURL.ParsedURL.rawPathToEncodedPathString(o.fullPath),"/"))){const s=e.ParsedURL.ParsedURL.concatenate(this.path(),e.ParsedURL.ParsedURL.rawPathToEncodedPathString(o.fullPath));this.excludedEmbedderFolders.push(e.ParsedURL.ParsedURL.urlToRawPathString(s,t.Platform.isWin()));continue}++r,this.requestEntries(o.fullPath,n)}else{if(this.isFileExcluded(e.ParsedURL.ParsedURL.rawPathToEncodedPathString(o.fullPath)))continue;this.initialFilePathsInternal.add(e.ParsedURL.ParsedURL.rawPathToEncodedPathString(e.ParsedURL.ParsedURL.substr(o.fullPath,1)))}}0==--r&&s()}.bind(this);this.requestEntries(o.DevToolsPath.EmptyRawPathString,n)}))}async createFoldersIfNotExist(e){let t=await new Promise((t=>this.domFileSystem.root.getDirectory(e,void 0,t,(()=>t(null)))));if(t)return t;const s=e.split("/");let r="";for(const e of s)if(r=r+"/"+e,t=await this.innerCreateFolderIfNeeded(r),!t)return null;return t}innerCreateFolderIfNeeded(e){return new Promise((t=>{this.domFileSystem.root.getDirectory(e,{create:!0},(e=>t(e)),(s=>{const r=w.errorMessage(s);console.error(r+" trying to create directory '"+e+"'"),t(null)}))}))}async createFile(t,s){const r=await this.createFoldersIfNotExist(e.ParsedURL.ParsedURL.encodedPathToRawPathString(t));if(!r)return null;const n=await this.serializedFileOperation(t,function s(n,i){return new Promise((o=>{const a=e.ParsedURL.ParsedURL.concatenate(n,(i||"").toString());r.getFile(a,{create:!0,exclusive:!0},o,(e=>{if("InvalidModificationError"===e.name)return void o(s.call(this,n,i?i+1:1));const r=w.errorMessage(e);console.error(r+" when testing if file exists '"+this.path()+"/"+t+"/"+a+"'"),o(null)}))}))}.bind(this,s||"NewFile"));return n?e.ParsedURL.ParsedURL.rawPathToEncodedPathString(e.ParsedURL.ParsedURL.substr(n.fullPath,1)):null}deleteFile(t){const{promise:s,resolve:r}=o.PromiseUtilities.promiseWithResolvers();return this.domFileSystem.root.getFile(e.ParsedURL.ParsedURL.encodedPathToRawPathString(t),void 0,function(e){e.remove(n,i.bind(this))}.bind(this),i.bind(this)),s;function n(){r(!0)}function i(e){const s=w.errorMessage(e);console.error(s+" when deleting file '"+this.path()+"/"+t+"'"),r(!1)}}deleteDirectoryRecursively(t){const{promise:s,resolve:r}=o.PromiseUtilities.promiseWithResolvers();return this.domFileSystem.root.getDirectory(e.ParsedURL.ParsedURL.encodedPathToRawPathString(t),void 0,function(e){e.removeRecursively(n,i.bind(this))}.bind(this),i.bind(this)),s;function n(){r(!0)}function i(e){const s=w.errorMessage(e);console.error(s+" when deleting directory '"+this.path()+"/"+t+"'"),r(!1)}}requestFileBlob(t){return new Promise((s=>{function r(e){if("NotFoundError"===e.name)return void s(null);const r=w.errorMessage(e);console.error(r+" when getting content for file '"+this.path()+"/"+t+"'"),s(null)}this.domFileSystem.root.getFile(e.ParsedURL.ParsedURL.encodedPathToRawPathString(t),void 0,(e=>{e.file(s,r.bind(this))}),r.bind(this))}))}requestFileContent(e){return this.serializedFileOperation(e,(()=>this.innerRequestFileContent(e)))}async innerRequestFileContent(t){const s=await this.requestFileBlob(t);if(!s)return{error:F(P.blobCouldNotBeLoaded)};const r=function(t,s){if(s.type)return s.type;const r=e.ParsedURL.ParsedURL.extractExtension(t),n=e.ResourceType.ResourceType.mimeFromExtension(r);if(n)return n;return R.has(r)?"application/octet-stream":"text/plain"}(t,s);try{return o.MimeType.isTextType(r)?new n.ContentData.ContentData(await s.text(),!1,r):new n.ContentData.ContentData(await e.Base64.encode(s),!0,r)}catch(e){return{error:F(P.cantReadFileSS,{PH1:t,PH2:e.message})}}}async setFileContent(s,r,n){let i;t.userMetrics.actionTaken(t.UserMetrics.Action.FileSavedInWorkspace);function o(e){e.createWriter(a.bind(this),d.bind(this))}async function a(e){let t;e.onerror=d.bind(this),e.onwriteend=function(){e.onwriteend=i,e.truncate(t.size)},t=n?await(await fetch(`data:application/octet-stream;base64,${r}`)).blob():new Blob([r],{type:"text/plain"}),e.write(t)}function d(e){const t=w.errorMessage(e);console.error(t+" when setting content for file '"+this.path()+"/"+s+"'"),i(void 0)}this.serializedFileOperation(s,(()=>{const t=new Promise((e=>{i=e}));return this.domFileSystem.root.getFile(e.ParsedURL.ParsedURL.encodedPathToRawPathString(s),{create:!0},o.bind(this),d.bind(this)),t}))}renameFile(t,s,r){if(!(s=s?e.ParsedURL.ParsedURL.trim(s):s)||-1!==s.indexOf("/"))return void r(!1);let n,i;function o(e){i=e,i.getFile(s,void 0,a,d.bind(this))}function a(e){r(!1)}function d(e){"NotFoundError"===e.name?n.moveTo(i,s,l,c.bind(this)):r(!1)}function l(e){r(!0,e.name)}function c(e){const n=w.errorMessage(e);console.error(n+" when renaming file '"+this.path()+"/"+t+"' to '"+s+"'"),r(!1)}this.domFileSystem.root.getFile(e.ParsedURL.ParsedURL.encodedPathToRawPathString(t),void 0,function(e){if(e.name===s)return void r(!1);n=e,n.getParent(o.bind(this),c.bind(this))}.bind(this),c.bind(this))}readDirectory(e,t){const s=e.createReader();let r=[];function n(s){const r=w.errorMessage(s);console.error(r+" when reading directory '"+e.fullPath+"'"),t([])}s.readEntries((function e(i){var o;i.length?(r=r.concat((o=i,Array.prototype.slice.call(o||[],0))),s.readEntries(e,n)):t(r.sort())}),n)}requestEntries(e,t){this.domFileSystem.root.getDirectory(e,void 0,function(e){this.readDirectory(e,t)}.bind(this),(function(s){const r=w.errorMessage(s);console.error(r+" when requesting entry '"+e+"'"),t([])}))}saveExcludedFolders(){const e=this.excludedFoldersSetting.get();e[this.path()]=[...this.excludedFoldersInternal],this.excludedFoldersSetting.set(e)}addExcludedFolder(e){this.excludedFoldersInternal.add(e),this.saveExcludedFolders(),this.manager.dispatchEventToListeners(j.ExcludedFolderAdded,e)}removeExcludedFolder(e){this.excludedFoldersInternal.delete(e),this.saveExcludedFolders(),this.manager.dispatchEventToListeners(j.ExcludedFolderRemoved,e)}fileSystemRemoved(){const e=this.excludedFoldersSetting.get();delete e[this.path()],this.excludedFoldersSetting.set(e)}isFileExcluded(t){if(this.excludedFoldersInternal.has(t))return!0;const s=this.manager.workspaceFolderExcludePatternSetting().asRegExp();return Boolean(s&&s.test(e.ParsedURL.ParsedURL.encodedPathToRawPathString(t)))}excludedFolders(){return this.excludedFoldersInternal}searchInPath(s,r){return new Promise((n=>{const i=this.manager.registerCallback((function(t){n(t.map((t=>e.ParsedURL.ParsedURL.rawPathToUrlString(t)))),r.incrementWorked(1)}));t.InspectorFrontendHost.InspectorFrontendHostInstance.searchInPath(i,this.embedderPathInternal,s)}))}indexContent(e){e.setTotalWork(1);const s=this.manager.registerProgress(e);t.InspectorFrontendHost.InspectorFrontendHostInstance.indexPath(s,this.embedderPathInternal,JSON.stringify(this.excludedEmbedderFolders))}mimeFromPath(t){return e.ResourceType.ResourceType.mimeFromURL(t)||"text/plain"}canExcludeFolder(e){return Boolean(e)&&"overrides"!==this.type()}contentType(t){const s=e.ParsedURL.ParsedURL.extractExtension(t);return C.has(s)?e.ResourceType.resourceTypes.Stylesheet:U.has(s)?e.ResourceType.resourceTypes.Document:k.has(s)?e.ResourceType.resourceTypes.Image:I.has(s)?e.ResourceType.resourceTypes.Script:R.has(s)?e.ResourceType.resourceTypes.Other:e.ResourceType.resourceTypes.Document}tooltipForURL(s){const r=o.StringUtilities.trimMiddle(e.ParsedURL.ParsedURL.urlToRawPathString(s,t.Platform.isWin()),150);return F(P.linkedToS,{PH1:r})}supportsAutomapping(){return"overrides"!==this.type()}}const C=new Set(["css","scss","sass","less"]),U=new Set(["htm","html","asp","aspx","phtml","jsp"]),I=new Set(["asp","aspx","c","cc","cljs","coffee","cpp","cs","dart","java","js","jsp","jsx","h","m","mjs","mm","py","sh","ts","tsx","ls"]),k=new Set(["jpeg","jpg","svg","gif","webp","png","ico","tiff","tif","bmp"]),R=new Set(["cmd","com","exe","a","ar","iso","tar","bz2","gz","lz","lzma","z","7z","apk","arc","cab","dmg","jar","pak","rar","zip","3gp","aac","aiff","flac","m4a","mmf","mp3","ogg","oga","raw","sln","wav","wma","webm","mkv","flv","vob","ogv","gifv","avi","mov","qt","mp4","m4p","m4v","mpg","mpeg","jpeg","jpg","gif","webp","png","ico","tiff","tif","bmp"]);var x=Object.freeze({__proto__:null,IsolatedFileSystem:w,BinaryExtensions:R});const L={unableToAddFilesystemS:"Unable to add filesystem: {PH1}"},b=a.i18n.registerUIStrings("models/persistence/IsolatedFileSystemManager.ts",L),T=a.i18n.getLocalizedString.bind(void 0,b);let E;class M extends e.ObjectWrapper.ObjectWrapper{fileSystemsInternal;callbacks;progresses;workspaceFolderExcludePatternSettingInternal;fileSystemRequestResolve;fileSystemsLoadedPromise;constructor(){super(),this.fileSystemsInternal=new Map,this.callbacks=new Map,this.progresses=new Map,t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.FileSystemRemoved,this.onFileSystemRemoved,this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.FileSystemAdded,(e=>{this.onFileSystemAdded(e)}),this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.FileSystemFilesChangedAddedRemoved,this.onFileSystemFilesChanged,this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.IndexingTotalWorkCalculated,this.onIndexingTotalWorkCalculated,this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.IndexingWorked,this.onIndexingWorked,this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.IndexingDone,this.onIndexingDone,this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.SearchCompleted,this.onSearchCompleted,this);const s=["/Thumbs.db$","/ehthumbs.db$","/Desktop.ini$","/\\$RECYCLE.BIN/"],r=["/\\.DS_Store$","/\\.Trashes$","/\\.Spotlight-V100$","/\\.AppleDouble$","/\\.LSOverride$","/Icon$","/\\._.*$"],n=["/.*~$"];let i=["/node_modules/","/bower_components/","/\\.devtools","/\\.git/","/\\.sass-cache/","/\\.hg/","/\\.idea/","/\\.svn/","/\\.cache/","/\\.project/"];i=t.Platform.isWin()?i.concat(s):t.Platform.isMac()?i.concat(r):i.concat(n);const o=i.join("|");this.workspaceFolderExcludePatternSettingInternal=e.Settings.Settings.instance().createRegExpSetting("workspace-folder-exclude-pattern",o,t.Platform.isWin()?"i":""),this.fileSystemRequestResolve=null,this.fileSystemsLoadedPromise=this.requestFileSystems()}static instance(e={forceNew:null}){const{forceNew:t}=e;return E&&!t||(E=new M),E}static removeInstance(){E=null}requestFileSystems(){let e;const s=new Promise((t=>{e=t}));return t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.FileSystemsLoaded,(function(e){const t=e.data,s=[];for(let e=0;e<t.length;++e)s.push(this.innerAddFileSystem(t[e],!1));Promise.all(s).then(r)}),this),t.InspectorFrontendHost.InspectorFrontendHostInstance.requestFileSystems(),s;function r(t){e(t.filter((e=>Boolean(e))))}}addFileSystem(e){return t.userMetrics.actionTaken("overrides"===e?t.UserMetrics.Action.OverrideTabAddFolder:t.UserMetrics.Action.WorkspaceTabAddFolder),new Promise((s=>{this.fileSystemRequestResolve=s,t.InspectorFrontendHost.InspectorFrontendHostInstance.addFileSystem(e||"")}))}removeFileSystem(e){t.userMetrics.actionTaken("overrides"===e.type()?t.UserMetrics.Action.OverrideTabRemoveFolder:t.UserMetrics.Action.WorkspaceTabRemoveFolder),t.InspectorFrontendHost.InspectorFrontendHostInstance.removeFileSystem(e.embedderPath())}waitForFileSystems(){return this.fileSystemsLoadedPromise}innerAddFileSystem(t,s){const r=t.fileSystemPath,n=e.ParsedURL.ParsedURL.rawPathToUrlString(t.fileSystemPath);return w.create(this,n,r,t.type,t.fileSystemName,t.rootURL).then(function(e){if(!e)return null;this.fileSystemsInternal.set(n,e),s&&this.dispatchEventToListeners(j.FileSystemAdded,e);return e}.bind(this))}addPlatformFileSystem(e,t){this.fileSystemsInternal.set(e,t),this.dispatchEventToListeners(j.FileSystemAdded,t)}onFileSystemAdded(t){const{errorMessage:s,fileSystem:r}=t.data;if(s){if("<selection cancelled>"!==s&&e.Console.Console.instance().error(T(L.unableToAddFilesystemS,{PH1:s})),!this.fileSystemRequestResolve)return;this.fileSystemRequestResolve.call(null,null),this.fileSystemRequestResolve=null}else r&&this.innerAddFileSystem(r,!0).then((e=>{this.fileSystemRequestResolve&&(this.fileSystemRequestResolve.call(null,e),this.fileSystemRequestResolve=null)}))}onFileSystemRemoved(t){const s=t.data,r=e.ParsedURL.ParsedURL.rawPathToUrlString(s),n=this.fileSystemsInternal.get(r);n&&(this.fileSystemsInternal.delete(r),n.fileSystemRemoved(),this.dispatchEventToListeners(j.FileSystemRemoved,n))}onFileSystemFilesChanged(t){const s={changed:r.call(this,t.data.changed),added:r.call(this,t.data.added),removed:r.call(this,t.data.removed)};function r(t){const s=new o.MapUtilities.Multimap;for(const r of t){const t=e.ParsedURL.ParsedURL.rawPathToUrlString(r);for(const n of this.fileSystemsInternal.keys()){const i=this.fileSystemsInternal.get(n);if(i&&i.isFileExcluded(e.ParsedURL.ParsedURL.rawPathToEncodedPathString(r)))continue;const o=n.endsWith("/")?n:n+"/";t.startsWith(o)&&s.set(n,t)}}return s}this.dispatchEventToListeners(j.FileSystemFilesChanged,s)}fileSystems(){return[...this.fileSystemsInternal.values()]}fileSystem(e){return this.fileSystemsInternal.get(e)||null}workspaceFolderExcludePatternSetting(){return this.workspaceFolderExcludePatternSettingInternal}registerCallback(e){const t=++W;return this.callbacks.set(t,e),t}registerProgress(e){const t=++W;return this.progresses.set(t,e),t}onIndexingTotalWorkCalculated(e){const{requestId:t,totalWork:s}=e.data,r=this.progresses.get(t);r&&r.setTotalWork(s)}onIndexingWorked(e){const{requestId:s,worked:r}=e.data,n=this.progresses.get(s);n&&(n.incrementWorked(r),n.isCanceled()&&(t.InspectorFrontendHost.InspectorFrontendHostInstance.stopIndexing(s),this.onIndexingDone(e)))}onIndexingDone(e){const{requestId:t}=e.data,s=this.progresses.get(t);s&&(s.done(),this.progresses.delete(t))}onSearchCompleted(e){const{requestId:t,files:s}=e.data,r=this.callbacks.get(t);r&&(r.call(null,s),this.callbacks.delete(t))}}var j;!function(e){e.FileSystemAdded="FileSystemAdded",e.FileSystemRemoved="FileSystemRemoved",e.FileSystemFilesChanged="FileSystemFilesChanged",e.ExcludedFolderAdded="ExcludedFolderAdded",e.ExcludedFolderRemoved="ExcludedFolderRemoved"}(j||(j={}));let W=0;var A=Object.freeze({__proto__:null,IsolatedFileSystemManager:M,get Events(){return j}});class O{isolatedFileSystemManager;workspace;eventListeners;boundFileSystems;constructor(e,t){this.isolatedFileSystemManager=e,this.workspace=t,this.eventListeners=[this.isolatedFileSystemManager.addEventListener(j.FileSystemAdded,this.onFileSystemAdded,this),this.isolatedFileSystemManager.addEventListener(j.FileSystemRemoved,this.onFileSystemRemoved,this),this.isolatedFileSystemManager.addEventListener(j.FileSystemFilesChanged,this.fileSystemFilesChanged,this)],this.boundFileSystems=new Map,this.isolatedFileSystemManager.waitForFileSystems().then(this.onFileSystemsLoaded.bind(this))}static projectId(e){return e}static relativePath(t){const s=t.project().fileSystemBaseURL;return e.ParsedURL.ParsedURL.split(e.ParsedURL.ParsedURL.sliceUrlToEncodedPathString(t.url(),s.length),"/")}static tooltipForUISourceCode(e){return e.project().fileSystemInternal.tooltipForURL(e.url())}static fileSystemType(e){return e.fileSystemInternal.type()}static fileSystemSupportsAutomapping(e){return e.fileSystemInternal.supportsAutomapping()}static completeURL(t,s){const r=t;return e.ParsedURL.ParsedURL.concatenate(r.fileSystemBaseURL,s)}static fileSystemPath(e){return e}fileSystemManager(){return this.isolatedFileSystemManager}onFileSystemsLoaded(e){for(const t of e)this.addFileSystem(t)}onFileSystemAdded(e){const t=e.data;this.addFileSystem(t)}addFileSystem(e){const t=new B(this,e,this.workspace);this.boundFileSystems.set(e.path(),t)}onFileSystemRemoved(e){const t=e.data,s=this.boundFileSystems.get(t.path());s&&s.dispose(),this.boundFileSystems.delete(t.path())}fileSystemFilesChanged(e){const t=e.data;for(const e of t.changed.keysArray()){const s=this.boundFileSystems.get(e);s&&t.changed.get(e).forEach((e=>s.fileChanged(e)))}for(const e of t.added.keysArray()){const s=this.boundFileSystems.get(e);s&&t.added.get(e).forEach((e=>s.fileChanged(e)))}for(const e of t.removed.keysArray()){const s=this.boundFileSystems.get(e);s&&t.removed.get(e).forEach((e=>s.removeUISourceCode(e)))}}dispose(){e.EventTarget.removeEventListeners(this.eventListeners);for(const e of this.boundFileSystems.values())e.dispose(),this.boundFileSystems.delete(e.fileSystemInternal.path())}}class B extends i.Workspace.ProjectStore{fileSystemInternal;fileSystemBaseURL;fileSystemParentURL;fileSystemWorkspaceBinding;fileSystemPathInternal;creatingFilesGuard;constructor(t,s,r){const n=s.path(),o=O.projectId(n);console.assert(!r.project(o));const a=n.substr(n.lastIndexOf("/")+1);super(r,o,i.Workspace.projectTypes.FileSystem,a),this.fileSystemInternal=s,this.fileSystemBaseURL=e.ParsedURL.ParsedURL.concatenate(this.fileSystemInternal.path(),"/"),this.fileSystemParentURL=e.ParsedURL.ParsedURL.substr(this.fileSystemBaseURL,0,n.lastIndexOf("/")+1),this.fileSystemWorkspaceBinding=t,this.fileSystemPathInternal=n,this.creatingFilesGuard=new Set,r.addProject(this),this.populate()}fileSystemPath(){return this.fileSystemPathInternal}fileSystem(){return this.fileSystemInternal}mimeType(e){return this.fileSystemInternal.mimeFromPath(e.url())}initialGitFolders(){return this.fileSystemInternal.initialGitFolders().map((t=>e.ParsedURL.ParsedURL.concatenate(this.fileSystemPathInternal,"/",t)))}filePathForUISourceCode(t){return e.ParsedURL.ParsedURL.sliceUrlToEncodedPathString(t.url(),this.fileSystemPathInternal.length)}isServiceProject(){return!1}requestMetadata(e){const t=H.get(e);if(t)return t;const s=this.filePathForUISourceCode(e),r=this.fileSystemInternal.getMetadata(s).then((function(e){if(!e)return null;return new i.UISourceCode.UISourceCodeMetadata(e.modificationTime,e.size)}));return H.set(e,r),r}requestFileBlob(e){return this.fileSystemInternal.requestFileBlob(this.filePathForUISourceCode(e))}requestFileContent(e){const t=this.filePathForUISourceCode(e);return this.fileSystemInternal.requestFileContent(t)}canSetFileContent(){return!0}async setFileContent(e,t,s){const r=this.filePathForUISourceCode(e);await this.fileSystemInternal.setFileContent(r,t,s)}fullDisplayName(e){const t=e.project().fileSystemParentURL;return e.url().substring(t.length)}canRename(){return!0}rename(t,s,r){if(s===t.name())return void r(!0,t.name(),t.url(),t.contentType());let n=this.filePathForUISourceCode(t);this.fileSystemInternal.renameFile(n,s,function(s,i){if(!s||!i)return void r(!1,i);console.assert(Boolean(i));const o=n.lastIndexOf("/"),a=e.ParsedURL.ParsedURL.substr(n,0,o);n=e.ParsedURL.ParsedURL.encodedFromParentPathAndName(a,i),n=e.ParsedURL.ParsedURL.substr(n,1);const d=e.ParsedURL.ParsedURL.concatenate(this.fileSystemBaseURL,n),l=this.fileSystemInternal.contentType(i);this.renameUISourceCode(t,i),r(!0,i,d,l)}.bind(this))}async searchInFileContent(e,t,s,r){const i=this.filePathForUISourceCode(e),o=await this.fileSystemInternal.requestFileContent(i);return!n.ContentData.ContentData.isError(o)&&o.isTextContent?n.TextUtils.performSearchInContent(o.text,t,s,r):[]}async findFilesMatchingSearchRequest(e,t,s){let r=t.map((e=>e.url()));const n=e.queries().slice();n.length||n.push(""),s.setTotalWork(n.length);for(const t of n){const n=await this.fileSystemInternal.searchInPath(e.isRegex()?"":t,s);n.sort(o.StringUtilities.naturalOrderComparator),r=o.ArrayUtilities.intersectOrdered(r,n,o.StringUtilities.naturalOrderComparator),s.incrementWorked(1)}const i=new Map;for(const e of r){const t=this.uiSourceCodeForURL(e);t&&i.set(t,null)}return s.done(),i}indexContent(e){this.fileSystemInternal.indexContent(e)}populate(){const e=this.fileSystemInternal.initialFilePaths();if(0===e.length)return;const s=performance.now();(function r(n){const i=Math.min(n+1e3,e.length);for(let t=n;t<i;++t)this.addFile(e[t]);i<e.length?window.setTimeout(r.bind(this,i),100):"filesystem"===this.type()&&t.userMetrics.workspacesPopulated(performance.now()-s)}).call(this,0)}excludeFolder(t){let s=e.ParsedURL.ParsedURL.sliceUrlToEncodedPathString(t,this.fileSystemBaseURL.length);s.startsWith("/")||(s=e.ParsedURL.ParsedURL.prepend("/",s)),s.endsWith("/")||(s=e.ParsedURL.ParsedURL.concatenate(s,"/")),this.fileSystemInternal.addExcludedFolder(s);for(const e of this.uiSourceCodes())e.url().startsWith(t)&&this.removeUISourceCode(e.url())}canExcludeFolder(e){return this.fileSystemInternal.canExcludeFolder(e)}canCreateFile(){return!0}async createFile(e,t,s,r){const n=this.fileSystemPathInternal+e+(e.endsWith("/")?"":"/")+t;this.creatingFilesGuard.add(n);const i=await this.fileSystemInternal.createFile(e,t);if(!i)return null;const o=this.addFile(i,s,r);return this.creatingFilesGuard.delete(n),o}deleteFile(e){const t=this.filePathForUISourceCode(e);this.fileSystemInternal.deleteFile(t).then((t=>{t&&this.removeUISourceCode(e.url())}))}deleteDirectoryRecursively(e){return this.fileSystemInternal.deleteDirectoryRecursively(e)}remove(){this.fileSystemWorkspaceBinding.isolatedFileSystemManager.removeFileSystem(this.fileSystemInternal)}addFile(t,s,r){const n=this.fileSystemInternal.contentType(t),i=this.createUISourceCode(e.ParsedURL.ParsedURL.concatenate(this.fileSystemBaseURL,t),n);return void 0!==s&&i.setContent(s,Boolean(r)),this.addUISourceCode(i),i}fileChanged(e){if(this.creatingFilesGuard.has(e))return;const t=this.uiSourceCodeForURL(e);if(t)H.delete(t),t.checkContentUpdated();else{const t=this.fileSystemInternal.contentType(e);this.addUISourceCode(this.createUISourceCode(e,t))}}tooltipForURL(e){return this.fileSystemInternal.tooltipForURL(e)}dispose(){this.removeProject()}}const H=new WeakMap;var N=Object.freeze({__proto__:null,FileSystemWorkspaceBinding:O,FileSystem:B});let D;const q=["chromewebstore.google.com","chrome.google.com"];class _ extends e.ObjectWrapper.ObjectWrapper{bindings;originalResponseContentPromises;savingForOverrides;savingSymbol;enabledSetting;workspace;networkUISourceCodeForEncodedPath;interceptionHandlerBound;updateInterceptionThrottler;projectInternal;activeProject;activeInternal;enabled;eventDescriptors;#e=new Map;#t=new WeakMap;#s;#r;constructor(t){super(),this.bindings=new WeakMap,this.originalResponseContentPromises=new WeakMap,this.savingForOverrides=new WeakSet,this.savingSymbol=Symbol("SavingForOverrides"),this.enabledSetting=e.Settings.Settings.instance().moduleSetting("persistence-network-overrides-enabled"),this.enabledSetting.addChangeListener(this.enabledChanged,this),this.workspace=t,this.networkUISourceCodeForEncodedPath=new Map,this.interceptionHandlerBound=this.interceptionHandler.bind(this),this.updateInterceptionThrottler=new e.Throttler.Throttler(50),this.#s=new e.Throttler.Throttler(50),this.#r=new Set,this.projectInternal=null,this.activeProject=null,this.activeInternal=!1,this.enabled=!1,this.workspace.addEventListener(i.Workspace.Events.ProjectAdded,(e=>{this.onProjectAdded(e.data)})),this.workspace.addEventListener(i.Workspace.Events.ProjectRemoved,(e=>{this.onProjectRemoved(e.data)})),re.instance().addNetworkInterceptor(this.canHandleNetworkUISourceCode.bind(this)),l.BreakpointManager.BreakpointManager.instance().addUpdateBindingsCallback(this.networkUISourceCodeAdded.bind(this)),this.eventDescriptors=[],this.enabledChanged(),s.TargetManager.TargetManager.instance().observeTargets(this)}targetAdded(){this.updateActiveProject()}targetRemoved(){this.updateActiveProject()}static instance(e={forceNew:null,workspace:null}){const{forceNew:t,workspace:s}=e;if(!D||t){if(!s)throw new Error("Missing workspace for NetworkPersistenceManager");D=new _(s)}return D}active(){return this.activeInternal}project(){return this.projectInternal}originalContentForUISourceCode(e){const t=this.bindings.get(e);if(!t)return null;const s=t.fileSystem;return this.originalResponseContentPromises.get(s)||null}async enabledChanged(){this.enabled!==this.enabledSetting.get()&&(this.enabled=this.enabledSetting.get(),this.enabled?(t.userMetrics.actionTaken(t.UserMetrics.Action.PersistenceNetworkOverridesEnabled),this.eventDescriptors=[i.Workspace.WorkspaceImpl.instance().addEventListener(i.Workspace.Events.UISourceCodeRenamed,(e=>{this.uiSourceCodeRenamedListener(e)})),i.Workspace.WorkspaceImpl.instance().addEventListener(i.Workspace.Events.UISourceCodeAdded,(e=>{this.uiSourceCodeAdded(e)})),i.Workspace.WorkspaceImpl.instance().addEventListener(i.Workspace.Events.UISourceCodeRemoved,(e=>{this.uiSourceCodeRemovedListener(e)})),i.Workspace.WorkspaceImpl.instance().addEventListener(i.Workspace.Events.WorkingCopyCommitted,(e=>this.onUISourceCodeWorkingCopyCommitted(e.data.uiSourceCode)))],await this.updateActiveProject()):(t.userMetrics.actionTaken(t.UserMetrics.Action.PersistenceNetworkOverridesDisabled),e.EventTarget.removeEventListeners(this.eventDescriptors),await this.updateActiveProject()),this.dispatchEventToListeners("LocalOverridesProjectUpdated",this.enabled))}async uiSourceCodeRenamedListener(e){const t=e.data.uiSourceCode;await this.onUISourceCodeRemoved(t),await this.onUISourceCodeAdded(t)}async uiSourceCodeRemovedListener(e){await this.onUISourceCodeRemoved(e.data)}async uiSourceCodeAdded(e){await this.onUISourceCodeAdded(e.data)}async updateActiveProject(){const e=this.activeInternal;if(this.activeInternal=Boolean(this.enabledSetting.get()&&s.TargetManager.TargetManager.instance().rootTarget()&&this.projectInternal),this.activeInternal!==e){if(this.activeInternal&&this.projectInternal){await Promise.all([...this.projectInternal.uiSourceCodes()].map((e=>this.filesystemUISourceCodeAdded(e))));const e=this.workspace.projectsForType(i.Workspace.projectTypes.Network);for(const t of e)await Promise.all([...t.uiSourceCodes()].map((e=>this.networkUISourceCodeAdded(e))))}else this.projectInternal&&(await Promise.all([...this.projectInternal.uiSourceCodes()].map((e=>this.filesystemUISourceCodeRemoved(e)))),this.networkUISourceCodeForEncodedPath.clear());re.instance().refreshAutomapping()}}encodedPathFromUrl(t,s){return e.ParsedURL.ParsedURL.rawPathToEncodedPathString(this.rawPathFromUrl(t,s))}rawPathFromUrl(t,s){if(!this.activeInternal&&!s||!this.projectInternal)return o.DevToolsPath.EmptyRawPathString;let r=e.ParsedURL.ParsedURL.urlWithoutHash(t.replace(/^https?:\/\//,""));r.endsWith("/")&&-1===r.indexOf("?")&&(r=e.ParsedURL.ParsedURL.concatenate(r,"index.html"));let n=_.encodeEncodedPathToLocalPathParts(r);const i=O.fileSystemPath(this.projectInternal.id()),a=n.join("/");if(i.length+a.length>200){const t=n[0],s=n[n.length-1],i=s?s.substr(0,10)+"-":"",d=e.ParsedURL.ParsedURL.extractExtension(r),l=d?"."+d.substr(0,10):"";n=[t,"longurls",i+o.StringUtilities.hashCode(a).toString(16)+l]}return e.ParsedURL.ParsedURL.join(n,"/")}static encodeEncodedPathToLocalPathParts(e){const s=[];for(const r of this.#n(e)){if(!r)continue;let e=encodeURI(r).replace(/[\/\*]/g,(e=>"%"+e[0].charCodeAt(0).toString(16).toUpperCase()));if(t.Platform.isWin()){e=e.replace(/[:\?]/g,(e=>"%"+e[0].charCodeAt(0).toString(16).toUpperCase())),z.has(e.toLowerCase())&&(e=e.split("").map((e=>"%"+e.charCodeAt(0).toString(16).toUpperCase())).join(""));"."===e.charAt(e.length-1)&&(e=e.substr(0,e.length-1)+"%2E")}s.push(e)}return s}static#n(t){const s=(t=e.ParsedURL.ParsedURL.urlWithoutHash(t)).indexOf("?");if(-1===s)return t.split("/");if(0===s)return[t];const r=t.substr(s),n=t.substr(0,t.length-r.length).split("/");return n[n.length-1]+=r,n}fileUrlFromNetworkUrl(t,s){return this.projectInternal?e.ParsedURL.ParsedURL.concatenate(this.projectInternal.fileSystemPath(),"/",this.encodedPathFromUrl(t,s)):o.DevToolsPath.EmptyUrlString}getHeadersUISourceCodeFromUrl(t){const s=this.fileUrlFromNetworkUrl(t,!0),r=e.ParsedURL.ParsedURL.substring(s,0,s.lastIndexOf("/")),n=e.ParsedURL.ParsedURL.concatenate(r,"/",$);return i.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(n)}async getOrCreateHeadersUISourceCodeFromUrl(s){let r=this.getHeadersUISourceCodeFromUrl(s);if(!r&&this.projectInternal){const n=this.encodedPathFromUrl(s,!0),i=e.ParsedURL.ParsedURL.substring(n,0,n.lastIndexOf("/"));r=await this.projectInternal.createFile(i,$,""),t.userMetrics.actionTaken(t.UserMetrics.Action.HeaderOverrideFileCreated)}return r}decodeLocalPathToUrlPath(e){try{return unescape(e)}catch(e){console.error(e)}return e}async#i(e){const t=this.bindings.get(e),s=e.url().endsWith($);if(t){const e=this.#o(t.network);await e.run(this.#a.bind(this,t))}else s&&this.dispatchEventToListeners("RequestsForHeaderOverridesFileChanged",e)}async#d(e){const t=this.bindings.get(e);t&&await this.#a(t)}#a(e){return this.bindings.delete(e.network),this.bindings.delete(e.fileSystem),re.instance().removeBinding(e)}async#l(e,t){const s=this.#o(e);await s.run((async()=>{const s=this.bindings.get(e);if(s){const{network:r,fileSystem:n}=s;if(e===r&&t===n)return;await this.#d(e),await this.#d(t)}await this.#c(e,t)}))}#o(t){let s=this.#t.get(t);return s||(s=new e.Mutex.Mutex,this.#t.set(t,s)),s}async#c(e,t){const s=new pe(e,t);this.bindings.set(e,s),this.bindings.set(t,s),await re.instance().addBinding(s);const r=this.savingForOverrides.has(e)?e:t,{content:n,isEncoded:i}=await r.requestContent();re.instance().syncContent(r,n||"",i)}onUISourceCodeWorkingCopyCommitted(e){this.saveUISourceCodeForOverrides(e),this.updateInterceptionPatterns()}isActiveHeaderOverrides(e){return!!this.enabledSetting.get()&&(e.url().endsWith($)&&this.hasMatchingNetworkUISourceCodeForHeaderOverridesFile(e))}isUISourceCodeOverridable(e){return e.project().type()===i.Workspace.projectTypes.Network&&!_.isForbiddenNetworkUrl(e.url())}#h(e){return this.bindings.has(e)||this.savingForOverrides.has(e)}#u(e){return this.isUISourceCodeOverridable(e)&&!this.#h(e)&&!this.activeInternal&&!this.projectInternal}#p(e){return this.activeInternal&&this.isUISourceCodeOverridable(e)&&!this.#h(e)}async setupAndStartLocalOverrides(e){return this.#u(e)&&(t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideContentContextMenuSetup),await new Promise((e=>h.InspectorView.InspectorView.instance().displaySelectOverrideFolderInfobar(e))),await M.instance().addFileSystem("overrides")),this.project()?(this.enabledSetting.get()||(t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideContentContextMenuActivateDisabled),this.enabledSetting.set(!0),await this.once("LocalOverridesProjectUpdated")),this.#h(e)?t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideContentContextMenuOpenExistingFile):(t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideContentContextMenuSaveNewFile),e.commitWorkingCopy(),await this.saveUISourceCodeForOverrides(e)),!0):(t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideContentContextMenuAbandonSetup),!1)}async saveUISourceCodeForOverrides(t){if(!this.#p(t))return;this.savingForOverrides.add(t);let s=this.encodedPathFromUrl(t.url());const{content:r,isEncoded:n}=await t.requestContent(),i=s.lastIndexOf("/"),o=e.ParsedURL.ParsedURL.substring(s,i+1),a=e.ParsedURL.ParsedURL.encodedPathToRawPathString(o);s=e.ParsedURL.ParsedURL.substr(s,0,i),this.projectInternal&&await this.projectInternal.createFile(s,a,r??"",n),this.fileCreatedForTest(s,a),this.savingForOverrides.delete(t)}fileCreatedForTest(e,t){}patternForFileSystemUISourceCode(e){const t=O.relativePath(e);if(t.length<2)return"";if("longurls"===t[1]&&2!==t.length)return"file:"===t[0]?"file:///*":"http?://"+t[0]+"/*";const s=this.decodeLocalPathToUrlPath(this.decodeLocalPathToUrlPath(t.join("/")));return s.startsWith("file:/")?"file:///"+s.substring(6):"http?://"+s}isForbiddenFileUrl(e){const t=O.relativePath(e),s=this.decodeLocalPathToUrlPath(this.decodeLocalPathToUrlPath(t[0]||""));return"chrome:"===s||q.includes(s)}static isForbiddenNetworkUrl(t){const s=e.ParsedURL.ParsedURL.fromString(t);return!!s&&("chrome"===s.scheme||q.includes(s.host))}async onUISourceCodeAdded(e){await this.networkUISourceCodeAdded(e),await this.filesystemUISourceCodeAdded(e)}canHandleNetworkUISourceCode(t){return this.activeInternal&&!e.ParsedURL.schemeIs(t.url(),"snippet:")}async networkUISourceCodeAdded(t){if(t.project().type()!==i.Workspace.projectTypes.Network||!this.canHandleNetworkUISourceCode(t))return;const s=e.ParsedURL.ParsedURL.urlWithoutHash(t.url());this.networkUISourceCodeForEncodedPath.set(this.encodedPathFromUrl(s),t);const r=this.projectInternal.uiSourceCodeForURL(this.fileUrlFromNetworkUrl(s));r&&await this.#l(t,r),this.#m(t)}async filesystemUISourceCodeAdded(t){if(!this.activeInternal||t.project()!==this.projectInternal)return;this.updateInterceptionPatterns();const s=O.relativePath(t),r=this.networkUISourceCodeForEncodedPath.get(e.ParsedURL.ParsedURL.join(s,"/"));r&&await this.#l(r,t)}async#g(e){const t=(await e.requestContent()).content||"[]";let s=[];try{if(s=JSON.parse(t),!s.every(V))throw"Type mismatch after parsing"}catch(t){return console.error("Failed to parse",e.url(),"for locally overriding headers."),[]}return s}#f(e){const t=this.decodeLocalPathToUrlPath(e);return{singlyDecodedPath:t,decodedPath:this.decodeLocalPathToUrlPath(t)}}async generateHeaderPatterns(t){const s=await this.#g(t),r=O.relativePath(t),n=e.ParsedURL.ParsedURL.slice(e.ParsedURL.ParsedURL.join(r,"/"),0,-$.length),{singlyDecodedPath:i,decodedPath:o}=this.#f(n);let a;return a=r.length>2&&"longurls"===r[1]&&s.length?this.#S(o,s,r[0]):o.startsWith("file:/")?this.#y(e.ParsedURL.ParsedURL.substring(o,6),s):this.#P(o,s),{...a,path:i}}#P(e,t){const s=new Set,r=[];for(const n of t){s.add("http?://"+e+n.applyTo),""===e&&(s.add("file:///"+n.applyTo),r.push({applyToRegex:new RegExp("^file:///"+G(e+n.applyTo)+"$"),headers:n.headers}));const{head:t,tail:i}=J(n.applyTo);i?(s.add("http?://"+e+t),r.push({applyToRegex:new RegExp(`^${G(e+t)}(${G(i)})?$`),headers:n.headers})):r.push({applyToRegex:new RegExp(`^${G(e+n.applyTo)}$`),headers:n.headers})}return{headerPatterns:s,overridesWithRegex:r}}#y(e,t){const s=new Set,r=[];for(const n of t)s.add("file:///"+e+n.applyTo),r.push({applyToRegex:new RegExp(`^file:/${G(e+n.applyTo)}$`),headers:n.headers});return{headerPatterns:s,overridesWithRegex:r}}#S(t,s,r){const n=new Set;let{decodedPath:i}=this.#f(e.ParsedURL.ParsedURL.concatenate(r,"/*"));const o=t.startsWith("file:/");o&&(t=e.ParsedURL.ParsedURL.substring(t,6),i=e.ParsedURL.ParsedURL.substring(i,6)),n.add((o?"file:///":"http?://")+i);const a=[];for(const e of s)a.push({applyToRegex:new RegExp(`^${o?"file:/":""}${G(t+e.applyTo)}$`),headers:e.headers});return{headerPatterns:n,overridesWithRegex:a}}async updateInterceptionPatternsForTests(){await this.#v()}updateInterceptionPatterns(){this.updateInterceptionThrottler.schedule(this.#v.bind(this))}async#v(){if(this.#e.clear(),!this.activeInternal||!this.projectInternal)return s.NetworkManager.MultitargetNetworkManager.instance().setInterceptionHandlerForPatterns([],this.interceptionHandlerBound);let e=new Set;for(const t of this.projectInternal.uiSourceCodes()){if(this.isForbiddenFileUrl(t))continue;const s=this.patternForFileSystemUISourceCode(t);if(t.name()===$){const{headerPatterns:s,path:r,overridesWithRegex:n}=await this.generateHeaderPatterns(t);s.size>0&&(e=new Set([...e,...s]),this.#e.set(r,n))}else e.add(s);const{head:r,tail:n}=J(s);n&&e.add(r)}return s.NetworkManager.MultitargetNetworkManager.instance().setInterceptionHandlerForPatterns(Array.from(e).map((e=>({urlPattern:e,requestStage:"Response"}))),this.interceptionHandlerBound)}async onUISourceCodeRemoved(e){await this.networkUISourceCodeRemoved(e),await this.filesystemUISourceCodeRemoved(e)}async networkUISourceCodeRemoved(e){e.project().type()===i.Workspace.projectTypes.Network&&(await this.#i(e),this.#t.delete(e),this.networkUISourceCodeForEncodedPath.delete(this.encodedPathFromUrl(e.url()))),this.#m(e)}#m(t){if(!this.projectInternal)return;const s=this.projectInternal,r=this.fileUrlFromNetworkUrl(t.url());for(let t=s.fileSystemPath().length;t<r.length;t++){if("/"!==r[t])continue;const n=e.ParsedURL.ParsedURL.concatenate(e.ParsedURL.ParsedURL.substring(r,0,t+1),".headers"),i=s.uiSourceCodeForURL(n);i&&(this.#r.add(i),this.#s.schedule(this.#F.bind(this)))}}#F(){for(const e of this.#r)this.dispatchEventToListeners("RequestsForHeaderOverridesFileChanged",e);return this.#r.clear(),Promise.resolve()}hasMatchingNetworkUISourceCodeForHeaderOverridesFile(t){const s=O.relativePath(t),r=e.ParsedURL.ParsedURL.slice(e.ParsedURL.ParsedURL.join(s,"/"),0,-$.length);for(const e of this.networkUISourceCodeForEncodedPath.keys())if(e.startsWith(r))return!0;return!1}async filesystemUISourceCodeRemoved(e){e.project()===this.projectInternal&&(this.updateInterceptionPatterns(),this.originalResponseContentPromises.delete(e),await this.#i(e))}async setProject(e){e!==this.projectInternal&&(this.projectInternal&&await Promise.all([...this.projectInternal.uiSourceCodes()].map((e=>this.filesystemUISourceCodeRemoved(e)))),this.projectInternal=e,this.projectInternal&&await Promise.all([...this.projectInternal.uiSourceCodes()].map((e=>this.filesystemUISourceCodeAdded(e)))),await this.updateActiveProject(),this.dispatchEventToListeners("ProjectChanged",this.projectInternal))}async onProjectAdded(e){if(e.type()!==i.Workspace.projectTypes.FileSystem||"overrides"!==O.fileSystemType(e))return;O.fileSystemPath(e.id())&&(this.projectInternal&&this.projectInternal.remove(),await this.setProject(e))}async onProjectRemoved(e){for(const t of e.uiSourceCodes())await this.networkUISourceCodeRemoved(t);e===this.projectInternal&&await this.setProject(null)}mergeHeaders(e,t){const r=new o.MapUtilities.Multimap;for(const{name:e,value:s}of t)"set-cookie"!==e.toLowerCase()&&r.set(e.toLowerCase(),s);const n=new Set(r.keysArray());for(const{name:t,value:s}of e){const e=t.toLowerCase();n.has(e)||"set-cookie"===e||r.set(e,s)}const i=[];for(const e of r.keysArray())for(const t of r.get(e))i.push({name:e,value:t});const a=e.filter((e=>"set-cookie"===e.name.toLowerCase()))||[],d=t.filter((e=>"set-cookie"===e.name.toLowerCase())),l=s.NetworkManager.InterceptedRequest.mergeSetCookieHeaders(a,d);return i.push(...l),i}#w(e,t,s){const r=this.#e.get(e)||[];for(const e of r){const r=this.decodeLocalPathToUrlPath(this.rawPathFromUrl(t));e.applyToRegex.test(r)&&(s=this.mergeHeaders(s,e.headers))}return s}handleHeaderInterception(t){let s=t.responseHeaders||[];const r=this.rawPathFromUrl(t.request.url).split("/");let n=o.DevToolsPath.EmptyEncodedPathString;s=this.#w(n,t.request.url,s);for(const i of r)n=e.ParsedURL.ParsedURL.concatenate(n,i,"/"),s=this.#w(n,t.request.url,s);return s}async interceptionHandler(t){const s=t.request.method;if(!this.activeInternal||"OPTIONS"===s)return;const r=this.projectInternal,i=this.fileUrlFromNetworkUrl(t.request.url),o=r.uiSourceCodeForURL(i);let a=this.handleHeaderInterception(t);if(!o&&!a.length)return;a.length||(a=t.responseHeaders||[]);let{mimeType:d}=t.getMimeTypeAndCharset();if(!d){const s=e.ResourceType.resourceTypes[t.resourceType]||e.ResourceType.resourceTypes.Other;d=o?.mimeType()||"",e.ResourceType.ResourceType.fromMimeType(d)!==s&&(d=s.canonicalMimeType())}if(o){this.originalResponseContentPromises.set(o,t.responseBody().then((e=>n.ContentData.ContentData.isError(e)||!e.isTextContent?null:e.text)));const e=o.project(),s=await e.requestFileBlob(o);s&&t.continueRequestWithContent(new Blob([s],{type:d}),!1,a,!0)}else if(t.isRedirect())t.continueRequestWithContent(new Blob([],{type:d}),!0,a,!1);else{const e=await t.responseBody();if(!n.ContentData.ContentData.isError(e)){const s=e.isTextContent?e.text:e.base64;t.continueRequestWithContent(new Blob([s],{type:d}),!e.isTextContent,a,!1)}}}}const z=new Set(["con","prn","aux","nul","com1","com2","com3","com4","com5","com6","com7","com8","com9","lpt1","lpt2","lpt3","lpt4","lpt5","lpt6","lpt7","lpt8","lpt9"]),$=".headers";function V(e){return!!(e&&"string"==typeof e.applyTo&&e.headers&&e.headers.length&&Array.isArray(e.headers))&&e.headers.every((e=>"string"==typeof e.name&&"string"==typeof e.value))}function G(e){return o.StringUtilities.escapeCharacters(e,"[]{}()\\.^$+|-,?").replaceAll("*",".*")}function J(e){const t=e.lastIndexOf("/"),s=t>=0?e.slice(t+1):e,r=t>=0?e.slice(0,t+1):"",n=new RegExp("^"+G(s)+"$");return"*"!==s&&(n.test("index.html")||n.test("index.htm")||n.test("index.php"))?{head:r,tail:s}:{head:e}}var X=Object.freeze({__proto__:null,NetworkPersistenceManager:_,HEADERS_FILENAME:$,isHeaderOverride:V,escapeRegex:G,extractDirectoryIndex:J});const Y={linkedToSourceMapS:"Linked to source map: {PH1}",linkedToS:"Linked to {PH1}"},K=a.i18n.registerUIStrings("models/persistence/PersistenceUtils.ts",Y),Q=a.i18n.getLocalizedString.bind(void 0,K);class Z{static tooltipForUISourceCode(e){const t=re.instance().binding(e);return t?e===t.network?O.tooltipForUISourceCode(t.fileSystem):t.network.contentType().isFromSourceMap()?Q(Y.linkedToSourceMapS,{PH1:o.StringUtilities.trimMiddle(t.network.url(),150)}):Q(Y.linkedToS,{PH1:o.StringUtilities.trimMiddle(t.network.url(),150)}):""}static iconForUISourceCode(t){const s=re.instance().binding(t);if(s){if(!e.ParsedURL.schemeIs(s.fileSystem.url(),"file:"))return null;const t=new c.Icon.Icon;return t.data={iconName:"document",color:"var(--icon-default)",width:"16px",height:"16px"},h.Tooltip.Tooltip.install(t,Z.tooltipForUISourceCode(s.network)),_.instance().project()===s.fileSystem.project()?t.classList.add("dot","purple"):t.classList.add("dot","green"),t}if(t.project().type()!==i.Workspace.projectTypes.FileSystem||!e.ParsedURL.schemeIs(t.url(),"file:"))return null;if(_.instance().isActiveHeaderOverrides(t)){const e=new c.Icon.Icon;return e.data={iconName:"document",color:"var(--icon-default)",width:"16px",height:"16px"},e.classList.add("dot","purple"),e}const r=new c.Icon.Icon;return r.data={iconName:"document",color:"var(--icon-default)",width:"16px",height:"16px"},h.Tooltip.Tooltip.install(r,Z.tooltipForUISourceCode(t)),r}}class ee extends e.ObjectWrapper.ObjectWrapper{constructor(e){super(),e.addEventListener(ue.BindingCreated,this.bindingChanged,this),e.addEventListener(ue.BindingRemoved,this.bindingChanged,this)}bindingChanged(e){const t=e.data;this.dispatchEventToListeners("LinkIconChanged",t.network)}linkIcon(e){return Z.iconForUISourceCode(e)}}var te=Object.freeze({__proto__:null,PersistenceUtils:Z,LinkDecorator:ee});let se;class re extends e.ObjectWrapper.ObjectWrapper{workspace;breakpointManager;filePathPrefixesToBindingCount;subscribedBindingEventListeners;mapping;constructor(e,t){super(),this.workspace=e,this.breakpointManager=t,this.breakpointManager.addUpdateBindingsCallback(this.#C.bind(this)),this.filePathPrefixesToBindingCount=new ne,this.subscribedBindingEventListeners=new o.MapUtilities.Multimap;const s=new ee(this);d.Linkifier.Linkifier.setLinkDecorator(s),this.mapping=new ge(this.workspace,this.onStatusAdded.bind(this),this.onStatusRemoved.bind(this))}static instance(e={forceNew:null,workspace:null,breakpointManager:null}){const{forceNew:t,workspace:s,breakpointManager:r}=e;if(!se||t){if(!s||!r)throw new Error("Missing arguments for workspace");se=new re(s,r)}return se}addNetworkInterceptor(e){this.mapping.addNetworkInterceptor(e)}refreshAutomapping(){this.mapping.scheduleRemap()}async addBinding(e){await this.innerAddBinding(e)}async addBindingForTest(e){await this.innerAddBinding(e)}async removeBinding(e){await this.innerRemoveBinding(e)}async removeBindingForTest(e){await this.innerRemoveBinding(e)}#C(e){return e.project().type()!==i.Workspace.projectTypes.Network?Promise.resolve():this.mapping.computeNetworkStatus(e)}async innerAddBinding(e){ie.set(e.network,e),ie.set(e.fileSystem,e),e.fileSystem.forceLoadOnCheckContent(),e.network.addEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.onWorkingCopyCommitted,this),e.fileSystem.addEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.onWorkingCopyCommitted,this),e.network.addEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.onWorkingCopyChanged,this),e.fileSystem.addEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.onWorkingCopyChanged,this),this.filePathPrefixesToBindingCount.add(e.fileSystem.url()),await this.moveBreakpoints(e.fileSystem,e.network),console.assert(!e.fileSystem.isDirty()||!e.network.isDirty()),e.fileSystem.isDirty()?this.syncWorkingCopy(e.fileSystem):e.network.isDirty()?this.syncWorkingCopy(e.network):e.network.hasCommits()&&e.network.content()!==e.fileSystem.content()&&(e.network.setWorkingCopy(e.network.content()),this.syncWorkingCopy(e.network)),this.notifyBindingEvent(e.network),this.notifyBindingEvent(e.fileSystem),this.dispatchEventToListeners(ue.BindingCreated,e)}async innerRemoveBinding(e){ie.get(e.network)===e&&(console.assert(ie.get(e.network)===ie.get(e.fileSystem),"ERROR: inconsistent binding for networkURL "+e.network.url()),ie.delete(e.network),ie.delete(e.fileSystem),e.network.removeEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.onWorkingCopyCommitted,this),e.fileSystem.removeEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.onWorkingCopyCommitted,this),e.network.removeEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.onWorkingCopyChanged,this),e.fileSystem.removeEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.onWorkingCopyChanged,this),this.filePathPrefixesToBindingCount.remove(e.fileSystem.url()),await this.breakpointManager.copyBreakpoints(e.network,e.fileSystem),this.notifyBindingEvent(e.network),this.notifyBindingEvent(e.fileSystem),this.dispatchEventToListeners(ue.BindingRemoved,e))}onStatusAdded(e){const t=new pe(e.network,e.fileSystem);return oe.set(e,t),this.innerAddBinding(t)}async onStatusRemoved(e){const t=oe.get(e);await this.innerRemoveBinding(t)}onWorkingCopyChanged(e){const t=e.data;this.syncWorkingCopy(t)}syncWorkingCopy(e){const t=ie.get(e);if(!t||de.has(t))return;const n=t.network===e?t.fileSystem:t.network;if(!e.isDirty())return de.add(t),n.resetWorkingCopy(),de.delete(t),void this.contentSyncedForTest();const i=r.NetworkProject.NetworkProject.targetForUISourceCode(t.network);if(i&&i.type()===s.Target.Type.Node){const t=e.workingCopy();n.requestContent().then((()=>{const e=re.rewrapNodeJSContent(n,n.workingCopy(),t);o.call(this,(()=>e))}))}else o.call(this,(()=>e.workingCopy()));function o(e){t&&de.add(t),n.setWorkingCopyGetter(e),t&&de.delete(t),this.contentSyncedForTest()}}onWorkingCopyCommitted(e){const t=e.data.uiSourceCode,s=e.data.content;this.syncContent(t,s,Boolean(e.data.encoded))}syncContent(e,t,n){const i=ie.get(e);if(!i||ae.has(i))return;const o=i.network===e?i.fileSystem:i.network,a=r.NetworkProject.NetworkProject.targetForUISourceCode(i.network);function d(e){i&&ae.add(i),o.setContent(e,n),i&&ae.delete(i),this.contentSyncedForTest()}a&&a.type()===s.Target.Type.Node?o.requestContent().then((e=>{const s=re.rewrapNodeJSContent(o,e.content||"",t);d.call(this,s)})):d.call(this,t)}static rewrapNodeJSContent(e,t,s){return e.project().type()===i.Workspace.projectTypes.FileSystem?(s.startsWith(le)&&s.endsWith(ce)&&(s=s.substring(le.length,s.length-ce.length)),t.startsWith(he)&&(s=he+s)):(s.startsWith(he)&&(s=s.substring(he.length)),t.startsWith(le)&&t.endsWith(ce)&&(s=le+s+ce)),s}contentSyncedForTest(){}async moveBreakpoints(e,t){const s=this.breakpointManager.breakpointLocationsForUISourceCode(e).map((e=>e.breakpoint));await Promise.all(s.map((async e=>(await e.remove(!1),this.breakpointManager.setBreakpoint(t,e.lineNumber(),e.columnNumber(),e.condition(),e.enabled(),e.isLogpoint(),"RESTORED")))))}hasUnsavedCommittedChanges(e){return!this.workspace.hasResourceContentTrackingExtensions()&&(!e.project().canSetFileContent()&&(!ie.has(e)&&Boolean(e.hasCommits())))}binding(e){return ie.get(e)||null}subscribeForBindingEvent(e,t){this.subscribedBindingEventListeners.set(e,t)}unsubscribeFromBindingEvent(e,t){this.subscribedBindingEventListeners.delete(e,t)}notifyBindingEvent(e){if(!this.subscribedBindingEventListeners.has(e))return;const t=Array.from(this.subscribedBindingEventListeners.get(e));for(const e of t)e.call(null)}fileSystem(e){const t=this.binding(e);return t?t.fileSystem:null}network(e){const t=this.binding(e);return t?t.network:null}filePathHasBindings(e){return this.filePathPrefixesToBindingCount.hasBindingPrefix(e)}}class ne{prefixCounts;constructor(){this.prefixCounts=new Map}getPlatformCanonicalFilePath(s){return t.Platform.isWin()?e.ParsedURL.ParsedURL.toLowerCase(s):s}add(e){e=this.getPlatformCanonicalFilePath(e);let t="";for(const s of e.split("/")){t+=s+"/";const e=this.prefixCounts.get(t)||0;this.prefixCounts.set(t,e+1)}}remove(e){e=this.getPlatformCanonicalFilePath(e);let t="";for(const s of e.split("/")){t+=s+"/";const e=this.prefixCounts.get(t);1===e?this.prefixCounts.delete(t):void 0!==e&&this.prefixCounts.set(t,e-1)}}hasBindingPrefix(t){return(t=this.getPlatformCanonicalFilePath(t)).endsWith("/")||(t=e.ParsedURL.ParsedURL.concatenate(t,"/")),this.prefixCounts.has(t)}}const ie=new WeakMap,oe=new WeakMap,ae=new WeakSet,de=new WeakSet,le="(function (exports, require, module, __filename, __dirname) { ",ce="\n});",he="#!/usr/bin/env node";var ue;!function(e){e.BindingCreated="BindingCreated",e.BindingRemoved="BindingRemoved"}(ue||(ue={}));class pe{network;fileSystem;constructor(e,t){this.network=e,this.fileSystem=t}}var me=Object.freeze({__proto__:null,PersistenceImpl:re,NodePrefix:le,NodeSuffix:ce,NodeShebang:he,get Events(){return ue},PersistenceBinding:pe});class ge{workspace;onStatusAdded;onStatusRemoved;statuses;fileSystemUISourceCodes;sweepThrottler;sourceCodeToProcessingPromiseMap;sourceCodeToAutoMappingStatusMap;sourceCodeToMetadataMap;filesIndex;projectFoldersIndex;activeFoldersIndex;interceptors;constructor(t,s,r){this.workspace=t,this.onStatusAdded=s,this.onStatusRemoved=r,this.statuses=new Set,this.fileSystemUISourceCodes=new ye,this.sweepThrottler=new e.Throttler.Throttler(100),this.sourceCodeToProcessingPromiseMap=new WeakMap,this.sourceCodeToAutoMappingStatusMap=new WeakMap,this.sourceCodeToMetadataMap=new WeakMap,this.filesIndex=new fe,this.projectFoldersIndex=new Se,this.activeFoldersIndex=new Se,this.interceptors=[],this.workspace.addEventListener(i.Workspace.Events.UISourceCodeAdded,(e=>this.onUISourceCodeAdded(e.data))),this.workspace.addEventListener(i.Workspace.Events.UISourceCodeRemoved,(e=>this.onUISourceCodeRemoved(e.data))),this.workspace.addEventListener(i.Workspace.Events.UISourceCodeRenamed,this.onUISourceCodeRenamed,this),this.workspace.addEventListener(i.Workspace.Events.ProjectAdded,(e=>this.onProjectAdded(e.data)),this),this.workspace.addEventListener(i.Workspace.Events.ProjectRemoved,(e=>this.onProjectRemoved(e.data)),this);for(const e of t.projects())this.onProjectAdded(e);for(const e of t.uiSourceCodes())this.onUISourceCodeAdded(e)}addNetworkInterceptor(e){this.interceptors.push(e),this.scheduleRemap()}scheduleRemap(){for(const e of this.statuses.values())this.clearNetworkStatus(e.network);this.scheduleSweep()}scheduleSweep(){this.sweepThrottler.schedule(function(){const e=this.workspace.projectsForType(i.Workspace.projectTypes.Network);for(const t of e)for(const e of t.uiSourceCodes())this.computeNetworkStatus(e);return this.onSweepHappenedForTest(),Promise.resolve()}.bind(this))}onSweepHappenedForTest(){}onProjectRemoved(e){for(const t of e.uiSourceCodes())this.onUISourceCodeRemoved(t);if(e.type()!==i.Workspace.projectTypes.FileSystem)return;const t=e;for(const e of t.initialGitFolders())this.projectFoldersIndex.removeFolder(e);this.projectFoldersIndex.removeFolder(t.fileSystemPath()),this.scheduleRemap()}onProjectAdded(e){if(e.type()!==i.Workspace.projectTypes.FileSystem)return;const t=e;for(const e of t.initialGitFolders())this.projectFoldersIndex.addFolder(e);this.projectFoldersIndex.addFolder(t.fileSystemPath());for(const t of e.uiSourceCodes())this.onUISourceCodeAdded(t);this.scheduleRemap()}onUISourceCodeAdded(e){const t=e.project();if(t.type()===i.Workspace.projectTypes.FileSystem){if(!O.fileSystemSupportsAutomapping(t))return;this.filesIndex.addPath(e.url()),this.fileSystemUISourceCodes.add(e),this.scheduleSweep()}else t.type()===i.Workspace.projectTypes.Network&&this.computeNetworkStatus(e)}onUISourceCodeRemoved(e){if(e.project().type()===i.Workspace.projectTypes.FileSystem){this.filesIndex.removePath(e.url()),this.fileSystemUISourceCodes.delete(e.url());const t=this.sourceCodeToAutoMappingStatusMap.get(e);t&&this.clearNetworkStatus(t.network)}else e.project().type()===i.Workspace.projectTypes.Network&&this.clearNetworkStatus(e)}onUISourceCodeRenamed(e){const{uiSourceCode:t,oldURL:s}=e.data;if(t.project().type()!==i.Workspace.projectTypes.FileSystem)return;this.filesIndex.removePath(s),this.fileSystemUISourceCodes.delete(s);const r=this.sourceCodeToAutoMappingStatusMap.get(t);r&&this.clearNetworkStatus(r.network),this.filesIndex.addPath(t.url()),this.fileSystemUISourceCodes.add(t),this.scheduleSweep()}computeNetworkStatus(t){const i=this.sourceCodeToProcessingPromiseMap.get(t);if(i)return i;if(this.sourceCodeToAutoMappingStatusMap.has(t))return Promise.resolve();if(this.interceptors.some((e=>e(t))))return Promise.resolve();if(e.ParsedURL.schemeIs(t.url(),"wasm:"))return Promise.resolve();const o=this.createBinding(t).then(async function(e){if(!e)return null;if(this.sourceCodeToProcessingPromiseMap.get(t)!==o)return null;if(e.network.contentType().isFromSourceMap()||!e.fileSystem.contentType().isTextType())return e;if(e.fileSystem.isDirty()&&(e.network.isDirty()||e.network.hasCommits()))return null;const[i,a]=(await Promise.all([e.fileSystem.requestContentData(),e.network.project().requestFileContent(e.network)])).map(n.ContentData.ContentData.asDeferredContent);if(null===i.content||null===a)return null;if(this.sourceCodeToProcessingPromiseMap.get(t)!==o)return null;const d=r.NetworkProject.NetworkProject.targetForUISourceCode(e.network);let l=!1;const c=i.content;if(d&&d.type()===s.Target.Type.Node){if(a.content){l=c===re.rewrapNodeJSContent(e.fileSystem,c,a.content)}}else a.content&&(l=c.trimEnd()===a.content.trimEnd());if(!l)return this.prevalidationFailedForTest(e),null;return e}.bind(this)).then(async function(e){if(this.sourceCodeToProcessingPromiseMap.get(t)!==o)return;if(!e)return this.onBindingFailedForTest(),void this.sourceCodeToProcessingPromiseMap.delete(t);if(this.sourceCodeToAutoMappingStatusMap.has(e.network)||this.sourceCodeToAutoMappingStatusMap.has(e.fileSystem))return void this.sourceCodeToProcessingPromiseMap.delete(t);if(this.statuses.add(e),this.sourceCodeToAutoMappingStatusMap.set(e.network,e),this.sourceCodeToAutoMappingStatusMap.set(e.fileSystem,e),e.exactMatch){const t=this.projectFoldersIndex.closestParentFolder(e.fileSystem.url());!!t&&this.activeFoldersIndex.addFolder(t)&&this.scheduleSweep()}await this.onStatusAdded.call(null,e),this.sourceCodeToProcessingPromiseMap.delete(t)}.bind(this));return this.sourceCodeToProcessingPromiseMap.set(t,o),o}prevalidationFailedForTest(e){}onBindingFailedForTest(){}clearNetworkStatus(e){if(this.sourceCodeToProcessingPromiseMap.has(e))return void this.sourceCodeToProcessingPromiseMap.delete(e);const t=this.sourceCodeToAutoMappingStatusMap.get(e);if(t){if(this.statuses.delete(t),this.sourceCodeToAutoMappingStatusMap.delete(t.network),this.sourceCodeToAutoMappingStatusMap.delete(t.fileSystem),t.exactMatch){const e=this.projectFoldersIndex.closestParentFolder(t.fileSystem.url());e&&this.activeFoldersIndex.removeFolder(e)}this.onStatusRemoved.call(null,t)}}async createBinding(t){const s=t.url();if(e.ParsedURL.schemeIs(s,"file:")||e.ParsedURL.schemeIs(s,"snippet:")){const e=this.fileSystemUISourceCodes.get(s);return e?new Pe(t,e,!1):null}let r=e.ParsedURL.ParsedURL.extractPath(s);if(null===r)return null;r.endsWith("/")&&(r=e.ParsedURL.ParsedURL.concatenate(r,"index.html"));const n=this.filesIndex.similarFiles(r).map((e=>this.fileSystemUISourceCodes.get(e)));if(!n.length)return null;await Promise.all(n.concat(t).map((async e=>{this.sourceCodeToMetadataMap.set(e,await e.requestMetadata())})));const i=n.filter((e=>Boolean(this.activeFoldersIndex.closestParentFolder(e.url())))),o=this.sourceCodeToMetadataMap.get(t);if(!o||!o.modificationTime&&"number"!=typeof o.contentSize)return 1!==i.length?null:new Pe(t,i[0],!1);let a=this.filterWithMetadata(i,o);return a.length||(a=this.filterWithMetadata(n,o)),1!==a.length?null:new Pe(t,a[0],!0)}filterWithMetadata(e,t){return e.filter((e=>{const s=this.sourceCodeToMetadataMap.get(e);if(!s)return!1;const r=!t.modificationTime||!s.modificationTime||Math.abs(t.modificationTime.getTime()-s.modificationTime.getTime())<1e3,n=!t.contentSize||s.contentSize===t.contentSize;return r&&n}))}}class fe{#U=e.Trie.Trie.newArrayTrie();addPath(e){const t=e.split("/").reverse();this.#U.add(t)}removePath(e){const t=e.split("/").reverse();this.#U.remove(t)}similarFiles(e){const t=e.split("/").reverse(),s=this.#U.longestPrefix(t,!1);return 0===s.length?[]:this.#U.words(s).map((e=>e.reverse().join("/")))}}class Se{#I=e.Trie.Trie.newArrayTrie();#k=new Map;addFolder(e){const t=this.#R(e).split("/");this.#I.add(t);const s=t.join("/"),r=this.#k.get(s)??0;return this.#k.set(s,r+1),0===r}removeFolder(e){const t=this.#R(e).split("/"),s=t.join("/"),r=this.#k.get(s)??0;return!!r&&(r>1?(this.#k.set(s,r-1),!1):(this.#I.remove(t),this.#k.delete(s),!0))}closestParentFolder(e){const t=e.split("/");return this.#I.longestPrefix(t,!0).join("/")}#R(t){return t.endsWith("/")?e.ParsedURL.ParsedURL.substring(t,0,t.length-1):t}}class ye{sourceCodes;constructor(){this.sourceCodes=new Map}getPlatformCanonicalFileUrl(s){return t.Platform.isWin()?e.ParsedURL.ParsedURL.toLowerCase(s):s}add(e){const t=this.getPlatformCanonicalFileUrl(e.url());this.sourceCodes.set(t,e)}get(e){return e=this.getPlatformCanonicalFileUrl(e),this.sourceCodes.get(e)}delete(e){e=this.getPlatformCanonicalFileUrl(e),this.sourceCodes.delete(e)}}class Pe{network;fileSystem;exactMatch;constructor(e,t,s){this.network=e,this.fileSystem=t,this.exactMatch=s}}var ve=Object.freeze({__proto__:null,Automapping:ge,AutomappingStatus:Pe});const Fe=new CSSStyleSheet;Fe.replaceSync(".file-system-header{display:flex;flex-direction:row;align-items:center;flex:auto;margin:10px 0}.file-system-header-text{flex:1 0 auto}.add-button{margin-left:10px;align-self:flex-start}.file-system-list{flex:auto}.file-system-list-empty{flex:auto;height:30px;display:flex;align-items:center;justify-content:center;text-align:center}.file-system-list-item{padding:3px 5px;height:30px;display:flex;align-items:center;flex:auto 1 1}.file-system-value{flex:1 1 0}.list-item .file-system-value{white-space:nowrap;text-overflow:ellipsis;user-select:none;overflow:hidden}.file-system-edit-row{flex:none;display:flex;flex-direction:row;margin:6px 5px;align-items:center}.file-system-edit-row input{width:100%;text-align:inherit}\n/*# sourceURL=editFileSystemView.css */\n");const we={excludedFolders:"Excluded folders",add:"Add",none:"None",sViaDevtools:"{PH1} (via .devtools)",folderPath:"Folder path",enterAPath:"Enter a path",enterAUniquePath:"Enter a unique path"},Ce=a.i18n.registerUIStrings("models/persistence/EditFileSystemView.ts",we),Ue=a.i18n.getLocalizedString.bind(void 0,Ce);class Ie extends h.Widget.VBox{fileSystemPath;excludedFolders;eventListeners;excludedFoldersList;muteUpdate;excludedFolderEditor;constructor(e){super(!0),this.fileSystemPath=e,this.excludedFolders=[],this.eventListeners=[M.instance().addEventListener(j.ExcludedFolderAdded,this.update,this),M.instance().addEventListener(j.ExcludedFolderRemoved,this.update,this)];const t=this.contentElement.createChild("div","file-system-header");t.createChild("div","file-system-header-text").textContent=Ue(we.excludedFolders);const s=h.UIUtils.createTextButton(Ue(we.add),this.addExcludedFolderButtonClicked.bind(this),{className:"add-button",jslogContext:"settings.add-excluded-folder"});t.appendChild(s),this.excludedFoldersList=new h.ListWidget.ListWidget(this),this.excludedFoldersList.element.classList.add("file-system-list");const r=document.createElement("div");r.classList.add("file-system-list-empty"),r.textContent=Ue(we.none),this.excludedFoldersList.setEmptyPlaceholder(r),this.excludedFoldersList.show(this.contentElement),this.update()}dispose(){e.EventTarget.removeEventListeners(this.eventListeners)}getFileSystem(){return M.instance().fileSystem(this.fileSystemPath)}update(){if(!this.muteUpdate){this.excludedFoldersList.clear(),this.excludedFolders=[];for(const e of this.getFileSystem().excludedFolders().values())this.excludedFolders.push(e),this.excludedFoldersList.appendItem(e,!0)}}addExcludedFolderButtonClicked(){this.excludedFoldersList.addNewItem(0,"")}renderItem(e,t){const s=document.createElement("div");s.classList.add("file-system-list-item");const r=t?e:Ue(we.sViaDevtools,{PH1:e}),n=s.createChild("div","file-system-value");return n.textContent=r,h.Tooltip.Tooltip.install(n,r),s}removeItemRequested(e,t){this.getFileSystem().removeExcludedFolder(this.excludedFolders[t])}commitEdit(e,t,s){this.muteUpdate=!0,s||this.getFileSystem().removeExcludedFolder(e),this.getFileSystem().addExcludedFolder(this.normalizePrefix(t.control("path-prefix").value)),this.muteUpdate=!1,this.update()}beginEdit(e){const t=this.createExcludedFolderEditor();return t.control("path-prefix").value=e,t}createExcludedFolderEditor(){if(this.excludedFolderEditor)return this.excludedFolderEditor;const e=new h.ListWidget.Editor;this.excludedFolderEditor=e;const t=e.contentElement();t.createChild("div","file-system-edit-row").createChild("div","file-system-value").textContent=Ue(we.folderPath);return t.createChild("div","file-system-edit-row").createChild("div","file-system-value").appendChild(e.createInput("path-prefix","text","/path/to/folder/",function(e,t,s){const r=this.normalizePrefix(s.value.trim());if(!r)return{valid:!1,errorMessage:Ue(we.enterAPath)};const n=this.getFileSystem().excludedFolders().size;for(let e=0;e<n;++e)if(e!==t&&this.excludedFolders[e]===r)return{valid:!1,errorMessage:Ue(we.enterAUniquePath)};return{valid:!0,errorMessage:void 0}}.bind(this))),e}normalizePrefix(e){return e?e+("/"===e[e.length-1]?"":"/"):""}wasShown(){super.wasShown(),this.excludedFoldersList.registerCSSFiles([Fe]),this.registerCSSFiles([Fe])}}var ke=Object.freeze({__proto__:null,EditFileSystemView:Ie});const Re={saveAs:"Save as...",saveImage:"Save image",showOverrides:"Show all overrides",overrideContent:"Override content",openInContainingFolder:"Open in containing folder",overrideSourceMappedFileWarning:"Override ‘{PH1}’ instead?",overrideSourceMappedFileExplanation:"‘{PH1}’ is a source mapped file and cannot be overridden.",saveWasmFailed:"Unable to save WASM module to disk. Most likely the module is too large."},xe=a.i18n.registerUIStrings("models/persistence/PersistenceActions.ts",Re),Le=a.i18n.getLocalizedString.bind(void 0,xe);var be=Object.freeze({__proto__:null,ContextMenuProvider:class{appendApplicableItems(n,o,a){a.contentType().isDocumentOrScriptOrStyleSheet()?o.saveSection().appendItem(Le(Re.saveAs),(async function(){a instanceof i.UISourceCode.UISourceCode&&a.commitWorkingCopy();const t=a.contentURL();let n;const o=function(e){if(!(e instanceof i.UISourceCode.UISourceCode))return null;const t=r.NetworkProject.NetworkProject.targetForUISourceCode(e),n=t?.model(s.DebuggerModel.DebuggerModel);if(n){const t=r.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().scriptFile(e,n);if(t?.script)return t.script}return r.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().scriptsForUISourceCode(e)[0]??null}(a);if(o?.isWasm())try{const t=await o.getWasmBytecode();n={isEncoded:!0,content:await e.Base64.encode(t)}}catch(s){return console.error(`Unable to convert WASM byte code for ${t} to base64. Not saving to disk`,s.stack),void e.Console.Console.instance().addMessage(Le(Re.saveWasmFailed),"error")}else n=await a.requestContent();await i.FileManager.FileManager.instance().save(t,n.content??"",!0,n.isEncoded),i.FileManager.FileManager.instance().close(t)}),{jslogContext:"save-as"}):a instanceof s.Resource.Resource&&a.contentType().isImage()&&o.saveSection().appendItem(Le(Re.saveImage),(async function(){const e=a,t=(await e.requestContent()).content||"",s=document.createElement("a");s.download=e.displayName,s.href="data:"+e.mimeType+";base64,"+t,s.click()}),{jslogContext:"save-image"});const d=i.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(a.contentURL()),l=_.instance(),c=d&&re.instance().binding(d),p=c?c.fileSystem.contentURL():a.contentURL();if(e.ParsedURL.schemeIs(p,"file:")){const s=e.ParsedURL.ParsedURL.urlToRawPathString(p,t.Platform.isWin());o.revealSection().appendItem(Le(Re.openInContainingFolder),(()=>t.InspectorFrontendHost.InspectorFrontendHostInstance.showItemInFolder(s)),{jslogContext:"open-in-containing-folder"})}if(a instanceof i.UISourceCode.UISourceCode&&a.project().type()===i.Workspace.projectTypes.FileSystem)return;let m=!0,g=()=>{};if(d&&l.isUISourceCodeOverridable(d))if(d.contentType().isFromSourceMap()){const e=this.getDeployedUiSourceCode(d);e&&(m=!1,g=this.redirectOverrideToDeployedUiSourceCode.bind(this,e,d))}else m=!1,g=this.handleOverrideContent.bind(this,d,a);u.Runtime.experiments.isEnabled("react-native-specific-ui")||o.overrideSection().appendItem(Le(Re.overrideContent),g,{disabled:m,jslogContext:"override-content"}),a instanceof s.NetworkRequest.NetworkRequest&&o.overrideSection().appendItem(Le(Re.showOverrides),(async()=>{await h.ViewManager.ViewManager.instance().showView("navigator-overrides"),t.userMetrics.actionTaken(t.UserMetrics.Action.ShowAllOverridesFromNetworkContextMenu)}),{jslogContext:"show-overrides"})}async handleOverrideContent(r,n){const o=_.instance();await o.setupAndStartLocalOverrides(r)&&await e.Revealer.reveal(r),n instanceof s.NetworkRequest.NetworkRequest?t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideContentFromNetworkContextMenu):n instanceof i.UISourceCode.UISourceCode&&t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideContentFromSourcesContextMenu),r.isFetchXHR()?t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideFetchXHR):n.contentType().isScript()?t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideScript):n.contentType().isDocument()?t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideDocument):n.contentType().isStyleSheet()?t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideStyleSheet):n.contentType().isImage()?t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideImage):n.contentType().isFont()&&t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideFont)}async redirectOverrideToDeployedUiSourceCode(e,s){t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideContentContextMenuSourceMappedWarning);const n=e.url(),i=r.ResourceUtils.displayNameForURL(n),o=s.url(),a=r.ResourceUtils.displayNameForURL(o),d=Le(Re.overrideSourceMappedFileWarning,{PH1:i})+"\n"+Le(Re.overrideSourceMappedFileExplanation,{PH1:a});await h.UIUtils.ConfirmDialog.show(d,void 0,{jslogContext:"override-source-mapped-file-warning"})&&(t.userMetrics.actionTaken(t.UserMetrics.Action.OverrideContentContextMenuRedirectToDeployed),await this.handleOverrideContent(e,e))}getDeployedUiSourceCode(t){const s=r.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance();for(const e of s.scriptsForUISourceCode(t)){const t=s.uiSourceCodeForScript(e);if(t)return t}const[n]=r.SASSSourceMapping.SASSSourceMapping.uiSourceOrigin(t);if(!n)return null;return i.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(n)||i.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(e.ParsedURL.ParsedURL.urlWithoutHash(n))}}});const Te=new CSSStyleSheet;Te.replaceSync(".workspace-settings-tab header{padding:0 0 6px}.workspace-settings-tab header > h1{font-size:18px;font-weight:normal;margin:0;padding-bottom:3px}.workspace-settings-tab .settings-content{overflow-y:auto;overflow-x:hidden;margin:8px 8px 8px 0;padding:0 4px;flex:auto}.workspace-settings-tab .settings-container{width:100%;column-width:288px}.workspace-settings-tab .settings-tab.settings-container{column-width:308px}.workspace-settings-tab .settings-container-wrapper{position:absolute;top:31px;left:0;right:0;bottom:0;overflow:auto;padding-top:9px}.workspace-settings-tab .settings-tab.settings-content{margin:0;padding:0}.workspace-settings-tab .settings-tab p{margin:12px 0}.workspace-settings-tab p.folder-exclude-pattern{display:grid;align-items:center}.workspace-settings-tab p.folder-exclude-pattern > input{width:80%;margin-left:10px}.workspace-settings-tab .settings-tab .file-system-container{border-top:1px solid var(--sys-color-divider);padding:19px 0 10px;margin:20px 0}.workspace-settings-tab .settings-tab .file-system-header{display:flex;flex-direction:row;align-items:center}.workspace-settings-tab .settings-tab .file-system-name{font-weight:bold;flex:none;margin-right:10px;font-size:15px;overflow:hidden;text-overflow:ellipsis;max-width:70%}.workspace-settings-tab .settings-tab .file-system-path{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:auto}.workspace-settings-tab .settings-info-message{background-color:var(--sys-color-neutral-container);border-radius:5px;padding:10px;margin:20px 0}.workspace-settings-tab .settings-tab.settings-content.settings-container{column-width:initial;overflow:hidden;padding-right:10px;> devtools-button{margin:0 0 3px 5px}}\n/*# sourceURL=workspaceSettingsTab.css */\n");const Ee={workspace:"Workspace",mappingsAreInferredAutomatically:"Mappings are inferred automatically.",addFolder:"Add folder…",folderExcludePattern:"Folder exclude pattern",remove:"Remove"},Me=a.i18n.registerUIStrings("models/persistence/WorkspaceSettingsTab.ts",Ee),je=a.i18n.getLocalizedString.bind(void 0,Me);class We extends h.Widget.VBox{containerElement;fileSystemsListContainer;elementByPath;mappingViewByPath;constructor(){super(),this.element.setAttribute("jslog",`${p.pane("workspace")}`),this.element.classList.add("workspace-settings-tab");const e=this.element.createChild("header");h.UIUtils.createTextChild(e.createChild("h1"),je(Ee.workspace)),this.containerElement=this.element.createChild("div","settings-container-wrapper").createChild("div","settings-tab settings-content settings-container"),M.instance().addEventListener(j.FileSystemAdded,(e=>this.fileSystemAdded(e.data)),this),M.instance().addEventListener(j.FileSystemRemoved,(e=>this.fileSystemRemoved(e.data)),this);const t=this.createFolderExcludePatternInput();t.classList.add("folder-exclude-pattern"),this.containerElement.appendChild(t);const s=this.containerElement.createChild("div","settings-info-message");h.UIUtils.createTextChild(s,je(Ee.mappingsAreInferredAutomatically)),this.fileSystemsListContainer=this.containerElement.createChild("div","");const r=h.UIUtils.createTextButton(je(Ee.addFolder),this.addFileSystemClicked.bind(this),{jslogContext:"sources.add-folder-to-workspace"});this.containerElement.appendChild(r),this.setDefaultFocusedElement(r),this.elementByPath=new Map,this.mappingViewByPath=new Map;const n=M.instance().fileSystems();for(let e=0;e<n.length;++e)this.addItem(n[e])}wasShown(){super.wasShown(),this.registerCSSFiles([Te])}createFolderExcludePatternInput(){const e=document.createElement("p"),t=e.createChild("label");t.textContent=je(Ee.folderExcludePattern);const s=M.instance().workspaceFolderExcludePatternSetting(),r=h.UIUtils.createInput("","text",s.name);h.ARIAUtils.bindLabelToControl(t,r),e.appendChild(r);const n=h.UIUtils.bindInput(r,s.set.bind(s),(function(e){let t;try{t=new RegExp(e)}catch(e){}return{valid:Boolean(t),errorMessage:void 0}}),!1);return s.addChangeListener((()=>n.call(null,s.get()))),n(s.get()),e}addItem(e){if(!(e instanceof w))return;const t=_.instance().project();if(t&&M.instance().fileSystem(t.fileSystemPath())===e)return;const s=this.renderFileSystem(e);this.elementByPath.set(e.path(),s),this.fileSystemsListContainer.appendChild(s);const r=new Ie(e.path());this.mappingViewByPath.set(e.path(),r),r.element.classList.add("file-system-mapping-view"),r.show(s)}renderFileSystem(e){const t=e.path(),s=t.lastIndexOf("/"),r=t.substr(s+1),n=document.createElement("div");n.classList.add("file-system-container");const i=n.createChild("div","file-system-header"),o=i.createChild("div","file-system-name");o.textContent=r,h.ARIAUtils.markAsHeading(o,2);const a=i.createChild("div","file-system-path");a.textContent=t,h.Tooltip.Tooltip.install(a,t);const d=new h.Toolbar.Toolbar(""),l=new h.Toolbar.ToolbarButton(je(Ee.remove),"cross",void 0,"settings.remove-file-system");return l.addEventListener("Click",this.removeFileSystemClicked.bind(this,e)),d.appendToolbarItem(l),i.appendChild(d.element),n}removeFileSystemClicked(e){M.instance().removeFileSystem(e)}addFileSystemClicked(){M.instance().addFileSystem()}fileSystemAdded(e){this.addItem(e)}fileSystemRemoved(e){const t=this.mappingViewByPath.get(e.path());t&&(t.dispose(),this.mappingViewByPath.delete(e.path()));const s=this.elementByPath.get(e.path());s&&(this.elementByPath.delete(e.path()),s.remove())}}var Ae=Object.freeze({__proto__:null,WorkspaceSettingsTab:We});export{ve as Automapping,ke as EditFileSystemView,N as FileSystemWorkspaceBinding,x as IsolatedFileSystem,A as IsolatedFileSystemManager,X as NetworkPersistenceManager,me as Persistence,be as PersistenceActions,te as PersistenceUtils,y as PlatformFileSystem,Ae as WorkspaceSettingsTab};
