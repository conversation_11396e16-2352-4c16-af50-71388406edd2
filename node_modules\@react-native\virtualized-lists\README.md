# @react-native/virtualized-lists

[![Version][version-badge]][package]

## Installation

```
yarn add @react-native/virtualized-lists
```

*Note: We're using `yarn` to install deps. Feel free to change commands to use `npm` 3+ and `npx` if you like*

[version-badge]: https://img.shields.io/npm/v/@react-native/virtualized-lists?style=flat-square
[package]: https://www.npmjs.com/package/@react-native/virtualized-lists

## Testing

To run the tests in this package, run the following commands from the React Native root folder:

1. `yarn` to install the dependencies. You just need to run this once
2. `yarn jest packages/virtualized-lists`.
