import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/auth_service.dart';
import 'services/product_service.dart';
import 'services/store_service.dart';
import 'services/livestream_service.dart';
import 'services/order_service.dart';
import 'services/image_upload_service.dart';
import 'services/message_service.dart';
import 'theme/app_theme.dart';
import 'pages/auth/user_type_selection.dart';

class HadiHiaApp extends StatelessWidget {
  const HadiHiaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<AuthService>(create: (_) => AuthService()),
        ChangeNotifierProvider<ProductService>(create: (_) => ProductService()),
        ChangeNotifierProvider<StoreService>(create: (_) => StoreService()),
        ChangeNotifierProvider<LivestreamService>(
          create: (_) => LivestreamService(),
        ),
        ChangeNotifierProvider<OrderService>(create: (_) => OrderService()),
        ChangeNotifierProvider<MessageService>(create: (_) => MessageService()),
        ChangeNotifierProvider<ImageUploadService>(
          create: (_) => ImageUploadService(),
        ),
      ],
      child: MaterialApp(
        title: 'HadiHia',
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        home: const UserTypeSelectionPage(),
      ),
    );
  }
}
