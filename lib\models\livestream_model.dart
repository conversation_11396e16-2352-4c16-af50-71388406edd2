enum LiveStreamStatus { scheduled, live, ended }

class LiveStream {
  final String id;
  final String title;
  final String description;
  final String thumbnailUrl;
  final String sellerId;
  final String storeId;
  final String storeName;
  final String storeImageUrl;
  final LiveStreamStatus status;
  final DateTime startTime;
  final DateTime? endTime;
  final List<String> featuredProductIds;
  final int viewerCount;
  
  LiveStream({
    required this.id,
    required this.title,
    required this.description,
    required this.thumbnailUrl,
    required this.sellerId,
    required this.storeId,
    required this.storeName,
    required this.storeImageUrl,
    required this.status,
    required this.startTime,
    this.endTime,
    this.featuredProductIds = const [],
    this.viewerCount = 0,
  });
  
  factory LiveStream.fromJson(Map<String, dynamic> json) {
    return LiveStream(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      thumbnailUrl: json['thumbnailUrl'],
      sellerId: json['sellerId'],
      storeId: json['storeId'],
      storeName: json['storeName'],
      storeImageUrl: json['storeImageUrl'],
      status: _parseStatus(json['status']),
      startTime: DateTime.parse(json['startTime']),
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
      featuredProductIds: List<String>.from(json['featuredProductIds'] ?? []),
      viewerCount: json['viewerCount'] ?? 0,
    );
  }
  
  static LiveStreamStatus _parseStatus(String status) {
    switch (status) {
      case 'scheduled':
        return LiveStreamStatus.scheduled;
      case 'live':
        return LiveStreamStatus.live;
      case 'ended':
        return LiveStreamStatus.ended;
      default:
        return LiveStreamStatus.scheduled;
    }
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'thumbnailUrl': thumbnailUrl,
      'sellerId': sellerId,
      'storeId': storeId,
      'storeName': storeName,
      'storeImageUrl': storeImageUrl,
      'status': status.toString().split('.').last,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'featuredProductIds': featuredProductIds,
      'viewerCount': viewerCount,
    };
  }
  
  LiveStream copyWith({
    String? id,
    String? title,
    String? description,
    String? thumbnailUrl,
    String? sellerId,
    String? storeId,
    String? storeName,
    String? storeImageUrl,
    LiveStreamStatus? status,
    DateTime? startTime,
    DateTime? endTime,
    List<String>? featuredProductIds,
    int? viewerCount,
  }) {
    return LiveStream(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      sellerId: sellerId ?? this.sellerId,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      storeImageUrl: storeImageUrl ?? this.storeImageUrl,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      featuredProductIds: featuredProductIds ?? this.featuredProductIds,
      viewerCount: viewerCount ?? this.viewerCount,
    );
  }
}
