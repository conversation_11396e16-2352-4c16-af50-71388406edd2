import*as e from"../../../core/platform/platform.js";import*as t from"../../../models/trace/trace.js";import*as n from"./components/components.js";const i=t.Types.Timing.MicroSeconds(5e3);function s(e){return"CURSOR_TIMESTAMP_MARKER"===e.type||"ENTRY_SELECTED"===e.type}class r extends Event{overlay;action;static eventName="annotationoverlayactionsevent";constructor(e,t){super(r.eventName),this.overlay=e,this.action=t}}class o extends EventTarget{#e=new Map;#t={trace:{visibleWindow:null},charts:{main:null,network:null}};#n;#i;constructor(e){super(),this.#i=e.container,this.#n=e.charts}#s(e){if(e instanceof t.Handlers.ModelHandlers.Frames.TimelineFrame)return{startTime:e.startTime,endTime:e.endTime,duration:e.duration,selfTime:t.Types.Timing.MicroSeconds(0)};if(t.Types.TraceEvents.isSyntheticLayoutShift(e)){return{endTime:t.Types.Timing.MicroSeconds(e.ts+i),duration:i,startTime:e.ts,selfTime:t.Types.Timing.MicroSeconds(0)}}return t.Helpers.Timing.eventTimingsMicroSeconds(e)}#r(e){return e instanceof t.Handlers.ModelHandlers.Frames.TimelineFrame?"main":t.Types.TraceEvents.isNetworkTrackEntry(e)?"network":"main"}add(e){if(this.#e.has(e))return e;const t=this.overlaysOfType(e.type);return s(e)&&t[0]?(this.updateExisting(t[0],e),t[0]):(this.#e.set(e,null),e)}updateExisting(e,t){if(this.#e.has(e))for(const[n,i]of Object.entries(t)){e[n]=i}else console.error("Trying to update an overlay that does not exist.")}overlaysForEntry(e){const t=[];for(const[n]of this.#e)"entry"in n&&n.entry===e&&t.push(n);return t}removeOverlaysOfType(e){const t=Array.from(this.#e.keys()).filter((t=>t.type===e));for(const e of t)this.remove(e);return t.length}overlaysOfType(e){const t=[];function n(t){return t.type===e}for(const[e]of this.#e)n(e)&&t.push(e);return t}remove(e){const t=this.#e.get(e);t&&this.#i&&this.#i.removeChild(t),this.#e.delete(e)}updateChartDimensions(e,t){this.#t.charts[e]=t}updateVisibleWindow(e){this.#t.trace.visibleWindow=e}reset(){this.#i&&(this.#i.innerHTML=""),this.#e.clear(),this.#t.trace.visibleWindow=null,this.#t.charts.main=null,this.#t.charts.network=null}update(){const e=[];for(const[t,n]of this.#e){const i=n||this.#o(t);n?this.#a(t,i):(this.#e.set(t,i),this.#i.appendChild(i)),this.#l(t,i),"TIME_RANGE"===t.type&&e.push(t)}e.length>1&&this.#h(e)}#h(e){const n=e.toSorted(((e,t)=>e.bounds.min-t.bounds.min)),i=new Map;for(let e=0;e<n.length;e++){const s=n[e],r=[];for(let i=e+1;i<n.length;i++){const e=n[i];if(!t.Helpers.Timing.boundsIncludeTimeRange({bounds:s.bounds,timeRange:e.bounds}))break;r.push(e)}i.set(s,r)}for(const[e,t]of i){const n=this.#e.get(e);if(!n)continue;let i=1;n.getAttribute("class")?.includes("overlap-")&&(i=0),t.forEach((e=>{const t=this.#e.get(e);t?.classList.add("overlap-"+i++)}))}}#l(n,i){switch(n.type){case"ENTRY_SELECTED":this.entryIsVisibleOnChart(n.entry)?(i.style.visibility="visible",this.#c(n,i)):i.style.visibility="hidden";break;case"TIME_RANGE":{this.#y(n,i);const e=i.querySelector("devtools-time-range-overlay");e&&e.afterOverlayUpdate();break}case"ENTRY_LABEL":if(this.entryIsVisibleOnChart(n.entry)){i.style.visibility="visible";const e=this.#d(n,i),t=i.querySelector("devtools-entry-label-overlay");t&&e?t.entryLabelParams=e:(i.style.visibility="hidden",console.error("Cannot calculate entry width and height values required to draw a label overlay."))}else i.style.visibility="hidden";break;case"TIMESPAN_BREAKDOWN":{this.#m(n,i);const e=i.querySelector("devtools-timespan-breakdown-overlay");e&&e.afterOverlayUpdate();break}case"CURSOR_TIMESTAMP_MARKER":{const{visibleWindow:e}=this.#t.trace;e&&t.Helpers.Timing.timestampIsInBounds(e,n.timestamp)?(i.style.visibility="visible",this.#v(n,i)):i.style.visibility="hidden";break}default:e.TypeScriptUtilities.assertNever(n,`Unknown overlay: ${JSON.stringify(n)}`)}}#v(e,t){const n=this.#u("main",e.timestamp);t.style.left=`${n}px`}#m(e,t){const i=t?.querySelector("devtools-timespan-breakdown-overlay"),s=i?.shadowRoot,r=s?.querySelectorAll(".timespan-breakdown-overlay-section");if(0===e.sections.length)return;const o=this.#u("main",e.sections[0].bounds.min),a=this.#u("main",e.sections[e.sections.length-1].bounds.max);if(null===o||null===a)return;const l=a-o;if(t.style.left=`${o}px`,t.style.width=`${l}px`,r?.length===e.sections.length){let t=0,i=!1;for(const s of e.sections){const e=this.#u("main",s.bounds.min),o=this.#u("main",s.bounds.max);if(null===e||null===o)return;const a=o-e,l=r[t],h=this.#t.charts.network?.heightPixels??0;l.style.left=`${e}px`,l.style.width=`${a}px`;const c=i?h+n.TimespanBreakdownOverlay.TimespanBreakdownOverlay.TIMESPAN_BREAKDOWN_OVERLAY_STAGGER_PX:h;l.style.height=`${c}px`,t++,i=!i}}}#y(e,t){const n=this.#u("main",e.bounds.min),i=this.#u("main",e.bounds.max);if(null===n||null===i)return;const s=i-n;t.style.left=`${n}px`,t.style.width=`${s}px`}#d(e,t){const i=this.#r(e.entry),s=this.xPixelForEventOnChart(e.entry),r=this.yPixelForEventOnChart(e.entry),{endTime:o}=this.#s(e.entry),a=this.#u(i,o),l=this.pixelHeightForEventOnChart(e.entry)??0;if(null===s||null===r||null===a)return null;const h=a-s,c=Math.max(2,h),y=this.#t.charts.network?.heightPixels??0,d=this.networkChartOffsetHeight()-r,m=l+r-y,v=Math.max("main"===i?d:m,0);let u=r-n.EntryLabelOverlay.EntryLabelOverlay.LABEL_AND_CONNECTOR_HEIGHT;return"main"===i&&(u+=v),t.style.top=`${u}px`,t.style.left=`${s+c/2}px`,{height:l,width:c,cutOffEntryHeight:v,chart:i}}#c(e,t){const n=this.#r(e.entry);let i=this.xPixelForEventOnChart(e.entry),s=this.yPixelForEventOnChart(e.entry);if(null===i||null===s)return;const{endTime:r,duration:o}=this.#s(e.entry),a=this.#u(n,r);if(null===a)return;const l=this.pixelHeightForEventOnChart(e.entry)??0;let h=l;if(null===h)return;let c=a-i;if(!o){const t="main"===n?this.#n.mainProvider:this.#n.networkProvider,s="main"===n?this.#n.mainChart:this.#n.networkChart,r=t.indexForEvent?.(e.entry),o=s.getMarkerPixelsForEntryIndex(r??-1);o&&(i=o.x,c=o.width)}const y=Math.max(2,c);if(t.style.width=`${y}px`,"main"===n){const e=this.networkChartOffsetHeight(),n=s<e;h=n?Math.abs(s+h-e):h,t.classList.toggle("cut-off-top",n),n&&(s=s+l-h)}else{const e=this.#t.charts.network?.heightPixels??0,n=s+l>e;t.classList.toggle("cut-off-bottom",n),n&&(h=e-s)}t.style.height=`${h}px`,t.style.top=`${s}px`,t.style.left=`${i}px`}#o(e){const t=document.createElement("div");switch(t.classList.add("overlay-item",`overlay-type-${e.type}`),e.type){case"ENTRY_LABEL":{const i=new n.EntryLabelOverlay.EntryLabelOverlay(e.label,"main"===this.#r(e.entry));return i.addEventListener(n.EntryLabelOverlay.EmptyEntryLabelRemoveEvent.eventName,(()=>{this.dispatchEvent(new r(e,"Remove"))})),i.addEventListener(n.EntryLabelOverlay.EntryLabelChangeEvent.eventName,(t=>{const n=t.newLabel;e.label=n,this.dispatchEvent(new r(e,"Update"))})),t.appendChild(i),t}case"TIME_RANGE":{const i=new n.TimeRangeOverlay.TimeRangeOverlay;return i.duration=e.showDuration?e.bounds.range:null,i.label=e.label,i.canvasRect=this.#n.mainChart.canvasBoundingClientRect(),t.appendChild(i),t}case"TIMESPAN_BREAKDOWN":{const i=new n.TimespanBreakdownOverlay.TimespanBreakdownOverlay;return i.sections=e.sections,i.canvasRect=this.#n.mainChart.canvasBoundingClientRect(),t.appendChild(i),t}default:return t}}#a(t,n){switch(t.type){case"ENTRY_SELECTED":case"ENTRY_LABEL":case"CURSOR_TIMESTAMP_MARKER":break;case"TIME_RANGE":{const e=n.querySelector("devtools-time-range-overlay");e&&(e.duration=t.showDuration?t.bounds.range:null,e.label=t.label,e.canvasRect=this.#n.mainChart.canvasBoundingClientRect());break}case"TIMESPAN_BREAKDOWN":{const e=n.querySelector("devtools-timespan-breakdown-overlay");e&&(e.sections=t.sections,e.canvasRect=this.#n.mainChart.canvasBoundingClientRect());break}default:e.TypeScriptUtilities.assertNever(t,`Unexpected overlay ${t}`)}}entryIsVisibleOnChart(e){const t=this.#p(e),n=this.#E(e);return t&&n}#E(e){if(null===this.#t.trace.visibleWindow)return!1;const{startTime:n,endTime:i}=this.#s(e),s=t.Helpers.Timing.traceWindowFromMicroSeconds(n,i);return t.Helpers.Timing.boundsIncludeTimeRange({bounds:this.#t.trace.visibleWindow,timeRange:s})}#p(e){const t=this.#r(e),n=this.yPixelForEventOnChart(e);if(null===n)return!1;const i=this.pixelHeightForEventOnChart(e);if(!i)return!1;if("main"===t){if(!this.#t.charts.main?.heightPixels)return!1;const e=n-this.networkChartOffsetHeight();if(e+i<0)return!1;if(e>this.#t.charts.main.heightPixels)return!1}if("network"===t){if(!this.#t.charts.network)return!1;if(n<=-14)return!1;if(n>this.#t.charts.network.heightPixels)return!1}return!0}xPixelForEventOnChart(e){const t=this.#r(e),{startTime:n}=this.#s(e);return this.#u(t,n)}#u(e,t){if(null===this.#t.trace.visibleWindow)return console.error("Cannot calculate xPixel without visible trace window."),null;const n=this.#t.charts[e]?.widthPixels??null;if(!n)return console.error(`Cannot calculate xPixel without ${e} dimensions.`),null;const i=t-this.#t.trace.visibleWindow.min,s=this.#t.trace.visibleWindow.range;return Math.floor(i/s*n)}yPixelForEventOnChart(e){const t=this.#r(e),n="main"===t?this.#n.mainChart:this.#n.networkChart,i="main"===t?this.#n.mainProvider:this.#n.networkProvider,s=i.indexForEvent?.(e);if("number"!=typeof s)return null;const r=i.timelineData();if(null===r)return null;const o=r.entryLevels.at(s);if(void 0===o)return null;if(!n.levelIsVisible(o))return null;let a=n.levelToOffset(o)-(this.#t.charts[t]?.scrollOffsetPixels??0);return"main"===t&&(a+=this.networkChartOffsetHeight()),a}pixelHeightForEventOnChart(e){const t=this.#r(e),n="main"===t?this.#n.mainChart:this.#n.networkChart,i="main"===t?this.#n.mainProvider:this.#n.networkProvider,s=i.indexForEvent?.(e);if("number"!=typeof s)return null;const r=i.timelineData();if(null===r)return null;const o=r.entryLevels.at(s);return void 0===o?null:n.levelHeight(o)}networkChartOffsetHeight(){return null===this.#t.charts.network||0===this.#t.charts.network.heightPixels?0:this.#t.charts.network.allGroupsCollapsed?this.#t.charts.network.heightPixels:this.#t.charts.network.heightPixels+8}}var a=Object.freeze({__proto__:null,LAYOUT_SHIFT_SYNTHETIC_DURATION:i,overlayIsSingleton:s,AnnotationOverlayActionEvent:r,Overlays:o});export{a as Overlays};
