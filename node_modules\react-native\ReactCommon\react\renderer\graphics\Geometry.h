/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#pragma once

#include <react/renderer/graphics/Float.h>
#include <react/renderer/graphics/Point.h>
#include <react/renderer/graphics/Rect.h>
#include <react/renderer/graphics/RectangleCorners.h>
#include <react/renderer/graphics/RectangleEdges.h>
#include <react/renderer/graphics/Size.h>
#include <react/renderer/graphics/Vector.h>

#warning \
    "The Geometry.h file is deprecated and will be removed in the next version of React Native. Please update your #include and #import statements to use the specific files. For example, if you imported Geometry.h to use Float.h, replase the #include <react/renderer/graphics/Geometry.h> with #include <react/renderer/graphics/Float.h>"
