import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/product_model.dart';
import '../../services/auth_service.dart';
import '../../services/product_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/image_upload_widget.dart';
import 'seller_main.dart';

class AddProductFormPage extends StatefulWidget {
  const AddProductFormPage({super.key});

  @override
  State<AddProductFormPage> createState() => _AddProductFormPageState();
}

class _AddProductFormPageState extends State<AddProductFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();

  String _selectedCategory = 'Electronics';
  String? _uploadedImageUrl;
  bool _isLoading = false;

  final List<String> _categories = [
    'Electronics',
    'Clothing',
    'Home & Garden',
    'Sports',
    'Books',
    'Beauty',
    'Toys',
    'Food',
    'Other',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add New Product'),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'Product Details',
                style: AppTextStyles.heading2.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Fill in the information below to add your product',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 32),

              // Product Image Upload
              Text(
                'Product Image',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ImageUploadWidget(
                onImageUploaded: (imageUrl) {
                  setState(() {
                    _uploadedImageUrl = imageUrl;
                  });
                },
                height: 200,
                placeholder: 'Upload a high-quality product image',
                required: true,
              ),
              const SizedBox(height: 24),

              // Product Name
              _buildTextField(
                controller: _nameController,
                label: 'Product Name',
                hint: 'Enter product name',
                icon: Icons.shopping_bag_outlined,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a product name';
                  }
                  if (value.trim().length < 3) {
                    return 'Product name must be at least 3 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Product Description
              _buildTextField(
                controller: _descriptionController,
                label: 'Description',
                hint: 'Describe your product',
                icon: Icons.description_outlined,
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a product description';
                  }
                  if (value.trim().length < 10) {
                    return 'Description must be at least 10 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Price and Category Row
              Row(
                children: [
                  // Price
                  Expanded(
                    flex: 2,
                    child: _buildTextField(
                      controller: _priceController,
                      label: 'Price (\$)',
                      hint: '0.00',
                      icon: Icons.attach_money,
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a price';
                        }
                        final price = double.tryParse(value);
                        if (price == null) {
                          return 'Please enter a valid number';
                        }
                        if (price <= 0) {
                          return 'Price must be greater than 0';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Category
                  Expanded(flex: 3, child: _buildCategoryDropdown()),
                ],
              ),
              const SizedBox(height: 32),

              // Add Product Button
              _buildAddProductButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int maxLines = 1,
    void Function(String)? onChanged,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      validator: validator,
      keyboardType: keyboardType,
      maxLines: maxLines,
      onChanged: onChanged,
    );
  }

  Widget _buildCategoryDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCategory,
      decoration: InputDecoration(
        labelText: 'Category',
        prefixIcon: const Icon(Icons.category_outlined),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      items:
          _categories.map((category) {
            return DropdownMenuItem<String>(
              value: category,
              child: Text(category),
            );
          }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedCategory = value;
          });
        }
      },
    );
  }

  Widget _buildAddProductButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleAddProduct,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child:
            _isLoading
                ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
                : Text(
                  'Add Product',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
      ),
    );
  }

  Future<void> _handleAddProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final productService = Provider.of<ProductService>(
        context,
        listen: false,
      );

      final user = authService.currentUser;
      if (user == null) {
        throw Exception('Please log in to add products');
      }

      // Check if image is uploaded
      if (_uploadedImageUrl == null || _uploadedImageUrl!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload a product image.'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
        return;
      }

      // Ensure we have a valid seller ID from Supabase auth
      final sellerId = user.id;
      if (sellerId.isEmpty) {
        throw Exception('Invalid user authentication. Please log in again.');
      }

      debugPrint('Creating product for seller ID: $sellerId');
      debugPrint(
        'Product details: ${_nameController.text.trim()}, ${_priceController.text.trim()}',
      );

      // Create the product object
      final newProduct = Product(
        id: '', // Will be set by Supabase
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text.trim()),
        imageUrl: _uploadedImageUrl!,
        category: _selectedCategory,
        sellerId: sellerId, // Use the validated seller ID
        createdAt: DateTime.now(),
      );

      debugPrint('Attempting to add product to Supabase...');

      // Add product to Supabase with enhanced error handling
      try {
        await productService.addProduct(newProduct);
        debugPrint('Product added successfully to Supabase');
      } catch (supabaseError) {
        debugPrint('=== SUPABASE INSERT ERROR ===');
        debugPrint('Error Type: ${supabaseError.runtimeType}');
        debugPrint('Error Message: $supabaseError');
        debugPrint('Product Data: ${newProduct.toJson()}');
        debugPrint('============================');
        rethrow; // Re-throw to be caught by outer catch block
      }

      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Product added successfully! 🎉'),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
        ),
      );

      // Navigate to seller home page
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const SellerMainPage()),
        (route) => false,
      );
    } catch (e) {
      if (!mounted) return;

      // Enhanced error handling with detailed Supabase error information
      String errorMessage = 'Failed to add product';
      String debugInfo = '';

      debugPrint('=== ADD PRODUCT ERROR DETAILS ===');
      debugPrint('Error Type: ${e.runtimeType}');
      debugPrint('Error Message: $e');
      debugPrint('Stack Trace: ${StackTrace.current}');
      debugPrint('================================');

      // Parse different types of errors for user-friendly messages
      if (e.toString().contains('duplicate key') ||
          e.toString().contains('unique constraint')) {
        errorMessage = 'A product with this name already exists';
        debugInfo = 'Duplicate key violation';
      } else if (e.toString().contains('foreign key') ||
          e.toString().contains('seller_id')) {
        errorMessage = 'Invalid seller account. Please log in again';
        debugInfo = 'Foreign key constraint violation';
      } else if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        errorMessage = 'Network error. Please check your connection';
        debugInfo = 'Network connectivity issue';
      } else if (e.toString().contains('auth') ||
          e.toString().contains('authentication')) {
        errorMessage = 'Authentication error. Please log in again';
        debugInfo = 'Authentication failure';
      } else if (e.toString().contains('permission') ||
          e.toString().contains('policy')) {
        errorMessage =
            'Permission denied. Please check your account permissions';
        debugInfo = 'RLS policy violation';
      } else if (e.toString().contains('not null') ||
          e.toString().contains('required')) {
        errorMessage = 'Missing required information. Please fill all fields';
        debugInfo = 'Required field validation error';
      } else {
        errorMessage = 'Unexpected error occurred. Please try again';
        debugInfo = 'General exception';
      }

      // Log the detailed error for debugging
      debugPrint('User-friendly error: $errorMessage');
      debugPrint('Debug info: $debugInfo');
      debugPrint('Original error: $e');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(errorMessage),
              const SizedBox(height: 4),
              Text(
                'Error details: ${e.toString().length > 100 ? '${e.toString().substring(0, 100)}...' : e.toString()}',
                style: const TextStyle(fontSize: 12, color: Colors.white70),
              ),
            ],
          ),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(
            seconds: 6,
          ), // Longer duration for detailed error
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: _handleAddProduct,
          ),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
