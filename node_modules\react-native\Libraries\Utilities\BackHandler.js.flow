/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow
 */

'use strict';

export type BackPressEventName = 'backPress' | 'hardwareBackPress';

type TBackHandler = {
  exitApp(): void,
  addEventListener(
    eventName: BackPressEventName,
    handler: () => ?boolean,
  ): {remove: () => void, ...},
};

declare const BackHandler: TBackHandler;

declare export default typeof BackHandler;
