import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

class AppTextStyles {
  // Headings
  static TextStyle get heading1 => GoogleFonts.cairo(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static TextStyle get heading2 => GoogleFonts.cairo(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static TextStyle get heading3 => GoogleFonts.cairo(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  // Body text
  static TextStyle get bodyLarge => GoogleFonts.tajawal(
    fontSize: 16,
    color: AppColors.textPrimary,
  );

  static TextStyle get bodyMedium => GoogleFonts.tajawal(
    fontSize: 14,
    color: AppColors.textPrimary,
  );

  static TextStyle get bodySmall => GoogleFonts.tajawal(
    fontSize: 12,
    color: AppColors.textSecondary,
  );

  // Button text
  static TextStyle get buttonText => GoogleFonts.cairo(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.textLight,
  );

  // Price text
  static TextStyle get priceText => GoogleFonts.cairo(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );

  // Live indicator text
  static TextStyle get liveText => GoogleFonts.cairo(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );
}
