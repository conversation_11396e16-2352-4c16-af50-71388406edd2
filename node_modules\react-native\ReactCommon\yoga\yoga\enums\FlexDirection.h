/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// @generated by enums.py
// clang-format off
#pragma once

#include <cstdint>
#include <yoga/YGEnums.h>
#include <yoga/enums/YogaEnums.h>

namespace facebook::yoga {

enum class FlexDirection : uint8_t {
  Column = YGFlexDirectionColumn,
  ColumnReverse = YGFlexDirectionColumnReverse,
  Row = YGFlexDirectionRow,
  RowReverse = YGFlexDirectionRowReverse,
};

template <>
constexpr int32_t ordinalCount<FlexDirection>() {
  return 4;
}

constexpr FlexDirection scopedEnum(YGFlexDirection unscoped) {
  return static_cast<FlexDirection>(unscoped);
}

constexpr YGFlexDirection unscopedEnum(FlexDirection scoped) {
  return static_cast<YGFlexDirection>(scoped);
}

inline const char* toString(FlexDirection e) {
  return YGFlexDirectionToString(unscopedEnum(e));
}

} // namespace facebook::yoga
