enum UserType { customer, seller }

class User {
  final String id;
  final String name;
  final String email;
  final String profileImage;
  final UserType userType;
  final double walletBalance;

  // Seller-specific fields
  final String? storeId;
  final String? storeName;
  final String? storeDescription;
  final String? storeImageUrl;
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.profileImage,
    required this.userType,
    this.walletBalance = 0.0,
    this.storeId,
    this.storeName,
    this.storeDescription,
    this.storeImageUrl,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      profileImage: json['profile_image'] ?? json['profileImage'],
      userType:
          (json['user_type'] ?? json['userType']) == 'seller'
              ? UserType.seller
              : UserType.customer,
      walletBalance:
          (json['wallet_balance'] ?? json['walletBalance'] ?? 0.0).toDouble(),
      storeId: json['store_id'] ?? json['storeId'],
      storeName: json['store_name'] ?? json['storeName'],
      storeDescription: json['store_description'] ?? json['storeDescription'],
      storeImageUrl: json['store_image_url'] ?? json['storeImageUrl'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : (json['createdAt'] != null
                  ? DateTime.parse(json['createdAt'])
                  : DateTime.now()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'profile_image': profileImage,
      'user_type': userType == UserType.seller ? 'seller' : 'customer',
      'wallet_balance': walletBalance,
      'store_id': storeId,
      'store_name': storeName,
      'store_description': storeDescription,
      'store_image_url': storeImageUrl,
      'created_at': createdAt.toIso8601String(),
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? profileImage,
    UserType? userType,
    double? walletBalance,
    String? storeId,
    String? storeName,
    String? storeDescription,
    String? storeImageUrl,
    DateTime? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      profileImage: profileImage ?? this.profileImage,
      userType: userType ?? this.userType,
      walletBalance: walletBalance ?? this.walletBalance,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      storeDescription: storeDescription ?? this.storeDescription,
      storeImageUrl: storeImageUrl ?? this.storeImageUrl,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
