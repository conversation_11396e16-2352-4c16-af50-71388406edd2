enum UserType { customer, seller }

class User {
  final String id;
  final String name;
  final String email;
  final String profileImage;
  final UserType userType;
  final double walletBalance;

  // Seller-specific fields
  final String? storeId;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.profileImage,
    required this.userType,
    this.walletBalance = 0.0,
    this.storeId,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      profileImage: json['profileImage'],
      userType:
          json['userType'] == 'seller' ? UserType.seller : UserType.customer,
      walletBalance: json['walletBalance'] ?? 0.0,
      storeId: json['storeId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'profileImage': profileImage,
      'userType': userType == UserType.seller ? 'seller' : 'customer',
      'walletBalance': walletBalance,
      'storeId': storeId,
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? profileImage,
    UserType? userType,
    double? walletBalance,
    String? storeId,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      profileImage: profileImage ?? this.profileImage,
      userType: userType ?? this.userType,
      walletBalance: walletBalance ?? this.walletBalance,
      storeId: storeId ?? this.storeId,
    );
  }
}
