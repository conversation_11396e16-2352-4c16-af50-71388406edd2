# HadiHia Seller Chat Feature

This document provides an overview of the new chat feature added to the HadiHia seller interface.

## Overview

The chat feature allows sellers to communicate with customers directly within the app. Sellers can view and respond to messages from customers, send product information, and create special offers.

## Features

### Navigation
- A new "Messages" tab has been added to the bottom navigation bar
- The Messages icon shows a badge with the number of unread messages
- When clicked, it navigates to the SellerChatPage

### SellerChatPage
- Displays a list of all conversations with customers
- Shows customer name, profile picture, last message, and timestamp
- Indicates unread messages with bold text and a badge
- Includes tabs for "All Messages" and "Unread" messages
- Search functionality to find specific conversations

### Chat Room (SellerChatDetailPage)
- Real-time messaging interface with colored message bubbles
- Blue bubbles for seller messages, grey for customer messages
- Shows "typing..." indicator when customer is typing
- Displays read status for messages
- Automatically scrolls to the bottom when new messages arrive

### Message Types
Sellers can send various types of content:
- Text messages
- Product information (with image and price)
- Special offers
- Order information

### Additional Features
- Customer information sheet with order history and rating
- Ability to mark conversations as read
- Responsive design that works on all screen sizes

## How to Use

1. **Accessing the Chat Feature**
   - Tap the "Messages" icon in the bottom navigation bar

2. **Viewing Conversations**
   - All conversations are listed on the main chat page
   - Use the tabs to filter between all messages and unread messages
   - Use the search bar to find specific conversations

3. **Sending Messages**
   - Tap on a conversation to open the chat room
   - Type your message in the input field at the bottom
   - Tap the send button or press enter to send

4. **Sending Product Information**
   - Tap the "+" button next to the input field
   - Select "Product" from the attachment options
   - Choose a product from the list
   - Tap "Send Product" to share the product details

5. **Creating Special Offers**
   - Tap the "+" button next to the input field
   - Select "Offer" from the attachment options
   - Enter the discount percentage and offer message
   - Tap "Send Offer" to share the special offer

## Implementation Notes

- The chat feature uses a mock message service for demonstration purposes
- In a production environment, this would be connected to a real-time backend like Supabase
- The UI is designed to be consistent with the rest of the HadiHia app
- Animations are included for a more engaging user experience

## Future Enhancements

- Voice messages
- Image and file sharing
- Group chats for team selling
- Automated responses and chatbots
- Analytics for message response times
