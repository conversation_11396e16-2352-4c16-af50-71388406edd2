import 'package:flutter/material.dart';
import '../models/product_model.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../pages/customer/product_details_page.dart';

class ProductCard extends StatelessWidget {
  final Product product;
  final VoidCallback? onTap;

  const ProductCard({super.key, required this.product, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          onTap ??
          () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ProductDetailsPage(product: product),
              ),
            );
          },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13), // 0.05 opacity
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        // Use LayoutBuilder to ensure the card fits within its constraints
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Calculate the image height based on the available width
            // to maintain the aspect ratio
            final imageHeight = constraints.maxWidth;
            // Calculate the remaining height for the content
            final contentMaxHeight = constraints.maxHeight - imageHeight;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product Image with Like Button - Fixed height based on width
                SizedBox(
                  height: imageHeight,
                  child: Stack(
                    children: [
                      // Product Image
                      Positioned.fill(
                        child: ClipRRect(
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(16),
                          ),
                          child: _buildProductImage(product.imageUrl),
                        ),
                      ),

                      // Like Button
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.favorite_border,
                            color: AppColors.primary,
                            size: 20,
                          ),
                        ),
                      ),

                      // Rating
                      Positioned(
                        bottom: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withAlpha(128), // 0.5 opacity
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.star,
                                color: Colors.amber,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '4.5',
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Product Info - Flexible height with constraints
                Flexible(
                  child: Container(
                    constraints: BoxConstraints(maxHeight: contentMaxHeight),
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Product Name
                          Text(
                            product.name,
                            style: AppTextStyles.bodyLarge.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: 14, // Slightly smaller font
                            ),
                            maxLines: 1, // Reduced from 2 to 1 to save space
                            overflow: TextOverflow.ellipsis,
                          ),

                          const SizedBox(height: 4), // Reduced spacing
                          // Price
                          Text(
                            '${product.price.toStringAsFixed(2)} MAD',
                            style: AppTextStyles.priceText,
                          ),

                          const SizedBox(height: 4), // Reduced spacing
                          // Seller Info
                          if (product.sellerName != null)
                            Row(
                              children: [
                                _buildSellerAvatar(product.sellerImage),
                                const SizedBox(width: 4), // Reduced spacing
                                Expanded(
                                  child: Text(
                                    product.sellerName!,
                                    style: AppTextStyles.bodySmall.copyWith(
                                      fontSize: 10, // Smaller font
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  // Helper method to build product image with fallback for invalid URLs
  Widget _buildProductImage(String? imageUrl) {
    // Check if the URL is null, empty, or invalid
    if (imageUrl == null ||
        imageUrl.isEmpty ||
        !Uri.parse(imageUrl).isAbsolute) {
      return Container(
        color: Colors.grey[200],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/placeholder.png',
                width: 60,
                height: 60,
                fit: BoxFit.contain,
              ),
              const SizedBox(height: 8),
              Text(
                'No Image',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
        ),
      );
    }

    // If URL is valid, try to load the image
    return Image.network(
      imageUrl,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        // Handle 404 or other loading errors
        return Container(
          color: Colors.grey[200],
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/images/placeholder.png',
                  width: 60,
                  height: 60,
                  fit: BoxFit.contain,
                ),
                const SizedBox(height: 8),
                Text(
                  'Image Error',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
        );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          color: Colors.grey[200],
          child: Center(
            child: CircularProgressIndicator(
              color: AppColors.primary,
              value:
                  loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
            ),
          ),
        );
      },
    );
  }

  // Helper method to build seller avatar with fallback for invalid URLs
  Widget _buildSellerAvatar(String? imageUrl) {
    // Check if the URL is null, empty, or invalid
    if (imageUrl == null ||
        imageUrl.isEmpty ||
        !Uri.parse(imageUrl).isAbsolute) {
      return CircleAvatar(
        radius: 8,
        backgroundColor: Colors.grey[300],
        child: Icon(Icons.store, size: 10, color: Colors.grey[700]),
      );
    }

    // If URL is valid, try to load the image
    return CircleAvatar(
      radius: 8,
      backgroundImage: NetworkImage(imageUrl),
      onBackgroundImageError: (exception, stackTrace) {
        // This is called when the image fails to load
      },
      child:
          null, // This will be ignored if the backgroundImage loads successfully
    );
  }
}
