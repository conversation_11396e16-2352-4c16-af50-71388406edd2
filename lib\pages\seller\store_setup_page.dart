import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/auth_service.dart';
import '../../services/store_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import 'seller_main.dart';

class StoreSetupPage extends StatefulWidget {
  const StoreSetupPage({super.key});

  @override
  State<StoreSetupPage> createState() => _StoreSetupPageState();
}

class _StoreSetupPageState extends State<StoreSetupPage> {
  final _formKey = GlobalKey<FormState>();
  final _storeNameController = TextEditingController();
  final _storeDescriptionController = TextEditingController();
  final _storeImageController = TextEditingController();

  @override
  void dispose() {
    _storeNameController.dispose();
    _storeDescriptionController.dispose();
    _storeImageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Your Store'),
        centerTitle: true,
        backgroundColor: AppColors.secondary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Welcome to HadiHia!', style: AppTextStyles.heading1),
              const SizedBox(height: 8),
              Text(
                'Let\'s set up your store to start selling your products.',
                style: AppTextStyles.bodyLarge,
              ),

              const SizedBox(height: 32),

              // Store Image
              Center(
                child: Column(
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.secondary,
                          width: 2,
                        ),
                      ),
                      child:
                          _storeImageController.text.isNotEmpty
                              ? ClipRRect(
                                borderRadius: BorderRadius.circular(14),
                                child: Image.network(
                                  _storeImageController.text,
                                  fit: BoxFit.cover,
                                  errorBuilder:
                                      (context, error, stackTrace) =>
                                          const Icon(
                                            Icons.store,
                                            size: 60,
                                            color: AppColors.secondary,
                                          ),
                                ),
                              )
                              : const Icon(
                                Icons.store,
                                size: 60,
                                color: AppColors.secondary,
                              ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        // In a real app, this would open an image picker
                        setState(() {
                          _storeImageController.text =
                              'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80';
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: AppColors.secondary,
                        side: const BorderSide(color: AppColors.secondary),
                      ),
                      icon: const Icon(Icons.add_photo_alternate),
                      label: const Text('Upload Store Image'),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Store Name
              TextFormField(
                controller: _storeNameController,
                decoration: const InputDecoration(
                  labelText: 'Store Name',
                  hintText: 'Enter your store name',
                  prefixIcon: Icon(Icons.store),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a store name';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Store Description
              TextFormField(
                controller: _storeDescriptionController,
                decoration: const InputDecoration(
                  labelText: 'Store Description',
                  hintText: 'Tell customers about your store',
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a store description';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 32),

              // Create Store Button
              SizedBox(
                width: double.infinity,
                child: Consumer2<StoreService, AuthService>(
                  builder: (context, storeService, authService, _) {
                    return ElevatedButton(
                      onPressed:
                          storeService.isLoading
                              ? null
                              : () => _handleCreateStore(
                                context,
                                storeService,
                                authService,
                              ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.secondary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child:
                          storeService.isLoading
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                              : const Text('Create Store'),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleCreateStore(
    BuildContext context,
    StoreService storeService,
    AuthService authService,
  ) async {
    if (_formKey.currentState!.validate()) {
      if (_storeImageController.text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload a store image'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      final user = authService.currentUser;
      if (user == null) return;

      final store = await storeService.createStore(
        name: _storeNameController.text,
        description: _storeDescriptionController.text,
        imageUrl: _storeImageController.text,
        sellerId: user.id,
      );

      // Update user with store ID
      authService.updateStoreId(store.id);

      if (!context.mounted) return;

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const SellerMainPage()),
      );
    }
  }
}
