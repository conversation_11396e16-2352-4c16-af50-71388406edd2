import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as n from"../../core/platform/platform.js";import*as s from"../../core/sdk/sdk.js";import*as i from"../../models/persistence/persistence.js";import*as r from"../../models/text_utils/text_utils.js";import*as o from"../../models/workspace/workspace.js";import*as p from"../../ui/legacy/legacy.js";import*as a from"../../ui/legacy/components/quick_open/quick_open.js";const c={scriptSnippet:"Script snippet #{PH1}",linkedTo:"Linked to {PH1}"},l=t.i18n.registerUIStrings("panels/snippets/ScriptSnippetFileSystem.ts",c),d=t.i18n.getLocalizedString.bind(void 0,l);function u(t){return e.ParsedURL.ParsedURL.rawPathToEncodedPathString(t)}function g(t){return e.ParsedURL.ParsedURL.encodedPathToRawPathString(t)}class m extends i.PlatformFileSystem.PlatformFileSystem{lastSnippetIdentifierSetting;snippetsSetting;constructor(){super("snippet://","snippets"),this.lastSnippetIdentifierSetting=e.Settings.Settings.instance().createSetting("script-snippets-last-identifier",0),this.snippetsSetting=e.Settings.Settings.instance().createSetting("script-snippets",[])}initialFilePaths(){return this.snippetsSetting.get().map((e=>u(e.name)))}async createFile(e,t){const n=this.lastSnippetIdentifierSetting.get()+1;this.lastSnippetIdentifierSetting.set(n);const s=d(c.scriptSnippet,{PH1:n}),i=this.snippetsSetting.get();return i.push({name:s,content:""}),this.snippetsSetting.set(i),u(s)}async deleteFile(t){const n=g(e.ParsedURL.ParsedURL.substring(t,1)),s=this.snippetsSetting.get(),i=s.filter((e=>e.name!==n));return s.length!==i.length&&(this.snippetsSetting.set(i),!0)}async requestFileContent(t){const n=g(e.ParsedURL.ParsedURL.substring(t,1)),s=this.snippetsSetting.get().find((e=>e.name===n));return s?new r.ContentData.ContentData(s.content,!1,"text/javascript"):{error:`A snippet with name '${n}' was not found`}}async setFileContent(t,n,s){const i=g(e.ParsedURL.ParsedURL.substring(t,1)),r=this.snippetsSetting.get(),o=r.find((e=>e.name===i));return!!o&&(o.content=n,this.snippetsSetting.set(r),!0)}renameFile(t,n,s){const i=g(e.ParsedURL.ParsedURL.substring(t,1)),r=this.snippetsSetting.get(),o=r.find((e=>e.name===i));n=e.ParsedURL.ParsedURL.trim(n),o&&0!==n.length&&!r.find((e=>e.name===n))?(o.name=n,this.snippetsSetting.set(r),s(!0,n)):s(!1)}async searchInPath(e,t){const s=new RegExp(n.StringUtilities.escapeForRegExp(e),"i");return this.snippetsSetting.get().filter((e=>e.content.match(s))).map((e=>`snippet:///${u(e.name)}`))}mimeFromPath(e){return"text/javascript"}contentType(t){return e.ResourceType.resourceTypes.Script}tooltipForURL(t){return d(c.linkedTo,{PH1:g(e.ParsedURL.ParsedURL.sliceUrlToEncodedPathString(t,this.path().length))})}supportsAutomapping(){return!0}}async function S(t){if(!e.ParsedURL.schemeIs(t.url(),"snippet:"))return;const n=p.Context.Context.instance().flavor(s.RuntimeModel.ExecutionContext);if(!n)return;const i=n.runtimeModel,r=n.target().model(s.ConsoleModel.ConsoleModel);await t.requestContent(),t.commitWorkingCopy();const o=t.workingCopy();e.Console.Console.instance().show();const a=t.url(),c=await n.evaluate({expression:`${o}\n//# sourceURL=${a}`,objectGroup:"console",silent:!1,includeCommandLineAPI:!0,returnByValue:!1,generatePreview:!0,replMode:!0},!0,!0);if("exceptionDetails"in c&&c.exceptionDetails)return void r?.addMessage(s.ConsoleModel.ConsoleMessage.fromException(i,c.exceptionDetails,void 0,void 0,a));if(!("object"in c)||!c.object)return;const l=n.debuggerModel.scriptsForSourceURL(a);if(l.length<1)return;const d=l[l.length-1].scriptId,u={type:s.ConsoleModel.FrontendMessageType.Result,url:a,parameters:[c.object],executionContextId:n.id,scriptId:d};r?.addMessage(new s.ConsoleModel.ConsoleMessage(i,"javascript","info","",u))}function h(){const e=o.Workspace.WorkspaceImpl.instance().projectsForType(o.Workspace.projectTypes.FileSystem).find((e=>"snippets"===i.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.fileSystemType(e)));if(!e)throw new Error("Unable to find workspace project for the snippets file system");return e}var f=Object.freeze({__proto__:null,SnippetFileSystem:m,evaluateScriptSnippet:S,isSnippetsUISourceCode:function(t){return e.ParsedURL.schemeIs(t.url(),"snippet:")},isSnippetsProject:function(e){return e.type()===o.Workspace.projectTypes.FileSystem&&"snippets"===i.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.fileSystemType(e)},findSnippetsProject:h});const P={noSnippetsFound:"No snippets found.",run:"Run",snippet:"Snippet"},y=t.i18n.registerUIStrings("panels/snippets/SnippetsQuickOpen.ts",P),L=t.i18n.getLocalizedString.bind(void 0,y),F=t.i18n.getLazilyComputedLocalizedString.bind(void 0,y);let R;class U extends a.FilteredListWidget.Provider{snippets;constructor(){super("snippet"),this.snippets=[]}static instance(e={forceNew:null}){const{forceNew:t}=e;return R&&!t||(R=new U),R}selectItem(e,t){null!==e&&S(this.snippets[e])}notFoundText(e){return L(P.noSnippetsFound)}attach(){this.snippets=[...h().uiSourceCodes()]}detach(){this.snippets=[]}itemScoreAt(e,t){return t.length/this.snippets[e].name().length}itemCount(){return this.snippets.length}itemKeyAt(e){return this.snippets[e].name()}renderItem(e,t,n,s){n.textContent=this.snippets[e].name(),n.classList.add("monospace"),a.FilteredListWidget.FilteredListWidget.highlightRanges(n,t,!0)}}a.FilteredListWidget.registerProvider({prefix:"!",iconName:"exclamation",iconWidth:"20px",provider:()=>Promise.resolve(U.instance()),titlePrefix:F(P.run),titleSuggestion:F(P.snippet)});var x=Object.freeze({__proto__:null,SnippetsQuickOpen:U});export{f as ScriptSnippetFileSystem,x as SnippetsQuickOpen};
