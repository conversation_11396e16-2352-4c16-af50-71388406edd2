import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/livestream_model.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

class LivestreamCard extends StatelessWidget {
  final LiveStream livestream;
  final VoidCallback? onTap;

  const LivestreamCard({super.key, required this.livestream, this.onTap});

  @override
  Widget build(BuildContext context) {
    final isLive = livestream.status == LiveStreamStatus.live;

    return GestureDetector(
      onTap: onTap,
      child: Card(
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Livestream Thumbnail with Live Indicator
            Stack(
              children: [
                AspectRatio(
                  aspectRatio: 16 / 9,
                  child: CachedNetworkImage(
                    imageUrl: livestream.thumbnailUrl,
                    fit: BoxFit.cover,
                    placeholder:
                        (context, url) => const Center(
                          child: CircularProgressIndicator(
                            color: AppColors.primary,
                          ),
                        ),
                    errorWidget:
                        (context, url, error) => const Center(
                          child: Icon(Icons.error, color: AppColors.error),
                        ),
                  ),
                ),

                // Live Indicator or Scheduled Time
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: isLive ? AppColors.liveIndicator : Colors.black54,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (isLive) ...[
                          const Icon(
                            Icons.circle,
                            color: Colors.white,
                            size: 8,
                          ),
                          const SizedBox(width: 4),
                          Text('LIVE', style: AppTextStyles.liveText),
                          const SizedBox(width: 4),
                          Text(
                            '${livestream.viewerCount}',
                            style: AppTextStyles.liveText,
                          ),
                        ] else
                          Text(
                            _formatScheduledTime(livestream.startTime),
                            style: AppTextStyles.liveText,
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Livestream Title
                  Text(
                    livestream.title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Store Info
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 12,
                        backgroundImage: CachedNetworkImageProvider(
                          livestream.storeImageUrl,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          livestream.storeName,
                          style: AppTextStyles.bodySmall,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatScheduledTime(DateTime time) {
    final now = DateTime.now();
    final difference = time.difference(now);

    if (difference.inDays > 0) {
      return 'In ${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return 'In ${difference.inHours} hours';
    } else if (difference.inMinutes > 0) {
      return 'In ${difference.inMinutes} min';
    } else {
      return 'Starting soon';
    }
  }
}
