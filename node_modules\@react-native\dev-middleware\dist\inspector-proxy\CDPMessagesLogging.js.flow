/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

export type CDPMessageDestination =
  | "DebuggerToProxy"
  | "ProxyToDebugger"
  | "DeviceToProxy"
  | "ProxyToDevice";

declare export default class CDPMessagesLogging {
  constructor(): void;
  log(destination: CDPMessageDestination, message: string): void;
}
