import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

class ChatPage extends StatelessWidget {
  const ChatPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Dummy chat data
    final List<Map<String, dynamic>> chats = [
      {
        'name': 'Fatima\'s Crafts',
        'lastMessage': 'Your order has been shipped!',
        'time': '10:30 AM',
        'image': 'https://randomuser.me/api/portraits/women/1.jpg',
        'unread': 2,
      },
      {
        'name': 'Moroccan Treasures',
        'lastMessage': 'Thank you for your purchase!',
        'time': 'Yesterday',
        'image': 'https://randomuser.me/api/portraits/men/3.jpg',
        'unread': 0,
      },
      {
        'name': 'Marrakech Market',
        'lastMessage': 'Do you have any questions about the spices?',
        'time': 'Yesterday',
        'image': 'https://randomuser.me/api/portraits/women/4.jpg',
        'unread': 1,
      },
      {
        'name': 'Casablanca Fashion',
        'lastMessage': 'We have new items in stock!',
        'time': 'Monday',
        'image': 'https://randomuser.me/api/portraits/women/5.jpg',
        'unread': 0,
      },
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Chats'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Implement search
            },
          ),
        ],
      ),
      body:
          chats.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.chat_bubble_outline,
                      size: 64,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No chats yet',
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Start shopping to chat with sellers',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              )
              : ListView.separated(
                padding: const EdgeInsets.all(16),
                itemCount: chats.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final chat = chats[index];
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: CircleAvatar(
                      radius: 24,
                      backgroundImage: NetworkImage(chat['image']),
                    ),
                    title: Text(
                      chat['name'],
                      style: AppTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(
                      chat['lastMessage'],
                      style: AppTextStyles.bodyMedium.copyWith(
                        color:
                            chat['unread'] > 0
                                ? AppColors.textPrimary
                                : AppColors.textSecondary,
                        fontWeight:
                            chat['unread'] > 0
                                ? FontWeight.bold
                                : FontWeight.normal,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(chat['time'], style: AppTextStyles.bodySmall),
                        const SizedBox(height: 4),
                        if (chat['unread'] > 0)
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: const BoxDecoration(
                              color: AppColors.primary,
                              shape: BoxShape.circle,
                            ),
                            child: Text(
                              chat['unread'].toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    onTap: () {
                      showDialog(
                        context: context,
                        builder:
                            (context) => AlertDialog(
                              title: Text('Chat with ${chat['name']}'),
                              content: const Text(
                                'This is a placeholder for the chat interface. In a real app, this would show the actual chat conversation.',
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('Close'),
                                ),
                              ],
                            ),
                      );
                    },
                  );
                },
              ),
    );
  }
}
