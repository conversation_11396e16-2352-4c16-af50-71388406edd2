{"name": "@react-native/codegen", "version": "0.79.1", "description": "Code generation tools for React Native", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/react-native-codegen"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/react-native-codegen#readme", "keywords": ["code", "generation", "codegen", "tools", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">=18"}, "scripts": {"build": "yarn clean && node scripts/build.js --verbose", "clean": "<PERSON><PERSON><PERSON> lib", "prepare": "yarn run build"}, "files": ["lib"], "dependencies": {"glob": "^7.1.1", "hermes-parser": "0.25.1", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "yargs": "^17.6.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-async-to-generator": "^7.24.7", "@babel/plugin-transform-class-properties": "^7.25.4", "@babel/plugin-transform-destructuring": "^7.24.8", "@babel/plugin-transform-flow-strip-types": "^7.25.2", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7", "@babel/plugin-transform-object-rest-spread": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.8", "@babel/preset-env": "^7.25.3", "chalk": "^4.0.0", "hermes-estree": "0.25.1", "micromatch": "^4.0.4", "prettier": "2.8.8", "rimraf": "^3.0.2"}, "peerDependencies": {"@babel/core": "*"}}