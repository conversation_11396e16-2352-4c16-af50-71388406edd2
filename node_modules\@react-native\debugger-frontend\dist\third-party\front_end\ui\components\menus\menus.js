import*as e from"../../../core/platform/platform.js";import*as t from"../helpers/helpers.js";import*as o from"../render_coordinator/render_coordinator.js";import*as s from"../../lit-html/lit-html.js";import*as n from"../../visual_logging/visual_logging.js";import*as r from"../dialogs/dialogs.js";const i=new CSSStyleSheet;i.replaceSync(":host{border-radius:3px;width:fit-content;display:flex;align-items:center;background-color:var(--override-menu-background-color,var(--sys-color-cdt-base-container))}:host([has-open-dialog]){background-color:var(--override-menu-active-background-color,var(--sys-color-neutral-container))}#container{list-style-type:none;margin:0;padding:0;width:fit-content;display:block}#container:focus{outline:none}@keyframes slideIn{from{transform:var(--translate-dialog);opacity:0%}to{transform:none;opacity:100%}}\n/*# sourceURL=menu.css */\n");const l=new CSSStyleSheet;l.replaceSync(".menu-group-label{margin:4px 30px 4px 10px;font-size:12px;line-height:16px;position:relative;color:var(--sys-color-token-subtle);display:block}\n/*# sourceURL=menuGroup.css */\n");const d=new CSSStyleSheet;d.replaceSync('.menu-item{padding:4px 30px 4px calc(10px + var(--menu-checkmark-width));font-size:12px;line-height:16px;position:relative;display:block;color:var(--sys-color-on-surface)}.menu-item:focus{outline:none}:host(:not(:first-child)) .menu-item{border-top:var(--override-divider-line)}:host-context(devtools-menu-group) .menu-item{padding:4px 30px 4px 36px}.is-selected-item::before{content:"";position:absolute;left:10px;top:50%;transform:translateY(-50%);display:inline-block;mask-repeat:no-repeat;mask-position:center;width:calc(var(--menu-checkmark-width) - 10px);height:var(--menu-checkmark-height);mask-image:var(--selected-item-check);background:var(--sys-color-token-subtle)}:host(:hover:not(.prevents-close)) .menu-item,\n:host(:focus-visible:not(.prevents-close)) .menu-item{background:var(--sys-color-state-hover-on-subtle)}:host(:focus){outline:none}\n/*# sourceURL=menuItem.css */\n');const a=o.RenderCoordinator.RenderCoordinator.instance(),h=new URL("../../../Images/checkmark.svg",import.meta.url).toString();class c extends HTMLElement{static litTagName=s.literal`devtools-menu`;#e=this.attachShadow({mode:"open"});#t=this.#o.bind(this);#s=null;#n=!1;#r={origin:null,open:!1,position:"auto",showConnector:!1,showDivider:!1,showSelectedItem:!0,horizontalAlignment:"auto",getConnectorCustomXPosition:null};get origin(){return this.#r.origin}set origin(e){this.#r.origin=e,t.ScheduledRender.scheduleRender(this,this.#t)}get open(){return this.#r.open}set open(e){e!==this.open&&(this.#r.open=e,this.toggleAttribute("has-open-dialog",this.open),this.#i().setDialogVisible(this.open),t.ScheduledRender.scheduleRender(this,this.#t))}get position(){return this.#r.position}set position(e){this.#r.position=e,t.ScheduledRender.scheduleRender(this,this.#t)}get showConnector(){return this.#r.showConnector}set showConnector(e){this.#r.showConnector=e,t.ScheduledRender.scheduleRender(this,this.#t)}get showDivider(){return this.#r.showDivider}set showDivider(e){this.#r.showDivider=e,t.ScheduledRender.scheduleRender(this,this.#t)}get showSelectedItem(){return this.#r.showSelectedItem}set showSelectedItem(e){this.#r.showSelectedItem=e,t.ScheduledRender.scheduleRender(this,this.#t)}get horizontalAlignment(){return this.#r.horizontalAlignment}set horizontalAlignment(e){this.#r.horizontalAlignment=e,t.ScheduledRender.scheduleRender(this,this.#t)}get getConnectorCustomXPosition(){return this.#r.getConnectorCustomXPosition}set getConnectorCustomXPosition(e){this.#r.getConnectorCustomXPosition=e,t.ScheduledRender.scheduleRender(this,this.#t)}connectedCallback(){this.#e.adoptedStyleSheets=[i],a.write((()=>{this.style.setProperty("--selected-item-check",`url(${h})`),this.style.setProperty("--menu-checkmark-width",this.#r.showSelectedItem?"26px":"0px"),this.style.setProperty("--menu-checkmark-height",this.#r.showSelectedItem?"12px":"0px");const e=this.showDivider?"1px var(--divider-line) solid":"none";this.style.setProperty("--override-divider-line",e)}))}#i(){if(!this.#s)throw new Error("Dialog not found");return this.#s}async#l(){await a.write((()=>{this.setAttribute("has-open-dialog","has-open-dialog");const e=this.#e.querySelector("#container");e instanceof HTMLElement&&e.focus()}))}#d(){this.#a().focus()}#a(){const e=this.#e.querySelector("slot"),t=e?.assignedElements();let o=t[0];if(o instanceof HTMLSlotElement&&(o=o?.assignedElements()[0]),o instanceof p){const e=o.shadowRoot?.querySelector("slot");o=e?.assignedElements()[0]}if(o instanceof HTMLElement)return o;throw new Error("First item not found")}#h(e){const t=e.composedPath();if(e.stopPropagation(),t.find((e=>e instanceof HTMLInputElement)))return;const o=e.composedPath().find((e=>e instanceof u));o instanceof u&&this.#c(o)}#u(t){const o=t.key;t.stopImmediatePropagation();let s=t.target;const n=t.composedPath(),r="ArrowDown"===o||"ArrowRight"===o;return!this.#n&&r?(this.#d(),void(this.#n=!0)):this.#n||"ArrowUp"!==o?void((s instanceof u||(s=n.find((e=>e instanceof u)),s instanceof u))&&(e.KeyboardUtilities.keyIsArrowKey(o)?this.#p(o,s):"Home"===o?this.#m(s):"End"===o?this.#g():"Enter"===o||"Space"===t.code?this.#c(s):"Escape"===o&&(t.preventDefault(),this.#w()))):(this.#g(),void(this.#n=!0))}#c(e){""!==e.value&&(this.dispatchEvent(new m(e.value)),e.preventMenuCloseOnSelection||this.#w())}#p(e,t){let o=t;"ArrowDown"===e?(o=t.nextElementSibling,null===o&&t.parentElement instanceof p&&(o=this.#b(t))):"ArrowUp"===e&&(o=t.previousElementSibling,null===o&&t.parentElement instanceof p&&(o=this.#v(t))),o instanceof u&&o.focus()}#b(e){const t=e.parentElement;if(!(t instanceof p))return null;const o=t.nextElementSibling;if(o instanceof u)return o;if(!(o instanceof p))return null;for(const e of o.children)if(e instanceof u)return e;return null}#v(e){const t=e.parentElement;if(!(t instanceof p))return null;const o=t.previousElementSibling;return o instanceof u?o:o instanceof p&&o.lastElementChild instanceof u?o.lastElementChild:null}#m(e){let t=e;for(e.parentElement instanceof p&&(t=e.parentElement);t?.previousElementSibling;)t=t?.previousElementSibling;if(t instanceof u)t.focus();else for(const e of t.children)if(e instanceof u)return void e.focus()}#g(){const e=this.#a();let t=e;for(e.parentElement instanceof p&&(t=e.parentElement);t?.nextElementSibling;)t=t?.nextElementSibling;t instanceof u?t.focus():t instanceof p&&t.lastElementChild instanceof u&&t.lastElementChild.focus()}#w(e){e&&e.stopImmediatePropagation(),this.dispatchEvent(new g),this.#i().setDialogVisible(!1),this.#n=!1}async#o(){if(!t.ScheduledRender.isScheduledRender(this))throw new Error("Menu render was not scheduled");s.render(s.html`
      <${r.Dialog.Dialog.litTagName}
        @clickoutsidedialog=${this.#w}
        @forceddialogclose=${this.#w}
        .position=${this.position}
        .showConnector=${this.showConnector}
        .origin=${this.origin}
        .dialogShownCallback=${this.#l.bind(this)}
        .horizontalAlignment=${this.horizontalAlignment}
        .getConnectorCustomXPosition=${this.getConnectorCustomXPosition}
        on-render=${t.Directives.nodeRenderedCallback((e=>{this.#s=e}))}
        >
        <span id="container" role="menu" tabIndex="0" @keydown=${this.#u} jslog=${n.menu().track({resize:!0,keydown:"Escape"})}>
          <slot @click=${this.#h}>
          </slot>
        </span>
      </${r.Dialog.Dialog.litTagName}>
    `,this.#e,{host:this})}}class u extends HTMLElement{static litTagName=s.literal`devtools-menu-item`;#e=this.attachShadow({mode:"open"});#t=this.#o.bind(this);connectedCallback(){this.#e.adoptedStyleSheets=[d],this.tabIndex=0,this.setAttribute("role","menuitem")}#r={value:"",preventMenuCloseOnSelection:!1,selected:!1};get preventMenuCloseOnSelection(){return this.#r.preventMenuCloseOnSelection}set preventMenuCloseOnSelection(e){this.#r.preventMenuCloseOnSelection=e,t.ScheduledRender.scheduleRender(this,this.#t)}get value(){return this.#r.value}set value(e){this.#r.value=e,t.ScheduledRender.scheduleRender(this,this.#t)}get selected(){return this.#r.selected}set selected(e){this.#r.selected=e,t.ScheduledRender.scheduleRender(this,this.#t)}async#o(){if(!t.ScheduledRender.isScheduledRender(this))throw new Error("MenuItem render was not scheduled");s.render(s.html`
      <span class=${s.Directives.classMap({"menu-item":!0,"is-selected-item":this.selected,"prevents-close":this.preventMenuCloseOnSelection})}
      >
        <slot></slot>
      </span>
    `,this.#e,{host:this})}}class p extends HTMLElement{static litTagName=s.literal`devtools-menu-group`;#e=this.attachShadow({mode:"open"});#t=this.#o.bind(this);connectedCallback(){this.#e.adoptedStyleSheets=[l]}#r={name:null};get name(){return this.#r.name}set name(e){this.#r.name=e,t.ScheduledRender.scheduleRender(this,this.#t)}async#o(){if(!t.ScheduledRender.isScheduledRender(this))throw new Error("MenuGroup render was not scheduled");s.render(s.html`
      <span class="menu-group">
        <span class="menu-group-label">${this.name}</span>
        <slot></slot>
      </span>
    `,this.#e,{host:this})}}customElements.define("devtools-menu",c),customElements.define("devtools-menu-item",u),customElements.define("devtools-menu-group",p);class m extends Event{itemValue;static eventName="menuitemselected";constructor(e){super(m.eventName,{bubbles:!0,composed:!0}),this.itemValue=e}}class g extends Event{static eventName="menucloserequest";constructor(){super(g.eventName,{bubbles:!0,composed:!0})}}var w=Object.freeze({__proto__:null,Menu:c,MenuItem:u,MenuGroup:p,MenuItemSelectedEvent:m,MenuCloseRequest:g});const b=new CSSStyleSheet;b.replaceSync(":host{border:1px solid var(--sys-color-neutral-outline);border-radius:3px;width:fit-content;display:flex;align-items:center;background-color:var(--override-select-menu-background-color,var(--sys-color-cdt-base-container))}:host([has-open-dialog]){background-color:var(--override-select-menu-active-background-color,var(--sys-color-neutral-container))}button{background:none}#side-button{border:1px solid var(--override-select-menu-border,var(--sys-color-neutral-outline));border-radius:3px 0 0 3px;border-right:none;height:100%;position:relative;padding:var(--override-select-button-padding)}button:disabled{cursor:not-allowed}@keyframes slideIn{from{transform:var(--translate-dialog);opacity:0%}to{transform:none;opacity:100%}}\n/*# sourceURL=selectMenu.css */\n");const v=new CSSStyleSheet;v.replaceSync(':host{height:100%;width:100%;display:block}.show{display:block;font-size:12px;color:var(--sys-color-on-surface);height:100%;width:100%;border:none;border-radius:var(--override-select-menu-show-button-border-radius);padding:var(--override-select-menu-show-button-padding,1px 6px)}.show:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px}#button-label-wrapper{display:flex;justify-content:space-around;align-items:center}#label{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;width:fit-content;height:100%}#label[witharrow].single-arrow{padding:0}#label[witharrow]{padding:var(--override-select-menu-label-with-arrow-padding,0 10px 0 0);text-align:left}.single-arrow + span#arrow{margin:0}#arrow{mask-image:var(--deploy-menu-arrow);-webkit-mask-position-y:center;margin-left:5px;width:14px;flex-shrink:0;height:14px;display:inline-block;mask-repeat:no-repeat;background-color:var(--override-throttling-icon-and-text-color,var(--override-select-menu-arrow-color,var(--sys-color-on-surface)));transform:rotate(var(--arrow-angle));transform-origin:center;transition:200ms}.single-arrow{border-radius:0 3px 3px 0;border:1px solid var(--override-select-menu-border,var(--sys-color-neutral-outline));height:100%;aspect-ratio:1/1;padding:0;display:flex;justify-content:center;align-items:center}button[aria-expanded="true"] #arrow{transform:rotate(calc(var(--arrow-angle) + 180deg))}button{background:none}button[disabled]{color:var(--sys-color-state-disabled)}\n/*# sourceURL=selectMenuButton.css */\n');const S=o.RenderCoordinator.RenderCoordinator.instance(),f=new URL("../../../Images/triangle-down.svg",import.meta.url).toString();class y extends HTMLElement{static litTagName=s.literal`devtools-select-menu`;#e=this.attachShadow({mode:"open"});#t=this.#o.bind(this);#S=null;#f=!1;#r={buttonTitle:"",position:"bottom",horizontalAlignment:"auto",showArrow:!1,showConnector:!1,sideButton:!1,showDivider:!1,disabled:!1,showSelectedItem:!0,jslogContext:""};get buttonTitle(){return this.#r.buttonTitle}set buttonTitle(e){this.#r.buttonTitle=e,t.ScheduledRender.scheduleRender(this,this.#t)}get position(){return this.#r.position}set position(e){this.#r.position=e,t.ScheduledRender.scheduleRender(this,this.#t)}get horizontalAlignment(){return this.#r.horizontalAlignment}set horizontalAlignment(e){this.#r.horizontalAlignment=e,t.ScheduledRender.scheduleRender(this,this.#t)}get showConnector(){return this.#r.showConnector}set showConnector(e){this.#r.showArrow||(this.#r.showArrow=e),this.#r.showConnector=e,t.ScheduledRender.scheduleRender(this,this.#t)}get showArrow(){return this.#r.showArrow}set showArrow(e){this.#r.showArrow=e,t.ScheduledRender.scheduleRender(this,this.#t)}get sideButton(){return this.#r.sideButton}set sideButton(e){this.#r.sideButton=e,t.ScheduledRender.scheduleRender(this,this.#t)}get disabled(){return this.#r.disabled}set disabled(e){this.#r.disabled=e,t.ScheduledRender.scheduleRender(this,this.#t)}get showDivider(){return this.#r.showDivider}set showDivider(e){this.#r.showDivider=e,t.ScheduledRender.scheduleRender(this,this.#t)}get showSelectedItem(){return this.#r.showSelectedItem}set showSelectedItem(e){this.#r.showSelectedItem=e,t.ScheduledRender.scheduleRender(this,this.#t)}get jslogContext(){return this.#r.jslogContext}set jslogContext(e){this.#r.jslogContext=e,t.ScheduledRender.scheduleRender(this,this.#t)}connectedCallback(){this.#e.adoptedStyleSheets=[b]}#y(){if(!this.#S&&(this.#S=this.#e.querySelector("devtools-select-menu-button"),!this.#S))throw new Error("Arrow not found");return this.#S}#x(){this.#f=!0,this.setAttribute("has-open-dialog","has-open-dialog"),t.ScheduledRender.scheduleRender(this,this.#t)}click(){this.#y().click()}#R(){this.dispatchEvent(new C)}#C(){if(this.showConnector){const e=this.#y().getBoundingClientRect();return(e.left+e.right)/2}}#k(){return this.buttonTitle instanceof Function?this.buttonTitle():this.buttonTitle}#E(){const e=this.#k();return this.sideButton?s.html`
      <button id="side-button" @click=${this.#R} ?disabled=${this.disabled}>
        ${e}
      </button>
      <${x.litTagName}
        @click=${this.#x}
        @selectmenubuttontrigger=${this.#x}
        .singleArrow=${!0}
        .open=${this.#f}
        .showArrow=${!0}
        .arrowDirection=${this.position}
        .disabled=${this.disabled}>
      </${x.litTagName}>
    `:s.html`
          <${x.litTagName}
            @selectmenubuttontrigger=${this.#x}
            .open=${this.#f} .showArrow=${this.showArrow}
            .arrowDirection=${this.position}
            .disabled=${this.disabled}
            .jslogContext=${this.jslogContext}>
              ${e}
            </${x.litTagName}>
        `}#B(e){e&&e.stopImmediatePropagation(),S.write((()=>{this.removeAttribute("has-open-dialog")})),this.#f=!1,t.ScheduledRender.scheduleRender(this,this.#t)}#$(e){this.dispatchEvent(new R(e.itemValue))}async#o(){if(!t.ScheduledRender.isScheduledRender(this))throw new Error("SelectMenu render was not scheduled");s.render(s.html`
      <${c.litTagName}
        @menucloserequest=${this.#B}
        @menuitemselected=${this.#$}
        .position=${this.position}
        .origin=${this}
        .showConnector=${this.showConnector}
        .showDivider=${this.showDivider}
        .showSelectedItem=${this.showSelectedItem}
        .open=${this.#f}
        .getConnectorCustomXPosition=${this.#C.bind(this)}
      >
      <slot>
      </slot>
      </${c.litTagName}>
      ${this.#E()}
    `,this.#e,{host:this})}}class x extends HTMLElement{static litTagName=s.literal`devtools-select-menu-button`;#e=this.attachShadow({mode:"open"});#t=this.#o.bind(this);#D=null;connectedCallback(){this.#e.adoptedStyleSheets=[v],this.style.setProperty("--deploy-menu-arrow",`url(${f})`),S.write((()=>{switch(this.arrowDirection){case"auto":case"top":this.style.setProperty("--arrow-angle","180deg");break;case"bottom":this.style.setProperty("--arrow-angle","0deg");break;default:e.assertNever(this.arrowDirection,`Unknown position type: ${this.arrowDirection}`)}}))}#r={showArrow:!1,arrowDirection:"bottom",disabled:!1,singleArrow:!1,jslogContext:""};get showArrow(){return this.#r.showArrow}set showArrow(e){this.#r.showArrow=e,t.ScheduledRender.scheduleRender(this,this.#t)}get arrowDirection(){return this.#r.arrowDirection}set arrowDirection(e){this.#r.arrowDirection=e,t.ScheduledRender.scheduleRender(this,this.#t)}get disabled(){return this.#r.disabled}set disabled(e){this.#r.disabled=e,t.ScheduledRender.scheduleRender(this,this.#t)}set open(e){S.write((()=>{this.#I()?.setAttribute("aria-expanded",String(e))}))}set singleArrow(e){this.#r.singleArrow=e,t.ScheduledRender.scheduleRender(this,this.#t)}get jslogContext(){return this.#r.jslogContext}set jslogContext(e){this.#r.jslogContext=e,t.ScheduledRender.scheduleRender(this,this.#t)}click(){this.#I()?.click()}#I(){return this.#D||(this.#D=this.#e.querySelector("button")),this.#D}#A(t){const o=t.key,s="bottom"===this.arrowDirection&&"ArrowDown"===o,n="top"===this.arrowDirection&&"ArrowUp"===o,r=o===e.KeyboardUtilities.ENTER_KEY,i="Space"===t.code;(s||n||r||i)&&(this.dispatchEvent(new k),t.preventDefault())}#M(){this.dispatchEvent(new k)}async#o(){if(!t.ScheduledRender.isScheduledRender(this))throw new Error("SelectMenuItem render was not scheduled");const e=this.#r.showArrow?s.html`<span id="arrow"></span>`:s.nothing,o={"single-arrow":this.#r.singleArrow},r=s.html`
      <span id="button-label-wrapper">
        <span id="label" ?witharrow=${this.showArrow} class=${s.Directives.classMap(o)}><slot></slot></span>
        ${e}
      </span>
      `;s.render(s.html`
      <button aria-haspopup="true" aria-expanded="false" class="show" @keydown=${this.#A} @click=${this.#M} ?disabled=${this.disabled} jslog=${n.dropDown(this.jslogContext)}>${r}</button>
    `,this.#e,{host:this})}}customElements.define("devtools-select-menu",y),customElements.define("devtools-select-menu-button",x);class R extends Event{itemValue;static eventName="selectmenuselected";constructor(e){super(R.eventName,{bubbles:!0,composed:!0}),this.itemValue=e}}class C extends Event{static eventName="selectmenusidebuttonclick";constructor(){super(C.eventName,{bubbles:!0,composed:!0})}}class k extends Event{static eventName="selectmenubuttontrigger";constructor(){super(k.eventName,{bubbles:!0,composed:!0})}}var E=Object.freeze({__proto__:null,SelectMenu:y,SelectMenuButton:x,SelectMenuItemSelectedEvent:R,SelectMenuSideButtonClickEvent:C,SelectMenuButtonTriggerEvent:k,SelectMenuGroup:p});export{w as Menu,E as SelectMenu};
