const t=new CSSStyleSheet;t.replaceSync(":root::highlight(search-highlight){background-color:var(--sys-color-yellow-outline)}\n/*# sourceURL=highlighting.css */\n");class e{root;#t=0;#e;#h;constructor(t){this.root=t,this.#e=document.createTreeWalker(t,NodeFilter.SHOW_TEXT),this.#h=!this.#e.firstChild()}#i(){return this.#t+=this.#e.currentNode.textContent?.length??0,this.#h=!this.#e.nextNode(),!this.#h}#r(t){if(t<this.#t||this.#h)return null;for(;t>this.#t+(this.#e.currentNode.textContent?.length??0);)if(!this.#i())return null;return this.#e.currentNode}nextRange(t,e){if(e<=0||this.#h)return null;const h=this.#r(t);if(!h)return null;const i=t-this.#t,r=this.#r(t+e);if(!r)return null;const s=t+e-this.#t,o=new Range;return o.setStart(h,i),o.setEnd(r,s),o}}const h="search-highlight";let i;class r{#s=new Highlight;constructor(){document.adoptedStyleSheets.push(t),CSS.highlights.set(h,this.#s)}static instance(t={forceNew:null}){const{forceNew:e}=t;return i&&!e||(i=new r),i}addHighlights(t){t.forEach(this.addHighlight.bind(this))}removeHighlights(t){t.forEach(this.removeHighlight.bind(this))}addHighlight(t){this.#s.add(t)}removeHighlight(t){this.#s.delete(t)}highlightOrderedTextRanges(t,h){const i=new e(t),r=h.map((t=>i.nextRange(t.offset,t.length))).filter((t=>null!==t&&!t.collapsed));return this.addHighlights(r),r}}var s=Object.freeze({__proto__:null,RangeWalker:e,HIGHLIGHT_REGISTRY:h,HighlightManager:r});export{s as HighlightManager};
