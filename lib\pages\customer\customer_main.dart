import 'package:flutter/material.dart';
import '../../widgets/curved_bottom_nav_bar.dart';
import '../../widgets/welcome_notification.dart';
import '../../theme/app_colors.dart';
import '../../services/preferences_service.dart';
import 'home_page.dart';
import 'live_page.dart';
import 'chat_page.dart';
import 'cart_page.dart';
import 'account_page.dart';

class CustomerMainPage extends StatefulWidget {
  const CustomerMainPage({super.key});

  @override
  State<CustomerMainPage> createState() => _CustomerMainPageState();
}

class _CustomerMainPageState extends State<CustomerMainPage> {
  int _currentIndex = 0;
  bool _showWelcomeNotification = false;

  final List<Widget> _pages = [
    const HomePage(),
    const LivePage(),
    const ChatPage(),
    const CartPage(),
    const AccountPage(),
  ];

  @override
  void initState() {
    super.initState();
    _checkWelcomeStatus();
  }

  Future<void> _checkWelcomeStatus() async {
    final hasSeenWelcome = await PreferencesService.getHasSeenWelcome();

    if (!hasSeenWelcome) {
      if (mounted) {
        setState(() {
          _showWelcomeNotification = true;
        });

        // Auto-hide welcome notification after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _showWelcomeNotification = false;
            });
          }
        });

        // Save that user has seen welcome message
        await PreferencesService.setHasSeenWelcome();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Main content
          _pages[_currentIndex],

          // Welcome notification
          if (_showWelcomeNotification)
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              left: 16,
              right: 16,
              child: WelcomeNotification(
                message:
                    'Welcome back to HadiHia! We hope you find what you are looking for.',
                backgroundColor: AppColors.primary,
                onDismiss: () {
                  setState(() {
                    _showWelcomeNotification = false;
                  });
                },
              ),
            ),
        ],
      ),
      bottomNavigationBar: CustomerCurvedBottomNavBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
      ),
    );
  }
}
