import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/livestream_model.dart';
import '../../services/livestream_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import 'livestream_detail_page.dart';

class LivePage extends StatefulWidget {
  const LivePage({super.key});

  @override
  State<LivePage> createState() => _LivePageState();
}

class _LivePageState extends State<LivePage> {
  String _selectedCategory = 'All';

  final List<String> _categories = [
    'All',
    'Beauty',
    'Cooking',
    'Fashion',
    'Crafts',
    'Home Decor',
    'Technology',
    'Art',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Consumer<LivestreamService>(
          builder: (context, livestreamService, _) {
            if (livestreamService.isLoading) {
              return const Center(
                child: CircularProgressIndicator(color: AppColors.primary),
              );
            }

            final allLivestreams = livestreamService.activeLivestreams;

            // Filter by category if not "All"
            final filteredLivestreams =
                _selectedCategory == 'All'
                    ? allLivestreams
                    : allLivestreams
                        .where(
                          (stream) => stream.description.toLowerCase().contains(
                            _selectedCategory.toLowerCase(),
                          ),
                        )
                        .toList();

            return CustomScrollView(
              slivers: [
                // App Bar
                SliverAppBar(
                  floating: true,
                  title: Row(
                    children: [
                      Icon(
                        Icons.live_tv,
                        color: AppColors.liveIndicator,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Live Streams',
                        style: AppTextStyles.heading2.copyWith(fontSize: 20),
                      ),
                    ],
                  ),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: () {
                        // Search functionality would go here
                      },
                    ),
                  ],
                ),

                // Category Chips
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                    child: SizedBox(
                      height: 40,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          final isSelected = category == _selectedCategory;

                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: ChoiceChip(
                              label: Text(category),
                              selected: isSelected,
                              selectedColor: AppColors.primary,
                              backgroundColor: Colors.white,
                              labelStyle: TextStyle(
                                color:
                                    isSelected
                                        ? Colors.white
                                        : AppColors.textPrimary,
                              ),
                              onSelected: (selected) {
                                if (selected) {
                                  setState(() {
                                    _selectedCategory = category;
                                  });
                                }
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),

                // Live Streams Grid
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  sliver:
                      filteredLivestreams.isEmpty
                          ? SliverFillRemaining(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.tv_off,
                                    size: 64,
                                    color: Colors.grey.shade400,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No live streams available',
                                    style: AppTextStyles.bodyLarge.copyWith(
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Check back later for new streams',
                                    style: AppTextStyles.bodyMedium.copyWith(
                                      color: Colors.grey.shade500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                          : SliverGrid(
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  childAspectRatio: 0.75,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                ),
                            delegate: SliverChildBuilderDelegate((
                              context,
                              index,
                            ) {
                              final livestream = filteredLivestreams[index];
                              return LiveStreamCard(
                                livestream: livestream,
                                onTap:
                                    () => _navigateToLivestreamDetails(
                                      context,
                                      livestream,
                                    ),
                              );
                            }, childCount: filteredLivestreams.length),
                          ),
                ),

                // Upcoming Streams Section (if needed)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                    child: Text(
                      'Upcoming Streams',
                      style: AppTextStyles.heading2,
                    ),
                  ),
                ),

                // Upcoming Streams List
                SliverPadding(
                  padding: const EdgeInsets.all(16),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final scheduledLivestreams =
                            livestreamService.scheduledLivestreams;
                        if (index >= scheduledLivestreams.length) return null;

                        final livestream = scheduledLivestreams[index];
                        return UpcomingStreamCard(
                          livestream: livestream,
                          onTap:
                              () => _navigateToLivestreamDetails(
                                context,
                                livestream,
                              ),
                        );
                      },
                      childCount: livestreamService.scheduledLivestreams.length,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _navigateToLivestreamDetails(
    BuildContext context,
    LiveStream livestream,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LivestreamDetailPage(livestream: livestream),
      ),
    );
  }
}

class LiveStreamCard extends StatelessWidget {
  final LiveStream livestream;
  final VoidCallback onTap;

  const LiveStreamCard({
    super.key,
    required this.livestream,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate time since stream started
    final duration = DateTime.now().difference(livestream.startTime);
    final liveFor =
        duration.inHours > 0
            ? '${duration.inHours}h ${duration.inMinutes.remainder(60)}m'
            : '${duration.inMinutes}m';

    return GestureDetector(
      onTap: onTap,
      child: Card(
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 4,
        shadowColor: Colors.black26,
        // Use LayoutBuilder to ensure the card fits within its constraints
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Calculate the image height based on the aspect ratio
            final imageHeight = constraints.maxWidth * (9 / 16);
            // Calculate the remaining height for the content
            final contentMaxHeight = constraints.maxHeight - imageHeight;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Thumbnail with overlays - Fixed height
                SizedBox(
                  height: imageHeight,
                  width: constraints.maxWidth,
                  child: Stack(
                    children: [
                      // Thumbnail
                      Positioned.fill(
                        child: Image.network(
                          livestream.thumbnailUrl,
                          fit: BoxFit.cover,
                          errorBuilder:
                              (context, error, stackTrace) => Container(
                                color: Colors.grey.shade200,
                                child: const Icon(
                                  Icons.error,
                                  color: AppColors.error,
                                ),
                              ),
                        ),
                      ),

                      // Gradient overlay for better text visibility
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withAlpha(160),
                              ],
                              stops: const [0.6, 1.0],
                            ),
                          ),
                        ),
                      ),

                      // LIVE badge
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 3,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.liveIndicator,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.circle,
                                color: Colors.white,
                                size: 6,
                              ),
                              const SizedBox(width: 3),
                              Text(
                                'LIVE',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 8,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Viewer count
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 3,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black54,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.visibility,
                                color: Colors.white,
                                size: 10,
                              ),
                              const SizedBox(width: 3),
                              Text(
                                '${livestream.viewerCount}',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Live duration
                      Positioned(
                        bottom: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 3,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black54,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Live for $liveFor',
                            style: TextStyle(color: Colors.white, fontSize: 8),
                          ),
                        ),
                      ),

                      // Join Now button
                      Positioned(
                        bottom: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.play_circle_outline,
                                color: Colors.white,
                                size: 10,
                              ),
                              const SizedBox(width: 3),
                              Text(
                                'Join Now',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 8,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Stream info - Flexible height with constraints
                Flexible(
                  child: Container(
                    constraints: BoxConstraints(maxHeight: contentMaxHeight),
                    child: Padding(
                      padding: const EdgeInsets.all(6),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title
                          Text(
                            livestream.title,
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: 12, // Smaller font
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),

                          const SizedBox(height: 4), // Reduced spacing
                          // Seller info
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 8, // Smaller avatar
                                backgroundImage: NetworkImage(
                                  livestream.storeImageUrl,
                                ),
                              ),
                              const SizedBox(width: 4), // Reduced spacing
                              Expanded(
                                child: Text(
                                  livestream.storeName,
                                  style: AppTextStyles.bodySmall.copyWith(
                                    fontSize: 10, // Smaller font
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 4), // Reduced spacing
                          // Quick interaction icons
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildInteractionIcon(
                                Icons.favorite_border,
                                'Like',
                              ),
                              _buildInteractionIcon(
                                Icons.chat_bubble_outline,
                                'Chat',
                              ),
                              _buildInteractionIcon(
                                Icons.share_outlined,
                                'Share',
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildInteractionIcon(IconData icon, String label) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: AppColors.textSecondary),
        const SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(fontSize: 8, color: AppColors.textSecondary),
        ),
      ],
    );
  }
}

class UpcomingStreamCard extends StatelessWidget {
  final LiveStream livestream;
  final VoidCallback onTap;

  const UpcomingStreamCard({
    super.key,
    required this.livestream,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Format scheduled time
    final scheduledTime = livestream.startTime;
    final formattedDate =
        '${scheduledTime.day}/${scheduledTime.month}/${scheduledTime.year}';
    final formattedTime =
        '${scheduledTime.hour.toString().padLeft(2, '0')}:${scheduledTime.minute.toString().padLeft(2, '0')}';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(8), // Reduced padding
          child: Row(
            crossAxisAlignment:
                CrossAxisAlignment.center, // Ensure vertical centering
            children: [
              // Thumbnail - Fixed size
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: SizedBox(
                  width: 70, // Slightly smaller
                  height: 70, // Slightly smaller
                  child: Image.network(
                    livestream.thumbnailUrl,
                    fit: BoxFit.cover,
                    errorBuilder:
                        (context, error, stackTrace) => Container(
                          color: Colors.grey.shade200,
                          child: const Icon(
                            Icons.error,
                            color: AppColors.error,
                          ),
                        ),
                  ),
                ),
              ),
              const SizedBox(width: 8), // Reduced spacing
              // Stream info - Flexible to prevent overflow
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min, // Use minimum space needed
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      livestream.title,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 13, // Slightly smaller font
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // Store info
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 8,
                          backgroundImage: NetworkImage(
                            livestream.storeImageUrl,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            livestream.storeName,
                            style: AppTextStyles.bodySmall.copyWith(
                              fontSize: 10, // Smaller font
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4), // Reduced spacing
                    // Date and time - Wrap in a Flexible widget
                    Flexible(
                      child: Wrap(
                        spacing: 8, // Space between date and time
                        children: [
                          // Date
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 10, // Smaller icon
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 2), // Reduced spacing
                              Text(
                                formattedDate,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.textSecondary,
                                  fontSize: 9, // Smaller font
                                ),
                              ),
                            ],
                          ),

                          // Time
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 10, // Smaller icon
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 2), // Reduced spacing
                              Text(
                                formattedTime,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.textSecondary,
                                  fontSize: 9, // Smaller font
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Reminder button - Smaller and more compact
              SizedBox(
                width: 36, // Fixed width
                height: 36, // Fixed height
                child: IconButton(
                  padding: EdgeInsets.zero,
                  icon: const Icon(
                    Icons.notifications_none,
                    color: AppColors.primary,
                    size: 20, // Smaller icon
                  ),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Reminder set for ${livestream.title}'),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
