{"name": "parse-json", "version": "4.0.0", "description": "Parse JSO<PERSON> with more helpful errors", "license": "MIT", "repository": "sindresorhus/parse-json", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js", "vendor"], "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string", "str"], "dependencies": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}, "devDependencies": {"ava": "*", "nyc": "^11.2.1", "xo": "*"}}