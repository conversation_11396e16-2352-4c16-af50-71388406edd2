import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../services/image_upload_service.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

class ImageUploadWidget extends StatefulWidget {
  final String? initialImageUrl;
  final Function(String?) onImageUploaded;
  final double height;
  final double width;
  final String placeholder;
  final bool required;

  const ImageUploadWidget({
    super.key,
    this.initialImageUrl,
    required this.onImageUploaded,
    this.height = 200,
    this.width = double.infinity,
    this.placeholder = 'Tap to upload product image',
    this.required = true,
  });

  @override
  State<ImageUploadWidget> createState() => _ImageUploadWidgetState();
}

class _ImageUploadWidgetState extends State<ImageUploadWidget> {
  String? _currentImageUrl;
  XFile? _selectedImage;

  @override
  void initState() {
    super.initState();
    _currentImageUrl = widget.initialImageUrl;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ImageUploadService>(
      builder: (context, uploadService, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Upload Container
            GestureDetector(
              onTap: uploadService.isUploading ? null : _handleImagePick,
              child: Container(
                height: widget.height,
                width: widget.width,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: uploadService.error != null 
                        ? AppColors.error 
                        : Colors.grey.shade300,
                    width: 2,
                  ),
                ),
                child: _buildImageContent(uploadService),
              ),
            ),

            // Upload Progress
            if (uploadService.isUploading) ...[
              const SizedBox(height: 12),
              _buildProgressIndicator(uploadService),
            ],

            // Error Message
            if (uploadService.error != null) ...[
              const SizedBox(height: 8),
              _buildErrorMessage(uploadService),
            ],

            // Upload Actions
            if (!uploadService.isUploading && _currentImageUrl == null) ...[
              const SizedBox(height: 12),
              _buildUploadActions(),
            ],

            // Image Actions (when image is uploaded)
            if (_currentImageUrl != null && !uploadService.isUploading) ...[
              const SizedBox(height: 12),
              _buildImageActions(),
            ],
          ],
        );
      },
    );
  }

  Widget _buildImageContent(ImageUploadService uploadService) {
    // Show upload progress
    if (uploadService.isUploading) {
      return _buildUploadingState(uploadService);
    }

    // Show selected image (before upload)
    if (_selectedImage != null && _currentImageUrl == null) {
      return _buildSelectedImagePreview();
    }

    // Show uploaded image
    if (_currentImageUrl != null) {
      return _buildUploadedImage();
    }

    // Show placeholder
    return _buildPlaceholder();
  }

  Widget _buildUploadingState(ImageUploadService uploadService) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(
          value: uploadService.uploadProgress,
          color: AppColors.primary,
          strokeWidth: 3,
        ),
        const SizedBox(height: 16),
        Text(
          'Uploading image...',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${(uploadService.uploadProgress * 100).toInt()}%',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedImagePreview() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(14),
          child: kIsWeb
              ? Image.network(
                  _selectedImage!.path,
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                )
              : Image.file(
                  File(_selectedImage!.path),
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: () {
                setState(() {
                  _selectedImage = null;
                });
              },
            ),
          ),
        ),
        Positioned(
          bottom: 16,
          left: 16,
          right: 16,
          child: ElevatedButton.icon(
            onPressed: _handleImageUpload,
            icon: const Icon(Icons.cloud_upload),
            label: const Text('Upload Image'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadedImage() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(14),
          child: Image.network(
            _currentImageUrl!,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return _buildImageError();
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                  color: AppColors.primary,
                ),
              );
            },
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.success,
              borderRadius: BorderRadius.circular(20),
            ),
            padding: const EdgeInsets.all(8),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 16,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.add_photo_alternate_outlined,
          size: 64,
          color: Colors.grey.shade400,
        ),
        const SizedBox(height: 16),
        Text(
          widget.placeholder,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
        if (widget.required) ...[
          const SizedBox(height: 8),
          Text(
            'Required',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.error,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildImageError() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.broken_image_outlined,
          size: 64,
          color: Colors.red.shade400,
        ),
        const SizedBox(height: 16),
        Text(
          'Failed to load image',
          style: AppTextStyles.bodyMedium.copyWith(
            color: Colors.red.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(ImageUploadService uploadService) {
    return Column(
      children: [
        LinearProgressIndicator(
          value: uploadService.uploadProgress,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
        const SizedBox(height: 8),
        Text(
          'Uploading... ${(uploadService.uploadProgress * 100).toInt()}%',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorMessage(ImageUploadService uploadService) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.error.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: AppColors.error, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              uploadService.error!,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              uploadService.clearError();
            },
            child: Text(
              'Dismiss',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadActions() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _handleImagePick,
            icon: const Icon(Icons.photo_library),
            label: Text(kIsWeb ? 'Choose File' : 'Choose from Gallery'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: const BorderSide(color: AppColors.primary),
            ),
          ),
        ),
        if (!kIsWeb) ...[
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _handleImagePick(useCamera: true),
              icon: const Icon(Icons.camera_alt),
              label: const Text('Take Photo'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.secondary,
                side: const BorderSide(color: AppColors.secondary),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildImageActions() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _handleImagePick,
            icon: const Icon(Icons.edit),
            label: const Text('Change Image'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: const BorderSide(color: AppColors.primary),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _handleImageRemove,
            icon: const Icon(Icons.delete),
            label: const Text('Remove'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.error,
              side: const BorderSide(color: AppColors.error),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleImagePick({bool useCamera = false}) async {
    final uploadService = Provider.of<ImageUploadService>(context, listen: false);
    
    XFile? image;
    if (kIsWeb || !useCamera) {
      image = await uploadService.pickImageFromDevice();
    } else {
      image = await uploadService.pickImageFromDevice(source: ImageSource.camera);
    }

    if (image != null) {
      setState(() {
        _selectedImage = image;
        _currentImageUrl = null;
      });
      uploadService.clearError();
    }
  }

  Future<void> _handleImageUpload() async {
    if (_selectedImage == null) return;

    final uploadService = Provider.of<ImageUploadService>(context, listen: false);
    
    final String? imageUrl = await uploadService.uploadImage(_selectedImage!);
    
    if (imageUrl != null) {
      setState(() {
        _currentImageUrl = imageUrl;
        _selectedImage = null;
      });
      widget.onImageUploaded(imageUrl);
    }
  }

  void _handleImageRemove() {
    setState(() {
      _currentImageUrl = null;
      _selectedImage = null;
    });
    widget.onImageUploaded(null);
  }
}
