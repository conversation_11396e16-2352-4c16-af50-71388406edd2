import 'package:flutter/material.dart';

class VerificationBadge extends StatelessWidget {
  final double size;
  final Color color;
  final bool isAnimated;

  const VerificationBadge({
    Key? key,
    this.size = 18.0,
    this.color = Colors.blue,
    this.isAnimated = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isAnimated
        ? _AnimatedVerificationBadge(size: size, color: color)
        : _StaticVerificationBadge(size: size, color: color);
  }
}

class _StaticVerificationBadge extends StatelessWidget {
  final double size;
  final Color color;

  const _StaticVerificationBadge({
    Key? key,
    required this.size,
    required this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(102), // 0.4 opacity
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(Icons.check, color: Colors.white, size: size * 0.7),
    );
  }
}

class _AnimatedVerificationBadge extends StatefulWidget {
  final double size;
  final Color color;

  const _AnimatedVerificationBadge({
    Key? key,
    required this.size,
    required this.color,
  }) : super(key: key);

  @override
  State<_AnimatedVerificationBadge> createState() =>
      _AnimatedVerificationBadgeState();
}

class _AnimatedVerificationBadgeState extends State<_AnimatedVerificationBadge>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _opacityAnimation = Tween<double>(
      begin: 0.7,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                color: widget.color,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: widget.color.withAlpha(102), // 0.4 opacity
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                    spreadRadius: _scaleAnimation.value,
                  ),
                ],
              ),
              child: Icon(
                Icons.check,
                color: Colors.white,
                size: widget.size * 0.7,
              ),
            ),
          ),
        );
      },
    );
  }
}
