import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as s from"../../core/platform/platform.js";import*as n from"../../models/text_utils/text_utils.js";import*as a from"../../ui/legacy/components/utils/utils.js";import*as i from"../../ui/legacy/legacy.js";import*as r from"../../core/host/host.js";import*as h from"../../models/workspace/workspace.js";import*as o from"../../ui/components/buttons/buttons.js";import*as c from"../../ui/components/icon_button/icon_button.js";import*as l from"../../ui/visual_logging/visual_logging.js";const d=new CSSStyleSheet;d.replaceSync(":host{padding:0;margin:0;overflow-y:auto}.tree-outline{padding:0}.tree-outline ol{padding:0}.tree-outline li{height:16px}li.search-result{cursor:pointer;font-size:12px;margin-top:8px;padding:2px 0 2px 4px;word-wrap:normal;white-space:pre}li.search-result:hover{background-color:var(--sys-color-state-hover-on-subtle)}li.search-result .search-result-file-name{color:var(--sys-color-on-surface);flex:1 1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}li.search-result .search-result-matches-count{color:var(--sys-color-token-subtle);margin:0 8px}li.search-result.expanded .search-result-matches-count{display:none}li.show-more-matches{color:var(--sys-color-on-surface);cursor:pointer;margin:8px 0 0 -4px}li.show-more-matches:hover{text-decoration:underline}li.search-match{margin:2px 0;word-wrap:normal;white-space:pre}li.search-match.selected:focus-visible{background:var(--sys-color-tonal-container)}li.search-match::before{display:none}li.search-match .search-match-line-number{color:var(--sys-color-token-subtle);text-align:right;vertical-align:top;word-break:normal;padding:2px 4px 2px 6px;margin-right:5px}.tree-outline .devtools-link{text-decoration:none;display:block;flex:auto}li.search-match .search-match-content{color:var(--sys-color-on-surface)}ol.children.expanded{padding-bottom:4px}li.search-match .link-style.search-match-link{overflow:hidden;text-overflow:ellipsis;margin-left:9px;text-align:left}.search-result-qualifier{color:var(--sys-color-token-subtle)}.search-result-dash{color:var(--sys-color-surface-variant);margin:0 4px}\n/*# sourceURL=searchResultsPane.css */\n");const u={matchesCountS:"Matches Count {PH1}",lineS:"Line {PH1}",showDMore:"Show {PH1} more"},g=t.i18n.registerUIStrings("panels/search/SearchResultsPane.ts",u),p=t.i18n.getLocalizedString.bind(void 0,g);class m extends i.Widget.VBox{searchConfig;searchResults;treeElements;treeOutline;matchesExpandedCount;constructor(e){super(!0),this.searchConfig=e,this.searchResults=[],this.treeElements=[],this.treeOutline=new i.TreeOutline.TreeOutlineInShadow,this.treeOutline.hideOverflow(),this.contentElement.appendChild(this.treeOutline.element),this.matchesExpandedCount=0}addSearchResult(e){this.searchResults.push(e),this.addTreeElement(e)}showAllMatches(){this.treeElements.forEach((e=>{e.expand(),e.showAllMatches()}))}collapseAllResults(){this.treeElements.forEach((e=>{e.collapse()}))}addTreeElement(e){const t=new x(this.searchConfig,e);this.treeOutline.appendChild(t),this.treeOutline.selectedTreeElement||t.select(!0,!0),this.matchesExpandedCount<f&&t.expand(),this.matchesExpandedCount+=e.matchesCount(),this.treeElements.push(t)}wasShown(){super.wasShown(),this.treeOutline.registerCSSFiles([d])}}const f=200;class x extends i.TreeOutline.TreeElement{searchConfig;searchResult;initialized;toggleOnClick;constructor(e,t){super("",!0),this.searchConfig=e,this.searchResult=t,this.initialized=!1,this.toggleOnClick=!0}onexpand(){this.initialized||(this.updateMatchesUI(),this.initialized=!0)}showAllMatches(){this.removeChildren(),this.appendSearchMatches(0,this.searchResult.matchesCount())}updateMatchesUI(){this.removeChildren();const e=Math.min(this.searchResult.matchesCount(),20);e<this.searchResult.matchesCount()?(this.appendSearchMatches(0,e-1),this.appendShowMoreMatchesElement(e-1)):this.appendSearchMatches(0,e)}onattach(){this.updateSearchMatches()}updateSearchMatches(){this.listItemElement.classList.add("search-result");const e=s(this.searchResult.label(),"search-result-file-name");e.appendChild(s("—","search-result-dash")),e.appendChild(s(this.searchResult.description(),"search-result-qualifier")),this.tooltip=this.searchResult.description(),this.listItemElement.appendChild(e);const t=document.createElement("span");function s(e,t){const s=document.createElement("span");return s.className=t,s.textContent=e,s}t.className="search-result-matches-count",t.textContent=`${this.searchResult.matchesCount()}`,i.ARIAUtils.setLabel(t,p(u.matchesCountS,{PH1:this.searchResult.matchesCount()})),this.listItemElement.appendChild(t),this.expanded&&this.updateMatchesUI()}appendSearchMatches(t,r){const h=this.searchResult,o=this.searchConfig.queries(),c=[];for(let e=0;e<o.length;++e)c.push(s.StringUtilities.createSearchRegex(o[e],!this.searchConfig.ignoreCase(),this.searchConfig.isRegex()));for(let s=t;s<r;++s){let t=h.matchLineContent(s),r=[];const o=h.matchColumn(s),l=h.matchLength(s);if(void 0!==o&&void 0!==l){const{matchRange:e,lineSegment:s}=S(t,new n.TextRange.SourceRange(o,l));t=s,r=[e]}else{t=t.trim();for(let e=0;e<c.length;++e)r=r.concat(this.regexMatchRanges(t,c[e]));({lineSegment:t,matchRanges:r}=w(t,r))}const d=a.Linkifier.Linkifier.linkifyRevealable(h.matchRevealable(s),"",void 0,void 0,void 0,"search-match");d.classList.add("search-match-link"),d.tabIndex=0;const g=document.createElement("span");g.classList.add("search-match-line-number");const m=h.matchLabel(s);g.textContent=m,"number"!=typeof m||isNaN(m)?i.ARIAUtils.setLabel(g,m):i.ARIAUtils.setLabel(g,p(u.lineS,{PH1:m})),d.appendChild(g);const f=this.createContentSpan(t,r);d.appendChild(f);const x=new i.TreeOutline.TreeElement;this.appendChild(x),x.listItemElement.className="search-match",x.listItemElement.appendChild(d),x.listItemElement.addEventListener("keydown",(t=>{"Enter"===t.key&&(t.consume(!0),e.Revealer.reveal(h.matchRevealable(s)))})),x.tooltip=t}}appendShowMoreMatchesElement(e){const t=this.searchResult.matchesCount()-e,s=p(u.showDMore,{PH1:t}),n=new i.TreeOutline.TreeElement(s);this.appendChild(n),n.listItemElement.classList.add("show-more-matches"),n.onselect=this.showMoreMatchesElementSelected.bind(this,n,e)}createContentSpan(e,t){const s=document.createElement("span");return s.className="search-match-content",s.textContent=e,i.ARIAUtils.setLabel(s,`${e} line`),i.UIUtils.highlightRangesWithStyleClass(s,t,"highlighted-search-result"),s}regexMatchRanges(e,t){let s;t.lastIndex=0;const a=[];for(;t.lastIndex<e.length&&(s=t.exec(e));)a.push(new n.TextRange.SourceRange(s.index,s[0].length));return a}showMoreMatchesElementSelected(e,t){return this.removeChild(e),this.appendSearchMatches(t,this.searchResult.matchesCount()),!1}}const C={prefixLength:25,maxLength:1e3};function S(e,t,s=C){const a={...C,...s},i=e.trimStart(),r=e.length-i.length,h=Math.min(t.offset,r),o=Math.max(h,t.offset-a.prefixLength),c=Math.min(e.length,o+a.maxLength),l=o>h?"…":"",d=l+e.substring(o,c),u=t.offset-o+l.length,g=Math.min(t.length,d.length-u);return{lineSegment:d,matchRange:new n.TextRange.SourceRange(u,g)}}function w(e,t){let s=0,a=t;a.length>0&&a[0].offset>20&&(s=15);let i=e.substring(s,1e3+s);return s&&(a=a.map((e=>new n.TextRange.SourceRange(e.offset-s+1,e.length))),i="…"+i),{lineSegment:i,matchRanges:a}}var b=Object.freeze({__proto__:null,SearchResultsPane:m,matchesExpandedByDefault:f,matchesShownAtOnce:20,SearchResultsTreeElement:x,lineSegmentForMatch:S}),v=Object.freeze({__proto__:null});const R=new CSSStyleSheet;R.replaceSync('.search-drawer-header{align-items:center;flex-shrink:0;overflow:hidden;display:inline-flex;min-width:150px;.search-container{border-bottom:1px solid var(--sys-color-divider);display:flex;height:100%;align-items:center;flex-grow:1}.toolbar-item-search{flex-grow:1;box-shadow:inset 0 0 0 2px transparent;box-sizing:border-box;height:var(--sys-size-9);margin-left:var(--sys-size-3);padding:0 var(--sys-size-2) 0 var(--sys-size-5);border-radius:100px;position:relative;display:flex;align-items:center;background-color:var(--sys-color-cdt-base);&:has(input:focus){box-shadow:inset 0 0 0 2px var(--sys-color-state-focus-ring)}&:has(input:hover)::before{content:"";box-sizing:inherit;height:100%;width:100%;position:absolute;border-radius:100px;left:0;background-color:var(--sys-color-state-hover-on-subtle)}& > devtools-icon{color:var(--sys-color-on-surface-subtle);width:var(--sys-size-8);height:var(--sys-size-8);margin-right:var(--sys-size-3)}}.search-toolbar-input{appearance:none;color:var(--sys-color-on-surface);background-color:transparent;border:0;z-index:1;flex:1;&::placeholder{color:var(--sys-color-on-surface-subtle)}&:placeholder-shown + .clear-button{display:none}&::-webkit-search-cancel-button{display:none}}}.search-toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.search-toolbar-summary{background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);padding-left:5px;flex:0 0 19px;display:flex;padding-right:5px}.search-toolbar-summary .search-message{padding-top:2px;padding-left:1ex;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.search-view .search-results{overflow-y:auto;display:flex;flex:auto}.search-view .search-results > div{flex:auto}\n/*# sourceURL=searchView.css */\n');const y={find:"Find",enableCaseSensitive:"Enable case sensitive search",disableCaseSensitive:"Disable case sensitive search",enableRegularExpression:"Enable regular expressions",disableRegularExpression:"Disable regular expressions",refresh:"Refresh",clearInput:"Clear",clear:"Clear search",indexing:"Indexing…",searching:"Searching…",indexingInterrupted:"Indexing interrupted.",foundMatchingLineInFile:"Found 1 matching line in 1 file.",foundDMatchingLinesInFile:"Found {PH1} matching lines in 1 file.",foundDMatchingLinesInDFiles:"Found {PH1} matching lines in {PH2} files.",noMatchesFound:"No matches found.",searchFinished:"Search finished.",searchInterrupted:"Search interrupted."},E=t.i18n.registerUIStrings("panels/search/SearchView.ts",y),I=t.i18n.getLocalizedString.bind(void 0,E);function M(e,t){const s=new o.Button.Button;return s.data={variant:"icon_toggle",size:"SMALL",iconName:e,toggledIconName:e,toggleType:"primary-toggle",toggled:!1,jslogContext:t},s}class P extends i.Widget.VBox{focusOnShow;isIndexing;searchId;searchMatchesCount;searchResultsCount;nonEmptySearchResultsCount;searchingView;notFoundView;searchConfig;pendingSearchConfig;searchResultsPane;progressIndicator;visiblePane;searchPanelElement;searchResultsElement;search;matchCaseButton;regexButton;searchMessageElement;searchProgressPlaceholderElement;searchResultsMessageElement;advancedSearchConfig;searchScope;#e;#t=[];constructor(t,s){super(!0),this.setMinimumSize(0,40),this.focusOnShow=!1,this.isIndexing=!1,this.searchId=1,this.searchMatchesCount=0,this.searchResultsCount=0,this.nonEmptySearchResultsCount=0,this.searchingView=null,this.notFoundView=null,this.searchConfig=null,this.pendingSearchConfig=null,this.searchResultsPane=null,this.progressIndicator=null,this.visiblePane=null,this.#e=s,this.contentElement.setAttribute("jslog",`${l.panel("search").track({resize:!0})}`),this.contentElement.classList.add("search-view"),this.contentElement.addEventListener("keydown",(e=>{this.onKeyDownOnPanel(e)})),this.searchPanelElement=this.contentElement.createChild("div","search-drawer-header"),this.searchResultsElement=this.contentElement.createChild("div"),this.searchResultsElement.className="search-results";const n=document.createElement("div");n.classList.add("search-container");const a=n.createChild("div","toolbar-item-search"),r=c.Icon.create("search");a.appendChild(r),this.search=i.HistoryInput.HistoryInput.create(),this.search.addEventListener("keydown",(e=>{this.onKeyDown(e)})),this.search.setAttribute("jslog",`${l.textField().track({change:!0,keydown:"ArrowUp|ArrowDown|Enter"})}`),a.appendChild(this.search),this.search.placeholder=I(y.find),this.search.setAttribute("type","search"),this.search.setAttribute("results","0"),this.search.setAttribute("size","100"),this.search.classList.add("search-toolbar-input"),i.ARIAUtils.setLabel(this.search,this.search.placeholder);const d=new o.Button.Button;d.data={variant:"icon",size:"SMALL",iconName:"cross-circle-filled",jslogContext:"clear-input",title:I(y.clearInput)},d.classList.add("clear-button"),d.addEventListener("click",(()=>{this.onSearchInputClear()})),d.tabIndex=-1,a.appendChild(d);const u="regular-expression";this.regexButton=M(u,u),this.regexButton.addEventListener("click",(()=>this.regexButtonToggled())),a.appendChild(this.regexButton);const g="match-case";this.matchCaseButton=M(g,g),this.matchCaseButton.addEventListener("click",(()=>this.matchCaseButtonToggled())),a.appendChild(this.matchCaseButton),this.searchPanelElement.appendChild(n);const p=new i.Toolbar.Toolbar("search-toolbar",this.searchPanelElement);p.element.setAttribute("jslog",`${l.toolbar()}`);const m=new i.Toolbar.ToolbarButton(I(y.refresh),"refresh",void 0,"search.refresh"),f=new i.Toolbar.ToolbarButton(I(y.clear),"clear",void 0,"search.clear");p.appendToolbarItem(m),p.appendToolbarItem(f),m.addEventListener("Click",(()=>this.onAction())),f.addEventListener("Click",(()=>{this.resetSearch(),this.onSearchInputClear()}));const x=this.contentElement.createChild("div","search-toolbar-summary");this.searchMessageElement=x.createChild("div","search-message"),this.searchProgressPlaceholderElement=x.createChild("div","flex-centered"),this.searchResultsMessageElement=x.createChild("div","search-message"),this.advancedSearchConfig=e.Settings.Settings.instance().createLocalSetting(t+"-search-config",new h.SearchConfig.SearchConfig("",!0,!1).toPlainObject()),this.load(),this.searchScope=null}regexButtonToggled(){this.regexButton.title=this.regexButton.toggled?I(y.disableRegularExpression):I(y.enableRegularExpression)}matchCaseButtonToggled(){this.matchCaseButton.title=this.matchCaseButton.toggled?I(y.disableCaseSensitive):I(y.enableCaseSensitive)}buildSearchConfig(){return new h.SearchConfig.SearchConfig(this.search.value,!this.matchCaseButton.toggled,this.regexButton.toggled)}toggle(e,t){this.search.value=e,this.isShowing()?this.focus():this.focusOnShow=!0,this.initScope(),t?this.onAction():this.startIndexing()}createScope(){throw new Error("Not implemented")}initScope(){this.searchScope=this.createScope()}wasShown(){this.focusOnShow&&(this.focus(),this.focusOnShow=!1),this.registerCSSFiles([R])}onIndexingFinished(){if(!this.progressIndicator)return;const e=!this.progressIndicator.isCanceled();if(this.progressIndicator.done(),this.progressIndicator=null,this.isIndexing=!1,this.searchMessageElement.textContent=e?"":I(y.indexingInterrupted),e||(this.pendingSearchConfig=null),!this.pendingSearchConfig)return;const t=this.pendingSearchConfig;this.pendingSearchConfig=null,this.innerStartSearch(t)}startIndexing(){this.isIndexing=!0,this.progressIndicator&&this.progressIndicator.done(),this.progressIndicator=new i.ProgressIndicator.ProgressIndicator,this.searchMessageElement.textContent=I(y.indexing),this.progressIndicator.show(this.searchProgressPlaceholderElement),this.searchScope&&this.searchScope.performIndexing(new e.Progress.ProgressProxy(this.progressIndicator,this.onIndexingFinished.bind(this)))}onSearchInputClear(){this.search.value="",this.save(),this.focus()}onSearchResult(e,t){e===this.searchId&&this.progressIndicator&&(this.progressIndicator&&this.progressIndicator.isCanceled()?this.onIndexingFinished():(this.searchResultsPane||(this.searchResultsPane=new m(this.searchConfig),this.showPane(this.searchResultsPane)),this.#t.push(t),this.#e.schedule((async()=>this.#s()))))}#s(){for(const e of this.#t)this.addSearchResult(e),e.matchesCount()&&this.searchResultsPane?.addSearchResult(e);this.#t=[]}onSearchFinished(e,t){e===this.searchId&&this.progressIndicator&&(this.searchResultsPane||this.nothingFound(),this.searchFinished(t),this.searchConfig=null,i.ARIAUtils.alert(this.searchMessageElement.textContent+" "+this.searchResultsMessageElement.textContent))}innerStartSearch(e){this.searchConfig=e,this.progressIndicator&&this.progressIndicator.done(),this.progressIndicator=new i.ProgressIndicator.ProgressIndicator,this.searchStarted(this.progressIndicator),this.searchScope&&this.searchScope.performSearch(e,this.progressIndicator,this.onSearchResult.bind(this,this.searchId),this.onSearchFinished.bind(this,this.searchId))}resetSearch(){this.stopSearch(),this.showPane(null),this.searchResultsPane=null,this.searchMessageElement.textContent="",this.searchResultsMessageElement.textContent=""}stopSearch(){this.progressIndicator&&!this.isIndexing&&this.progressIndicator.cancel(),this.searchScope&&this.searchScope.stopSearch(),this.searchConfig=null}searchStarted(e){this.searchMatchesCount=0,this.searchResultsCount=0,this.nonEmptySearchResultsCount=0,this.searchingView||(this.searchingView=new i.EmptyWidget.EmptyWidget(I(y.searching))),this.showPane(this.searchingView),this.searchMessageElement.textContent=I(y.searching),e.show(this.searchProgressPlaceholderElement),this.updateSearchResultsMessage()}updateSearchResultsMessage(){this.searchMatchesCount&&this.searchResultsCount?1===this.searchMatchesCount&&1===this.nonEmptySearchResultsCount?this.searchResultsMessageElement.textContent=I(y.foundMatchingLineInFile):this.searchMatchesCount>1&&1===this.nonEmptySearchResultsCount?this.searchResultsMessageElement.textContent=I(y.foundDMatchingLinesInFile,{PH1:this.searchMatchesCount}):this.searchResultsMessageElement.textContent=I(y.foundDMatchingLinesInDFiles,{PH1:this.searchMatchesCount,PH2:this.nonEmptySearchResultsCount}):this.searchResultsMessageElement.textContent=""}showPane(e){this.visiblePane&&this.visiblePane.detach(),e&&e.show(this.searchResultsElement),this.visiblePane=e}nothingFound(){this.notFoundView||(this.notFoundView=new i.EmptyWidget.EmptyWidget(I(y.noMatchesFound))),this.showPane(this.notFoundView),this.searchResultsMessageElement.textContent=I(y.noMatchesFound)}addSearchResult(e){const t=e.matchesCount();this.searchMatchesCount+=t,this.searchResultsCount++,t&&this.nonEmptySearchResultsCount++,this.updateSearchResultsMessage()}searchFinished(e){this.searchMessageElement.textContent=I(e?y.searchFinished:y.searchInterrupted)}focus(){this.search.focus(),this.search.select()}willHide(){this.stopSearch()}onKeyDown(e){if(this.save(),e.keyCode===i.KeyboardShortcut.Keys.Enter.code)this.onAction()}onKeyDownOnPanel(e){const t=r.Platform.isMac(),s=t&&e.metaKey&&!e.ctrlKey&&e.altKey&&"BracketRight"===e.code,n=!t&&e.ctrlKey&&!e.metaKey&&e.shiftKey&&"BracketRight"===e.code,a=t&&e.metaKey&&!e.ctrlKey&&e.altKey&&"BracketLeft"===e.code,i=!t&&e.ctrlKey&&!e.metaKey&&e.shiftKey&&"BracketLeft"===e.code;s||n?(this.searchResultsPane?.showAllMatches(),l.logKeyDown(e.currentTarget,e,"show-all-matches")):(a||i)&&(this.searchResultsPane?.collapseAllResults(),l.logKeyDown(e.currentTarget,e,"collapse-all-results"))}save(){this.advancedSearchConfig.set(this.buildSearchConfig().toPlainObject())}load(){const e=h.SearchConfig.SearchConfig.fromPlainObject(this.advancedSearchConfig.get());this.search.value=e.query(),this.matchCaseButton.toggled=!e.ignoreCase(),this.matchCaseButtonToggled(),this.regexButton.toggled=e.isRegex(),this.regexButtonToggled()}onAction(){const e=this.buildSearchConfig();e.query()&&e.query().length&&(this.resetSearch(),++this.searchId,this.initScope(),this.isIndexing||this.startIndexing(),this.pendingSearchConfig=e)}get throttlerForTest(){return this.#e}}var L=Object.freeze({__proto__:null,SearchView:P});export{b as SearchResultsPane,v as SearchScope,L as SearchView};
