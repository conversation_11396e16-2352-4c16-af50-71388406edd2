<!--
 * Copyright 2018 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
-->
<!DOCTYPE html>
<html lang="en">
<meta charset="utf-8">
<title>React Native DevTools</title>
<style>
  @media (prefers-color-scheme: dark) {
    body {
      background-color: rgb(41 42 45);
    }
  }
</style>
<meta http-equiv="Content-Security-Policy" content="object-src 'none'; script-src 'self' 'unsafe-eval' https://chrome-devtools-frontend.appspot.com; worker-src 'self' blob:">
<meta name="referrer" content="no-referrer">
<link rel="icon" href="./Images/favicon.ico">
<script defer src="./embedder-static/embedderScript.js"></script>
<script type="module" src="./entrypoints/rn_fusebox/rn_fusebox.js"></script>
<body class="undocked" id="-blink-dev-tools">
