import*as e from"../../../core/i18n/i18n.js";import*as i from"../../../ui/legacy/legacy.js";const t={devices:"Devices",showDevices:"Show Devices"},s=e.i18n.registerUIStrings("panels/settings/emulation/emulation-meta.ts",t),n=e.i18n.getLazilyComputedLocalizedString.bind(void 0,s);let a;i.ViewManager.registerViewExtension({location:"settings-view",commandPrompt:n(t.showDevices),title:n(t.devices),order:30,loadView:async()=>new((await async function(){return a||(a=await import("./emulation.js")),a}()).DevicesSettingsTab.DevicesSettingsTab),id:"devices",settings:["standard-emulated-device-list","custom-emulated-device-list"],iconName:"devices"});
