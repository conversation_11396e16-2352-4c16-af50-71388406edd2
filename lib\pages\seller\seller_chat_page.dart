import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/message_model.dart';
import '../../models/user_model.dart';
import '../../services/auth_service.dart';
import '../../services/message_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import 'seller_chat_detail_page.dart';

class SellerChatPage extends StatefulWidget {
  const SellerChatPage({Key? key}) : super(key: key);

  @override
  State<SellerChatPage> createState() => _SellerChatPageState();
}

class _SellerChatPageState extends State<SellerChatPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            _isSearching
                ? TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search conversations...',
                    border: InputBorder.none,
                    hintStyle: TextStyle(color: Colors.white70),
                  ),
                  style: const TextStyle(color: Colors.white),
                  autofocus: true,
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                )
                : const Text('Messages'),
        centerTitle: true,
        backgroundColor: AppColors.secondary,
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                if (_isSearching) {
                  _isSearching = false;
                  _searchQuery = '';
                  _searchController.clear();
                } else {
                  _isSearching = true;
                }
              });
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          tabs: const [Tab(text: 'All Messages'), Tab(text: 'Unread')],
        ),
      ),
      body: Builder(
        builder: (context) {
          // Get message service and auth service
          final messageService = Provider.of<MessageService>(context);
          final authService = Provider.of<AuthService>(context);

          if (messageService.isLoading) {
            return const Center(
              child: CircularProgressIndicator(color: AppColors.secondary),
            );
          }

          final user = authService.currentUser;
          if (user == null) {
            return const Center(child: Text('Please log in to view messages'));
          }

          // Get conversations for the current seller
          final allConversations = messageService.getConversationsForUser(
            user.id,
            UserType.seller,
          );

          // Filter conversations based on search query
          final filteredConversations =
              _searchQuery.isEmpty
                  ? allConversations
                  : allConversations
                      .where(
                        (conversation) =>
                            conversation.customerName.toLowerCase().contains(
                              _searchQuery.toLowerCase(),
                            ) ||
                            conversation.lastMessageContent
                                .toLowerCase()
                                .contains(_searchQuery.toLowerCase()),
                      )
                      .toList();

          // Filter unread conversations
          final unreadConversations =
              filteredConversations
                  .where((conversation) => conversation.unreadCount > 0)
                  .toList();

          return TabBarView(
            controller: _tabController,
            children: [
              // All Messages Tab
              _buildConversationsList(context, filteredConversations),

              // Unread Messages Tab
              _buildConversationsList(context, unreadConversations),
            ],
          );
        },
      ),
    );
  }

  Widget _buildConversationsList(
    BuildContext context,
    List<Conversation> conversations,
  ) {
    if (conversations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No messages yet',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'When customers contact you, their messages will appear here',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: conversations.length,
      itemBuilder: (context, index) {
        final conversation = conversations[index];
        return _buildConversationCard(context, conversation);
      },
    );
  }

  Widget _buildConversationCard(
    BuildContext context,
    Conversation conversation,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => SellerChatDetailPage(conversation: conversation),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Customer Avatar with Badge
              Stack(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundImage: NetworkImage(conversation.customerImage),
                  ),
                  if (conversation.unreadCount > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: AppColors.secondary,
                          shape: BoxShape.circle,
                        ),
                        child: Text(
                          conversation.unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 16),

              // Conversation Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer Name and Time
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          conversation.customerName,
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _formatTime(conversation.lastMessageTime),
                          style: AppTextStyles.bodySmall.copyWith(
                            color:
                                conversation.unreadCount > 0
                                    ? AppColors.secondary
                                    : AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),

                    // Last Message
                    Text(
                      conversation.lastMessageContent,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color:
                            conversation.unreadCount > 0
                                ? AppColors.textPrimary
                                : AppColors.textSecondary,
                        fontWeight:
                            conversation.unreadCount > 0
                                ? FontWeight.bold
                                : FontWeight.normal,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),

                    // Order or Product Info
                    if (conversation.orderId != null ||
                        conversation.productId != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              conversation.orderId != null
                                  ? AppColors.secondary.withAlpha(
                                    26,
                                  ) // 0.1 opacity
                                  : AppColors.primary.withAlpha(
                                    26,
                                  ), // 0.1 opacity
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              conversation.orderId != null
                                  ? Icons.shopping_bag
                                  : Icons.inventory_2,
                              size: 12,
                              color:
                                  conversation.orderId != null
                                      ? AppColors.secondary
                                      : AppColors.primary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              conversation.orderId != null
                                  ? 'Order #${conversation.orderId}'
                                  : 'Product Inquiry',
                              style: AppTextStyles.bodySmall.copyWith(
                                color:
                                    conversation.orderId != null
                                        ? AppColors.secondary
                                        : AppColors.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(time.year, time.month, time.day);

    if (messageDate == today) {
      return '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
    } else if (messageDate == yesterday) {
      return 'Yesterday';
    } else {
      return '${time.day}/${time.month}/${time.year}';
    }
  }
}
