/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// @generated by enums.py
// clang-format off
#pragma once

#include <cstdint>
#include <yoga/YGEnums.h>
#include <yoga/enums/YogaEnums.h>

namespace facebook::yoga {

enum class Justify : uint8_t {
  FlexStart = YGJustifyFlexStart,
  Center = YGJustifyCenter,
  FlexEnd = YGJustifyFlexEnd,
  SpaceBetween = YGJustifySpaceBetween,
  SpaceAround = YGJustifySpaceAround,
  SpaceEvenly = YGJustifySpaceEvenly,
};

template <>
constexpr int32_t ordinalCount<Justify>() {
  return 6;
}

constexpr Justify scopedEnum(YGJustify unscoped) {
  return static_cast<Justify>(unscoped);
}

constexpr YGJustify unscopedEnum(Justify scoped) {
  return static_cast<YGJustify>(scoped);
}

inline const char* toString(Justify e) {
  return YGJustifyToString(unscopedEnum(e));
}

} // namespace facebook::yoga
