import 'package:shared_preferences/shared_preferences.dart';

class PreferencesService {
  static const String _hasSeenWelcomeKey = 'has_seen_welcome';
  
  // Save that user has seen welcome message
  static Future<void> setHasSeenWelcome() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasSeenWelcomeKey, true);
  }
  
  // Check if user has seen welcome message
  static Future<bool> getHasSeenWelcome() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hasSeenWelcomeKey) ?? false;
  }
  
  // Reset welcome message (for testing)
  static Future<void> resetHasSeenWelcome() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasSeenWelcomeKey, false);
  }
}
