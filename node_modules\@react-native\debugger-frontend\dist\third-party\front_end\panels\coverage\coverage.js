import*as e from"../../core/common/common.js";import*as t from"../../core/platform/platform.js";import*as o from"../../core/sdk/sdk.js";import*as s from"../../models/text_utils/text_utils.js";import*as r from"../../models/workspace/workspace.js";import*as i from"../../core/i18n/i18n.js";import*as n from"../../ui/legacy/components/data_grid/data_grid.js";import*as a from"../../ui/legacy/legacy.js";import*as c from"../../core/host/host.js";import*as l from"../../models/bindings/bindings.js";import*as d from"../../ui/visual_logging/visual_logging.js";var h;!function(e){e.CoverageUpdated="CoverageUpdated",e.CoverageReset="CoverageReset",e.SourceMapResolved="SourceMapResolved"}(h||(h={}));class g extends o.SDKModel.SDKModel{cpuProfilerModel;cssModel;debuggerModel;coverageByURL;coverageByContentProvider;coverageUpdateTimes;suspensionState;pollTimer;currentPollPromise;shouldResumePollingOnResume;jsBacklog;cssBacklog;performanceTraceRecording;sourceMapManager;willResolveSourceMaps;processSourceMapBacklog;constructor(e){super(e),this.cpuProfilerModel=e.model(o.CPUProfilerModel.CPUProfilerModel),this.cssModel=e.model(o.CSSModel.CSSModel),this.debuggerModel=e.model(o.DebuggerModel.DebuggerModel),this.sourceMapManager=this.debuggerModel?.sourceMapManager()||null,this.sourceMapManager?.addEventListener(o.SourceMapManager.Events.SourceMapAttached,this.sourceMapAttached,this),this.coverageByURL=new Map,this.coverageByContentProvider=new Map,this.coverageUpdateTimes=new Set,this.suspensionState="Active",this.pollTimer=null,this.currentPollPromise=null,this.shouldResumePollingOnResume=!1,this.jsBacklog=[],this.cssBacklog=[],this.performanceTraceRecording=!1,this.willResolveSourceMaps=!1,this.processSourceMapBacklog=[]}async start(e){if("Active"!==this.suspensionState)throw Error("Cannot start CoverageModel while it is not active.");const t=[];return this.cssModel&&(this.clearCSS(),this.cssModel.addEventListener(o.CSSModel.Events.StyleSheetAdded,this.handleStyleSheetAdded,this),t.push(this.cssModel.startCoverage())),this.cpuProfilerModel&&t.push(this.cpuProfilerModel.startPreciseCoverage(e,this.preciseCoverageDeltaUpdate.bind(this))),await Promise.all(t),Boolean(this.cssModel||this.cpuProfilerModel)}async sourceMapAttached(e){const t=e.data.client,o=e.data.sourceMap;this.processSourceMapBacklog.push({script:t,sourceMap:o}),this.willResolveSourceMaps||(this.willResolveSourceMaps=!0,setTimeout(this.resolveSourceMapsAndUpdate.bind(this),500))}async resolveSourceMapsAndUpdate(){this.willResolveSourceMaps=!1;const e=this.processSourceMapBacklog;this.processSourceMapBacklog=[],await Promise.all(e.map((({script:e,sourceMap:t})=>this.resolveSourceMap(e,t)))),this.dispatchEventToListeners(h.SourceMapResolved)}async resolveSourceMap(e,t){const o=e.sourceURL,r=this.coverageByURL.get(o);if(r&&0===r.sourcesURLCoverageInfo.size){const o=s.ContentData.ContentData.textOr(await e.requestContentData(),""),i=new s.Text.Text(o),[n,a]=this.calculateSizeForSources(t,i,e.contentLength);r.setSourceSegments(a);for(const e of t.sourceURLs())this.addCoverageForSource(e,n.get(e)||0,r.type(),r)}}async preciseCoverageDeltaUpdate(e,t,o){this.coverageUpdateTimes.add(e);const s=await this.backlogOrProcessJSCoverage(o,e);s.length&&this.dispatchEventToListeners(h.CoverageUpdated,s)}async stop(){await this.stopPolling();const e=[];this.cpuProfilerModel&&e.push(this.cpuProfilerModel.stopPreciseCoverage()),this.cssModel&&(e.push(this.cssModel.stopCoverage()),this.cssModel.removeEventListener(o.CSSModel.Events.StyleSheetAdded,this.handleStyleSheetAdded,this)),await Promise.all(e)}reset(){this.coverageByURL=new Map,this.coverageByContentProvider=new Map,this.coverageUpdateTimes=new Set,this.dispatchEventToListeners(h.CoverageReset)}async startPolling(){this.currentPollPromise||"Active"!==this.suspensionState||await this.pollLoop()}async pollLoop(){this.clearTimer(),this.currentPollPromise=this.pollAndCallback(),await this.currentPollPromise,("Active"===this.suspensionState||this.performanceTraceRecording)&&(this.pollTimer=window.setTimeout((()=>this.pollLoop()),200))}async stopPolling(){this.clearTimer(),await this.currentPollPromise,this.currentPollPromise=null,await this.pollAndCallback()}async pollAndCallback(){if("Suspended"===this.suspensionState&&!this.performanceTraceRecording)return;const e=await this.takeAllCoverage();console.assert("Suspended"!==this.suspensionState||Boolean(this.performanceTraceRecording),"CoverageModel was suspended while polling."),e.length&&this.dispatchEventToListeners(h.CoverageUpdated,e)}clearTimer(){this.pollTimer&&(clearTimeout(this.pollTimer),this.pollTimer=null)}async preSuspendModel(e){"Active"===this.suspensionState&&(this.suspensionState="Suspending","performance-timeline"!==e?this.currentPollPromise&&(await this.stopPolling(),this.shouldResumePollingOnResume=!0):this.performanceTraceRecording=!0)}async suspendModel(e){this.suspensionState="Suspended"}async resumeModel(){}async postResumeModel(){this.suspensionState="Active",this.performanceTraceRecording=!1,this.shouldResumePollingOnResume&&(this.shouldResumePollingOnResume=!1,await this.startPolling())}entries(){return Array.from(this.coverageByURL.values())}getCoverageForUrl(e){return this.coverageByURL.get(e)||null}usageForRange(e,t,o){const s=this.coverageByContentProvider.get(e);return s&&s.usageForRange(t,o)}clearCSS(){for(const e of this.coverageByContentProvider.values()){if(1!==e.type())continue;const t=e.getContentProvider();this.coverageByContentProvider.delete(t);const o=this.coverageByURL.get(e.url());if(!o)continue;const s=`${t.startLine}:${t.startColumn}`;o.removeCoverageEntry(s,e),0===o.numberOfEntries()&&this.coverageByURL.delete(e.url())}if(this.cssModel)for(const e of this.cssModel.getAllStyleSheetHeaders())this.addStyleSheetToCSSCoverage(e)}async takeAllCoverage(){const[e,t]=await Promise.all([this.takeCSSCoverage(),this.takeJSCoverage()]);return[...e,...t]}async takeJSCoverage(){if(!this.cpuProfilerModel)return[];const{coverage:e,timestamp:t}=await this.cpuProfilerModel.takePreciseCoverage();return this.coverageUpdateTimes.add(t),this.backlogOrProcessJSCoverage(e,t)}getCoverageUpdateTimes(){return this.coverageUpdateTimes}async backlogOrProcessJSCoverage(e,t){if(e.length>0&&this.jsBacklog.push({rawCoverageData:e,stamp:t}),"Active"!==this.suspensionState)return[];const o=(e,t)=>e.stamp-t.stamp,s=[];for(const{rawCoverageData:e,stamp:t}of this.jsBacklog.sort(o))s.push(await this.processJSCoverage(e,t));return this.jsBacklog=[],s.flat()}async processJSBacklog(){this.backlogOrProcessJSCoverage([],0)}async processJSCoverage(e,t){if(!this.debuggerModel)return[];const o=[];for(const s of e){const e=this.debuggerModel.scriptForId(s.scriptId);if(!e)continue;const r=[];let i=2;for(const e of s.functions){!1!==e.isBlockCoverage||1===e.ranges.length&&!e.ranges[0].count||(i|=4);for(const t of e.ranges)r.push(t)}const n=await this.addCoverage(e,e.contentLength,e.lineOffset,e.columnOffset,r,i,t);n&&o.push(...n)}return o}handleStyleSheetAdded(e){this.addStyleSheetToCSSCoverage(e.data)}async takeCSSCoverage(){if(!this.cssModel||"Active"!==this.suspensionState)return[];const{coverage:e,timestamp:t}=await this.cssModel.takeCoverageDelta();return this.coverageUpdateTimes.add(t),this.backlogOrProcessCSSCoverage(e,t)}async backlogOrProcessCSSCoverage(e,t){if(e.length>0&&this.cssBacklog.push({rawCoverageData:e,stamp:t}),"Active"!==this.suspensionState)return[];const o=(e,t)=>e.stamp-t.stamp,s=[];for(const{rawCoverageData:e,stamp:t}of this.cssBacklog.sort(o))s.push(await this.processCSSCoverage(e,t));return this.cssBacklog=[],s.flat()}async processCSSCoverage(e,t){if(!this.cssModel)return[];const o=[],s=new Map;for(const t of e){const e=this.cssModel.styleSheetHeaderForId(t.styleSheetId);if(!e)continue;let o=s.get(e);o||(o=[],s.set(e,o)),o.push({startOffset:t.startOffset,endOffset:t.endOffset,count:Number(t.used)})}for(const e of s){const s=e[0],r=e[1],i=await this.addCoverage(s,s.contentLength,s.startLine,s.startColumn,r,1,t);i&&o.push(...i)}return o}static convertToDisjointSegments(e,t){e.sort(((e,t)=>e.startOffset-t.startOffset));const o=[],s=[];for(const t of e){let e=s[s.length-1];for(;e&&e.endOffset<=t.startOffset;)r(e.endOffset,e.count),s.pop(),e=s[s.length-1];r(t.startOffset,e?e.count:0),s.push(t)}for(let e=s.pop();e;e=s.pop())r(e.endOffset,e.count);function r(e,s){const r=o[o.length-1];if(r){if(r.end===e)return;if(r.count===s)return void(r.end=e)}o.push({end:e,count:s,stamp:t})}return o}addStyleSheetToCSSCoverage(e){this.addCoverage(e,e.contentLength,e.startLine,e.startColumn,[],1,Date.now())}calculateSizeForSources(e,t,o){const s=new Map,r=[],i=function(e,o,s,r){if(e===s)return r-o;if(t){const i=t.offsetFromPosition(e,o);return t.offsetFromPosition(s,r)-i}return r},n=e.mappings();if(0===n.length)return[s,r];let a=n[0],c=0;c+=t?t.offsetFromPosition(a.lineNumber,a.columnNumber):i(0,0,a.lineNumber,a.columnNumber),r.push({end:c,sourceUrl:""});for(let l=0;l<n.length;l++){const d=n[l],h=e.findEntryRanges(d.lineNumber,d.columnNumber);if(h){const e=h.range,r=h.sourceURL,a=s.get(r)||0;let c=0;if(l===n.length-1){c=o-t.offsetFromPosition(e.startLine,e.startColumn)}else c=i(e.startLine,e.startColumn,e.endLine,e.endColumn);s.set(r,a+c)}if(c+=i(a.lineNumber,a.columnNumber,d.lineNumber,d.columnNumber),d.sourceURL!==a.sourceURL)if(t){const e=t.offsetFromPosition(d.lineNumber,d.columnNumber);r.push({end:e,sourceUrl:a.sourceURL||""})}else r.push({end:c,sourceUrl:a.sourceURL||""});a=d,l===n.length-1&&r.push({end:o,sourceUrl:d.sourceURL||""})}return[s,r]}async addCoverage(e,t,o,r,i,n,a){const c=[],l=e.contentURL();if(!l)return null;let d=this.coverageByURL.get(l),h=!1;if(!d){h=!0,d=new p(l),this.coverageByURL.set(l,d);const o=await(this.sourceMapManager?.sourceMapForClientPromise(e));if(o){const r=s.ContentData.ContentData.textOr(await e.requestContentData(),""),i=new s.Text.Text(r),[a,l]=this.calculateSizeForSources(o,i,t);d.setSourceSegments(l);for(const e of o.sourceURLs()){const t=this.addCoverageForSource(e,a.get(e)||0,n,d);t&&c.push(t)}}}const u=d.ensureEntry(e,t,o,r,n);this.coverageByContentProvider.set(e,u);const f=g.convertToDisjointSegments(i,a),v=f[f.length-1];v&&v.end<t&&f.push({end:t,stamp:a,count:0});const m=u.mergeCoverage(f);if(!h&&0===m)return null;d.addToSizes(m,0);for(const[e,t]of u.sourceDeltaMap){const o=d.sourcesURLCoverageInfo.get(e);o&&(o.addToSizes(t,0),o.lastSourceUsedRange=u.sourceUsedRangeMap.get(e)||[])}return c.push(u),c}addCoverageForSource(e,t,o,s){const i=r.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(e),n=new f(e,s),a=n.ensureEntry(i,t,0,0,o);return s.sourcesURLCoverageInfo.set(e,n),a}async exportReport(t){const o=[],s=Array.from(this.coverageByURL.keys()).sort();for(const t of s){const s=this.coverageByURL.get(t);if(!s)continue;const r=s.url();r.startsWith("extensions::")||e.ParsedURL.schemeIs(r,"chrome-extension:")||o.push(...await s.entriesForExport())}await t.write(JSON.stringify(o,void 0,2)),t.close()}}function u(e,t){const[o,s]=e.split(":"),[r,i]=t.split(":");return Number.parseInt(o,10)-Number.parseInt(r,10)||Number.parseInt(s,10)-Number.parseInt(i,10)}o.SDKModel.SDKModel.register(g,{capabilities:0,autostart:!1});class p extends e.ObjectWrapper.ObjectWrapper{urlInternal;coverageInfoByLocation;sizeInternal;usedSizeInternal;typeInternal;isContentScriptInternal;sourcesURLCoverageInfo=new Map;sourceSegments;constructor(e){super(),this.urlInternal=e,this.coverageInfoByLocation=new Map,this.sizeInternal=0,this.usedSizeInternal=0,this.isContentScriptInternal=!1}url(){return this.urlInternal}type(){return this.typeInternal}size(){return this.sizeInternal}usedSize(){return this.usedSizeInternal}unusedSize(){return this.sizeInternal-this.usedSizeInternal}usedPercentage(){return 0===this.sizeInternal?0:this.unusedSize()&&this.size()?this.usedSize()/this.size():0}unusedPercentage(){return 0===this.sizeInternal?100:this.unusedSize()/this.size()}isContentScript(){return this.isContentScriptInternal}entries(){return this.coverageInfoByLocation.values()}numberOfEntries(){return this.coverageInfoByLocation.size}removeCoverageEntry(e,t){this.coverageInfoByLocation.delete(e)&&this.addToSizes(-t.getUsedSize(),-t.getSize())}addToSizes(e,t){this.usedSizeInternal+=e,this.sizeInternal+=t,0===e&&0===t||this.dispatchEventToListeners(p.Events.SizesChanged)}setSourceSegments(e){this.sourceSegments=e}ensureEntry(e,t,s,r,i){const n=`${s}:${r}`;let a=this.coverageInfoByLocation.get(n);return 2&i&&!this.coverageInfoByLocation.size&&e instanceof o.Script.Script&&(this.isContentScriptInternal=e.isContentScript()),this.typeInternal|=i,a?(a.addCoverageType(i),a):(2&i&&!this.coverageInfoByLocation.size&&e instanceof o.Script.Script&&(this.isContentScriptInternal=e.isContentScript()),a=new m(e,t,s,r,i,this),this.coverageInfoByLocation.set(n,a),this.addToSizes(0,t),a)}async getFullText(){let e=!1;const t=this.url();for(const o of this.coverageInfoByLocation.values()){const{lineOffset:s,columnOffset:r}=o.getOffsets();if(s||r){e=Boolean(t);break}}if(!e)return null;const r=o.ResourceTreeModel.ResourceTreeModel.resourceForURL(t);if(!r)return null;const i=s.ContentData.ContentData.textOr(await r.requestContentData(),"");return new s.Text.Text(i)}entriesForExportBasedOnFullText(e){const t=Array.from(this.coverageInfoByLocation.keys()).sort(u),o={url:this.url(),ranges:[],text:e.value()};for(const s of t){const t=this.coverageInfoByLocation.get(s);if(!t)continue;const{lineOffset:r,columnOffset:i}=t.getOffsets(),n=e?e.offsetFromPosition(r,i):0;o.ranges.push(...t.rangesForExport(n))}return o}async entriesForExportBasedOnContent(){const e=Array.from(this.coverageInfoByLocation.keys()).sort(u),t=[];for(const o of e){const e=this.coverageInfoByLocation.get(o);if(!e)continue;const r={url:this.url(),ranges:e.rangesForExport(),text:s.ContentData.ContentData.textOr(await e.getContentProvider().requestContentData(),null)};t.push(r)}return t}async entriesForExport(){const e=await this.getFullText();return e?[await this.entriesForExportBasedOnFullText(e)]:this.entriesForExportBasedOnContent()}}class f extends p{generatedURLCoverageInfo;lastSourceUsedRange=[];constructor(e,t){super(e),this.generatedURLCoverageInfo=t}}!function(e){let t;!function(e){e.SizesChanged="SizesChanged"}(t=e.Events||(e.Events={}))}(p||(p={}));const v=(e,t)=>{const o=[];let s=0,r=0;for(;s<e.length&&r<t.length;){const i=e[s],n=t[r],a=(i.count||0)+(n.count||0),c=Math.min(i.end,n.end),l=o[o.length-1],d=Math.min(i.stamp,n.stamp);l&&l.count===a&&l.stamp===d?l.end=c:o.push({end:c,count:a,stamp:d}),i.end<=n.end&&s++,i.end>=n.end&&r++}for(;s<e.length;s++)o.push(e[s]);for(;r<t.length;r++)o.push(t[r]);return o};class m{contentProvider;size;usedSize;statsByTimestamp;lineOffset;columnOffset;coverageType;segments;generatedUrlCoverageInfo;sourceUsedSizeMap=new Map;sourceDeltaMap=new Map;sourceUsedRangeMap=new Map;constructor(e,t,o,s,r,i){this.contentProvider=e,this.size=t,this.usedSize=0,this.statsByTimestamp=new Map,this.lineOffset=o,this.columnOffset=s,this.coverageType=r,this.generatedUrlCoverageInfo=i,this.segments=[]}getContentProvider(){return this.contentProvider}url(){return this.contentProvider.contentURL()}type(){return this.coverageType}addCoverageType(e){this.coverageType|=e}getOffsets(){return{lineOffset:this.lineOffset,columnOffset:this.columnOffset}}mergeCoverage(e){const t=this.usedSize;return this.segments=v(this.segments,e),this.updateStats(),this.generatedUrlCoverageInfo.sourceSegments&&this.generatedUrlCoverageInfo.sourceSegments.length>0&&this.updateSourceCoverage(),this.usedSize-t}usedByTimestamp(){return this.statsByTimestamp}getSize(){return this.size}getUsedSize(){return this.usedSize}usageForRange(e,o){let s=t.ArrayUtilities.upperBound(this.segments,e,((e,t)=>e-t.end));for(;s<this.segments.length&&this.segments[s].end<o;++s)if(this.segments[s].count)return!0;return s<this.segments.length&&Boolean(this.segments[s].count)}updateStats(){this.statsByTimestamp=new Map,this.usedSize=0;let e=0;for(const t of this.segments){let o=this.statsByTimestamp.get(t.stamp);if(void 0===o&&(o=0),t.count){const s=t.end-e;this.usedSize+=s,this.statsByTimestamp.set(t.stamp,o+s)}e=t.end}}updateSourceCoverage(){const e=new Map;this.sourceDeltaMap=new Map,this.sourceUsedRangeMap=new Map;const t=this.generatedUrlCoverageInfo.sourceSegments||[];let o=0,s=0;for(const r of this.segments){const i=r.end;if(r.count)for(let r=s;r<t.length;r++){const n=0===r?0:t[r-1].end+1,a=t[r].end,c=Math.max(o,n),l=Math.min(i,a);if(c<=l){const o=l-c+1,i={start:c,end:l};e.has(t[r].sourceUrl)?e.set(t[r].sourceUrl,e.get(t[r].sourceUrl)+o):e.set(t[r].sourceUrl,o),this.sourceUsedRangeMap.has(t[r].sourceUrl)?this.sourceUsedRangeMap.get(t[r].sourceUrl)?.push(i):this.sourceUsedRangeMap.set(t[r].sourceUrl,[i]),s=r}if(i<a)break}o=i+1}for(const[t,o]of e){const e=this.sourceUsedSizeMap.get(t)||0;e!==o&&(this.sourceUsedSizeMap.set(t,o),this.sourceDeltaMap.set(t,o-e))}}rangesForExport(e=0){const t=[];let o=0;for(const s of this.segments){if(s.count){const r=t.length>0?t[t.length-1]:null;r&&r.end===o+e?r.end=s.end+e:t.push({start:o+e,end:s.end+e})}o=s.end}return t}}var S=Object.freeze({__proto__:null,get Events(){return h},CoverageModel:g,get URLCoverageInfo(){return p},SourceURLCoverageInfo:f,mergeSegments:v,CoverageInfo:m});const C=new CSSStyleSheet;C.replaceSync(".data-grid{border:none}.data-grid td .url-outer{width:100%;display:inline-flex;justify-content:flex-start}.data-grid td .url-outer .filter-highlight{font-weight:bold}.data-grid td .url-prefix{overflow-x:hidden;text-overflow:ellipsis}.data-grid td .url-suffix{flex:none}.data-grid td .bar{display:inline-block;height:8px;border:1px solid transparent}.data-grid td .bar-unused-size{background-color:var(--app-color-coverage-unused)}.data-grid td .bar-used-size{background-color:var(--app-color-coverage-used)}.data-grid td .percent-value{width:7ex;display:inline-block;color:var(--sys-color-on-surface-subtle)}@media (forced-colors: active){.data-grid td .bar-container{forced-color-adjust:none}.data-grid td .bar-unused-size{background-color:ButtonText}.data-grid td .bar-used-size{background-color:ButtonFace}.data-grid td .bar{border-color:ButtonText}.data-grid .selected td .bar{border-top-color:HighlightText;border-bottom-color:HighlightText}.data-grid .selected td .bar:last-child{border-right-color:HighlightText}.data-grid .selected td .bar:first-child{border-left-color:HighlightText}.data-grid:focus tr.selected span.percent-value{color:HighlightText}}\n/*# sourceURL=coverageListView.css */\n");const b={css:"CSS",jsPerFunction:"JS (per function)",jsPerBlock:"JS (per block)",url:"URL",type:"Type",totalBytes:"Total Bytes",unusedBytes:"Unused Bytes",usageVisualization:"Usage Visualization",codeCoverage:"Code Coverage",jsCoverageWithPerFunction:"JS coverage with per function granularity: Once a function was executed, the whole function is marked as covered.",jsCoverageWithPerBlock:"JS coverage with per block granularity: Once a block of JavaScript was executed, that block is marked as covered.",sBytes:"{n, plural, =1 {# byte} other {# bytes}}",sBytesS:"{n, plural, =1 {# byte, {percentage}} other {# bytes, {percentage}}}",sBytesSBelongToFunctionsThatHave:"{PH1} bytes ({PH2}) belong to functions that have not (yet) been executed.",sBytesSBelongToBlocksOf:"{PH1} bytes ({PH2}) belong to blocks of JavaScript that have not (yet) been executed.",sBytesSBelongToFunctionsThatHaveExecuted:"{PH1} bytes ({PH2}) belong to functions that have executed at least once.",sBytesSBelongToBlocksOfJavascript:"{PH1} bytes ({PH2}) belong to blocks of JavaScript that have executed at least once.",sOfFileUnusedSOfFileUsed:"{PH1} % of file unused, {PH2} % of file used"},y=i.i18n.registerUIStrings("panels/coverage/CoverageListView.ts",b),B=i.i18n.getLocalizedString.bind(void 0,y);function T(e){const t=[];return 1&e&&t.push(B(b.css)),4&e?t.push(B(b.jsPerFunction)):2&e&&t.push(B(b.jsPerBlock)),t.join("+")}class w extends a.Widget.VBox{nodeForCoverageInfo;isVisibleFilter;highlightRegExp;dataGrid;constructor(e){super(!0),this.nodeForCoverageInfo=new Map,this.isVisibleFilter=e,this.highlightRegExp=null;const t=[{id:"url",title:B(b.url),width:"250px",weight:3,fixedWidth:!1,sortable:!0,disclosure:!0},{id:"type",title:B(b.type),width:"45px",weight:1,fixedWidth:!0,sortable:!0},{id:"size",title:B(b.totalBytes),width:"60px",fixedWidth:!0,sortable:!0,align:"right",weight:1},{id:"unused-size",title:B(b.unusedBytes),width:"100px",fixedWidth:!0,sortable:!0,align:"right",sort:n.DataGrid.Order.Descending,weight:1},{id:"bars",title:B(b.usageVisualization),width:"250px",fixedWidth:!1,sortable:!0,weight:1}];this.dataGrid=new n.SortableDataGrid.SortableDataGrid({displayName:B(b.codeCoverage),columns:t,editCallback:void 0,refreshCallback:void 0,deleteCallback:void 0}),this.dataGrid.setResizeMethod("last"),this.dataGrid.setStriped(!0),this.dataGrid.element.classList.add("flex-auto"),this.dataGrid.addEventListener("OpenedNode",this.onOpenedNode,this),this.dataGrid.addEventListener("SortingChanged",this.sortingChanged,this);const o=this.dataGrid.asWidget();o.show(this.contentElement),this.setDefaultFocusedChild(o)}update(e){let t=!1;const o=e.reduce(((e,t)=>Math.max(e,t.size())),0),s=this.dataGrid.rootNode();for(const r of e){let e=this.nodeForCoverageInfo.get(r);e?this.isVisibleFilter(e.coverageInfo)&&(t=e.refreshIfNeeded(o)||t,r.sourcesURLCoverageInfo.size>0&&this.updateSourceNodes(r.sourcesURLCoverageInfo,o,e)):(e=new I(r,o),this.nodeForCoverageInfo.set(r,e),this.isVisibleFilter(e.coverageInfo)&&(s.appendChild(e),r.sourcesURLCoverageInfo.size>0&&this.createSourceNodes(r.sourcesURLCoverageInfo,o,e),t=!0))}t&&this.sortingChanged()}updateSourceNodes(e,t,o){let s=!1;for(const o of e.values()){const e=this.nodeForCoverageInfo.get(o);if(!e){s=!0;break}e.refreshIfNeeded(t)}s&&this.createSourceNodes(e,t,o)}async createSourceNodes(e,t,o){for(const s of e.values()){const e=new I(s,t);o.appendChild(e),this.nodeForCoverageInfo.set(s,e)}}reset(){this.nodeForCoverageInfo.clear(),this.dataGrid.rootNode().removeChildren()}updateFilterAndHighlight(e){this.highlightRegExp=e;let t=!1;for(const e of this.nodeForCoverageInfo.values()){const o=this.isVisibleFilter(e.coverageInfo),s=Boolean(e.parent);o&&e.setHighlight(this.highlightRegExp),o!==s&&(t=!0,o?this.appendNodeByType(e):e.remove())}t&&this.sortingChanged()}appendNodeByType(e){if(e.coverageInfo instanceof f){const t=this.nodeForCoverageInfo.get(e.coverageInfo.generatedURLCoverageInfo);t?.appendChild(e)}else this.dataGrid.rootNode().appendChild(e)}selectByUrl(e){for(const[t,o]of this.nodeForCoverageInfo.entries())if(t.url()===e){o.revealAndSelect();break}}onOpenedNode(){this.revealSourceForSelectedNode()}async revealSourceForSelectedNode(){const t=this.dataGrid.selectedNode;if(!t)return;const o=t.coverageInfo,s=r.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(o.url());s&&this.dataGrid.selectedNode===t&&e.Revealer.reveal(s)}sortingChanged(){const e=this.dataGrid.sortColumnId();if(!e)return;const t=I.sortFunctionForColumn(e);t&&this.dataGrid.sortNodes(t,!this.dataGrid.isSortOrderAscending())}wasShown(){super.wasShown(),this.registerCSSFiles([C])}}let R=null;function x(){return R||(R=new Intl.NumberFormat(i.DevToolsLocale.DevToolsLocale.instance().locale,{style:"percent",maximumFractionDigits:1})),R}let P=null;function M(){return P||(P=new Intl.NumberFormat(i.DevToolsLocale.DevToolsLocale.instance().locale)),P}class I extends n.SortableDataGrid.SortableDataGridNode{coverageInfo;lastUsedSize;url;maxSize;highlightRegExp;constructor(e,t){super(),this.coverageInfo=e,this.url=e.url(),this.maxSize=t,this.highlightRegExp=null}setHighlight(e){this.highlightRegExp!==e&&(this.highlightRegExp=e,this.refresh())}refreshIfNeeded(e){return(this.lastUsedSize!==this.coverageInfo.usedSize()||e!==this.maxSize)&&(this.lastUsedSize=this.coverageInfo.usedSize(),this.maxSize=e,this.refresh(),!0)}createCell(e){const t=this.createTD(e);switch(e){case"url":{a.Tooltip.Tooltip.install(t,this.url);const o=t.createChild("div","url-outer"),s=o.createChild("div","url-prefix"),r=o.createChild("div","url-suffix"),i=/^(.*)(\/[^/]*)$/.exec(this.url);s.textContent=i?i[1]:this.url,r.textContent=i?i[2]:"",this.highlightRegExp&&this.highlight(o,this.url),this.setCellAccessibleName(this.url,t,e);break}case"type":t.textContent=T(this.coverageInfo.type()),4&this.coverageInfo.type()?a.Tooltip.Tooltip.install(t,B(b.jsCoverageWithPerFunction)):2&this.coverageInfo.type()&&a.Tooltip.Tooltip.install(t,B(b.jsCoverageWithPerBlock));break;case"size":{const o=this.coverageInfo.size()||0,s=t.createChild("span"),r=M().format(o);s.textContent=r;const i=B(b.sBytes,{n:o});this.setCellAccessibleName(i,t,e);break}case"unused-size":{const o=this.coverageInfo.unusedSize()||0,s=t.createChild("span"),r=t.createChild("span","percent-value"),i=M().format(o);s.textContent=i;const n=x().format(this.coverageInfo.unusedPercentage());r.textContent=n;const a=B(b.sBytesS,{n:o,percentage:n});this.setCellAccessibleName(a,t,e);break}case"bars":{const o=t.createChild("div","bar-container"),s=x().format(this.coverageInfo.unusedPercentage()),r=x().format(this.coverageInfo.usedPercentage());if(this.coverageInfo.unusedSize()>0){const e=o.createChild("div","bar bar-unused-size");e.style.width=(this.coverageInfo.unusedSize()/this.maxSize*100||0)+"%",4&this.coverageInfo.type()?a.Tooltip.Tooltip.install(e,B(b.sBytesSBelongToFunctionsThatHave,{PH1:this.coverageInfo.unusedSize(),PH2:s})):2&this.coverageInfo.type()&&a.Tooltip.Tooltip.install(e,B(b.sBytesSBelongToBlocksOf,{PH1:this.coverageInfo.unusedSize(),PH2:s}))}if(this.coverageInfo.usedSize()>0){const e=o.createChild("div","bar bar-used-size");e.style.width=(this.coverageInfo.usedSize()/this.maxSize*100||0)+"%",4&this.coverageInfo.type()?a.Tooltip.Tooltip.install(e,B(b.sBytesSBelongToFunctionsThatHaveExecuted,{PH1:this.coverageInfo.usedSize(),PH2:r})):2&this.coverageInfo.type()&&a.Tooltip.Tooltip.install(e,B(b.sBytesSBelongToBlocksOfJavascript,{PH1:this.coverageInfo.usedSize(),PH2:r}))}this.setCellAccessibleName(B(b.sOfFileUnusedSOfFileUsed,{PH1:s,PH2:r}),t,e)}}return t}highlight(e,t){if(!this.highlightRegExp)return;const o=this.highlightRegExp.exec(t);if(!o||!o.length)return;const r=new s.TextRange.SourceRange(o.index,o[0].length);a.UIUtils.highlightRangesWithStyleClass(e,[r],"filter-highlight")}static sortFunctionForColumn(e){const t=(e,t)=>e.url.localeCompare(t.url);switch(e){case"url":return t;case"type":return(e,o)=>{const s=T(e.coverageInfo.type()),r=T(o.coverageInfo.type());return s.localeCompare(r)||t(e,o)};case"size":return(e,o)=>e.coverageInfo.size()-o.coverageInfo.size()||t(e,o);case"bars":case"unused-size":return(e,o)=>e.coverageInfo.unusedSize()-o.coverageInfo.unusedSize()||t(e,o);default:return console.assert(!1,"Unknown sort field: "+e),null}}}var U=Object.freeze({__proto__:null,coverageTypeToString:T,CoverageListView:w,GridNode:I});const k="coverage";class L{coverageModel;textByProvider;uiSourceCodeByContentProvider;#e;#t;#o;constructor(e,o,s,i){this.coverageModel=e,this.#e=o,this.#t=s,this.#o=i,this.textByProvider=new Map,this.uiSourceCodeByContentProvider=new t.MapUtilities.Multimap;for(const e of this.#e.uiSourceCodes())e.setDecorationData(k,this);this.#e.addEventListener(r.Workspace.Events.UISourceCodeAdded,this.onUISourceCodeAdded,this)}reset(){for(const e of this.#e.uiSourceCodes())e.setDecorationData(k,void 0)}dispose(){this.reset(),this.#e.removeEventListener(r.Workspace.Events.UISourceCodeAdded,this.onUISourceCodeAdded,this)}update(e){for(const t of e)for(const e of this.uiSourceCodeByContentProvider.get(t.getContentProvider()))e.setDecorationData(k,this)}async usageByLine(e,t){const o=[];await this.updateTexts(e,t);for(const{startLine:s,startColumn:r,endLine:i,endColumn:n}of t){const t=this.rawLocationsForSourceLocation(e,s,r),a=this.rawLocationsForSourceLocation(e,i,n),[c,l]=await Promise.all([t,a]);let d;for(let e=0,t=0;e<c.length;++e){const o=c[e];for(;t<l.length&&L.compareLocations(o,l[t])>=0;)++t;if(t>=l.length||l[t].id!==o.id)continue;const s=l[t++],r=this.textByProvider.get(s.contentProvider);if(!r)continue;const i=r.value();let n=Math.min(r.offsetFromPosition(o.line,o.column),i.length-1),a=Math.min(r.offsetFromPosition(s.line,s.column),i.length-1);for(;n<=a&&/\s/.test(i[n]);)++n;for(;n<=a&&/\s/.test(i[a]);)--a;if(n<=a&&(d=this.coverageModel.usageForRange(s.contentProvider,n,a)),d)break}o.push(d)}return o}async updateTexts(e,t){const o=[];for(const s of t)for(const t of await this.rawLocationsForSourceLocation(e,s.startLine,0))this.textByProvider.has(t.contentProvider)||(this.textByProvider.set(t.contentProvider,null),this.uiSourceCodeByContentProvider.set(t.contentProvider,e),o.push(this.updateTextForProvider(t.contentProvider)));await Promise.all(o)}async updateTextForProvider(e){const t=s.ContentData.ContentData.textOr(await e.requestContentData(),"");this.textByProvider.set(e,new s.Text.Text(t))}async rawLocationsForSourceLocation(e,t,o){const s=[],i=e.contentType();if(i.hasScripts()){let r=await this.#t.uiLocationToRawLocations(e,t,o);r=r.filter((e=>Boolean(e.script())));for(const e of r){const t=e.script();t&&(t.isInlineScript()&&i.isDocument()&&(e.lineNumber-=t.lineOffset,e.lineNumber||(e.columnNumber-=t.columnOffset)),s.push({id:`js:${e.scriptId}`,contentProvider:t,line:e.lineNumber,column:e.columnNumber}))}}if(i.isStyleSheet()||i.isDocument()){const n=this.#o.uiLocationToRawLocations(new r.UISourceCode.UILocation(e,t,o));for(const e of n){const t=e.header();t&&(t.isInline&&i.isDocument()&&(e.lineNumber-=t.startLine,e.lineNumber||(e.columnNumber-=t.startColumn)),s.push({id:`css:${e.styleSheetId}`,contentProvider:t,line:e.lineNumber,column:e.columnNumber}))}}return s.sort(L.compareLocations)}static compareLocations(e,t){return e.id.localeCompare(t.id)||e.line-t.line||e.column-t.column}onUISourceCodeAdded(e){e.data.setDecorationData(k,this)}}var F=Object.freeze({__proto__:null,decoratorType:k,CoverageDecorationManager:L});const E=new CSSStyleSheet;E.replaceSync(":host{overflow:hidden}.coverage-toolbar-container{display:flex;border-bottom:1px solid var(--sys-color-divider);flex:0 0 auto}.coverage-toolbar{display:inline-block;width:100%}.coverage-toolbar-summary{background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);padding-left:5px;flex:0 0 19px;display:flex;padding-right:5px}.coverage-toolbar-summary .coverage-message{padding-top:2px;padding-left:1ex;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.coverage-results{overflow-y:auto;display:flex;flex:auto}.landing-page,\n.bfcache-page,\n.prerender-page{justify-content:center;align-items:center;padding:20px}.landing-page .message,\n.bfcache-page .message,\n.prerender-page .message{white-space:pre-line;text-align:center}\n/*# sourceURL=coverageView.css */\n");const z={chooseCoverageGranularityPer:"Choose coverage granularity: Per function has low overhead, per block has significant overhead.",perFunction:"Per function",perBlock:"Per block",filterByUrl:"Filter by URL",filterCoverageByType:"Filter coverage by type",all:"All",css:"CSS",javascript:"JavaScript",includeExtensionContentScripts:"Include extension content scripts",contentScripts:"Content scripts",clickTheReloadButtonSToReloadAnd:"Click the reload button {PH1} to reload and start capturing coverage.",clickTheRecordButtonSToStart:"Click the record button {PH1} to start capturing coverage.",bfcacheNoCapture:"Could not capture coverage info because the page was served from the back/forward cache.",activationNoCapture:"Could not capture coverage info because the page was prerendered in the background.",reloadPrompt:"Click the reload button {PH1} to reload and get coverage.",filteredSTotalS:"Filtered: {PH1}  Total: {PH2}",sOfSSUsedSoFarSUnused:"{PH1} of {PH2} ({PH3}%) used so far, {PH4} unused."},A=i.i18n.registerUIStrings("panels/coverage/CoverageView.ts",z),O=i.i18n.getLocalizedString.bind(void 0,A);let N;class D extends a.Widget.VBox{model;decorationManager;coverageTypeComboBox;coverageTypeComboBoxSetting;toggleRecordAction;toggleRecordButton;inlineReloadButton;startWithReloadButton;clearAction;exportAction;textFilterRegExp;filterInput;typeFilterValue;filterByTypeComboBox;showContentScriptsSetting;contentScriptsCheckbox;coverageResultsElement;landingPage;bfcacheReloadPromptPage;activationReloadPromptPage;listView;statusToolbarElement;statusMessageElement;constructor(){super(!0),this.element.setAttribute("jslog",`${d.panel("coverage").track({resize:!0})}`),this.model=null,this.decorationManager=null;const t=this.contentElement.createChild("div","coverage-toolbar-container");t.setAttribute("jslog",`${d.toolbar()}`);const s=new a.Toolbar.Toolbar("coverage-toolbar",t);s.makeWrappable(!0),this.coverageTypeComboBox=new a.Toolbar.ToolbarComboBox(this.onCoverageTypeComboBoxSelectionChanged.bind(this),O(z.chooseCoverageGranularityPer),void 0,"coverage-type");const r=[{label:O(z.perFunction),value:6},{label:O(z.perBlock),value:2}];for(const e of r)this.coverageTypeComboBox.addOption(this.coverageTypeComboBox.createOption(e.label,`${e.value}`));this.coverageTypeComboBoxSetting=e.Settings.Settings.instance().createSetting("coverage-view-coverage-type",0),this.coverageTypeComboBox.setSelectedIndex(this.coverageTypeComboBoxSetting.get()),this.coverageTypeComboBox.setEnabled(!0),s.appendToolbarItem(this.coverageTypeComboBox),this.toggleRecordAction=a.ActionRegistry.ActionRegistry.instance().getAction("coverage.toggle-recording"),this.toggleRecordButton=a.Toolbar.Toolbar.createActionButton(this.toggleRecordAction),s.appendToolbarItem(this.toggleRecordButton);const i=o.TargetManager.TargetManager.instance().primaryPageTarget(),n=i&&i.model(o.ResourceTreeModel.ResourceTreeModel);this.inlineReloadButton=null,n&&(this.startWithReloadButton=a.Toolbar.Toolbar.createActionButtonForId("coverage.start-with-reload"),s.appendToolbarItem(this.startWithReloadButton),this.toggleRecordButton.setEnabled(!1),this.toggleRecordButton.setVisible(!1)),this.clearAction=a.ActionRegistry.ActionRegistry.instance().getAction("coverage.clear"),this.clearAction.setEnabled(!1),s.appendToolbarItem(a.Toolbar.Toolbar.createActionButton(this.clearAction)),s.appendSeparator(),this.exportAction=a.ActionRegistry.ActionRegistry.instance().getAction("coverage.export"),this.exportAction.setEnabled(!1),s.appendToolbarItem(a.Toolbar.Toolbar.createActionButton(this.exportAction)),this.textFilterRegExp=null,s.appendSeparator(),this.filterInput=new a.Toolbar.ToolbarFilter(O(z.filterByUrl),.4,1),this.filterInput.setEnabled(!1),this.filterInput.addEventListener("TextChanged",this.onFilterChanged,this),s.appendToolbarItem(this.filterInput),s.appendSeparator(),this.typeFilterValue=null,this.filterByTypeComboBox=new a.Toolbar.ToolbarComboBox(this.onFilterByTypeChanged.bind(this),O(z.filterCoverageByType),void 0,"coverage-by-type");const c=[{label:O(z.all),value:""},{label:O(z.css),value:1},{label:O(z.javascript),value:6}];for(const e of c)this.filterByTypeComboBox.addOption(this.filterByTypeComboBox.createOption(e.label,`${e.value}`));this.filterByTypeComboBox.setSelectedIndex(0),this.filterByTypeComboBox.setEnabled(!1),s.appendToolbarItem(this.filterByTypeComboBox),s.appendSeparator(),this.showContentScriptsSetting=e.Settings.Settings.instance().createSetting("show-content-scripts",!1),this.showContentScriptsSetting.addChangeListener(this.onFilterChanged,this),this.contentScriptsCheckbox=new a.Toolbar.ToolbarSettingCheckbox(this.showContentScriptsSetting,O(z.includeExtensionContentScripts),O(z.contentScripts)),this.contentScriptsCheckbox.setEnabled(!1),s.appendToolbarItem(this.contentScriptsCheckbox),this.coverageResultsElement=this.contentElement.createChild("div","coverage-results"),this.landingPage=this.buildLandingPage(),this.bfcacheReloadPromptPage=this.buildReloadPromptPage(O(z.bfcacheNoCapture),"bfcache-page"),this.activationReloadPromptPage=this.buildReloadPromptPage(O(z.activationNoCapture),"prerender-page"),this.listView=new w(this.isVisible.bind(this,!1)),this.statusToolbarElement=this.contentElement.createChild("div","coverage-toolbar-summary"),this.statusMessageElement=this.statusToolbarElement.createChild("div","coverage-message"),this.landingPage.show(this.coverageResultsElement)}static instance(){return N||(N=new D),N}static removeInstance(){N=void 0}buildLandingPage(){const e=new a.Widget.VBox;let t;if(this.startWithReloadButton)this.inlineReloadButton=a.UIUtils.createInlineButton(a.Toolbar.Toolbar.createActionButtonForId("coverage.start-with-reload")),t=i.i18n.getFormatLocalizedString(A,z.clickTheReloadButtonSToReloadAnd,{PH1:this.inlineReloadButton});else{const e=a.UIUtils.createInlineButton(a.Toolbar.Toolbar.createActionButton(this.toggleRecordAction));t=i.i18n.getFormatLocalizedString(A,z.clickTheRecordButtonSToStart,{PH1:e})}return t.classList.add("message"),e.contentElement.appendChild(t),e.element.classList.add("landing-page"),e}buildReloadPromptPage(e,t){const o=new a.Widget.VBox,s=document.createElement("div");s.classList.add("message"),s.textContent=e,o.contentElement.appendChild(s),this.inlineReloadButton=a.UIUtils.createInlineButton(a.Toolbar.Toolbar.createActionButtonForId("inspector-main.reload"));const r=i.i18n.getFormatLocalizedString(A,z.reloadPrompt,{PH1:this.inlineReloadButton});return r.classList.add("message"),o.contentElement.appendChild(r),o.element.classList.add(t),o}clear(){this.model&&this.model.reset(),this.reset()}reset(){this.decorationManager&&(this.decorationManager.dispose(),this.decorationManager=null),this.listView.reset(),this.listView.detach(),this.landingPage.show(this.coverageResultsElement),this.statusMessageElement.textContent="",this.filterInput.setEnabled(!1),this.filterByTypeComboBox.setEnabled(!1),this.contentScriptsCheckbox.setEnabled(!1),this.exportAction.setEnabled(!1)}toggleRecording(){!this.toggleRecordAction.toggled()?this.startRecording({reload:!1,jsCoveragePerBlock:this.isBlockCoverageSelected()}):this.stopRecording()}isBlockCoverageSelected(){const e=this.coverageTypeComboBox.selectedOption();return 2===Number(e?e.value:Number.NaN)}selectCoverageType(e){const t=e?1:0;this.coverageTypeComboBox.setSelectedIndex(t)}onCoverageTypeComboBoxSelectionChanged(){this.coverageTypeComboBoxSetting.set(this.coverageTypeComboBox.selectedIndex())}async ensureRecordingStarted(){this.toggleRecordAction.toggled()&&await this.stopRecording(),await this.startRecording({reload:!1,jsCoveragePerBlock:!1})}async startRecording(e){let t,s;this.startWithReloadButton&&this.startWithReloadButton.element.hasFocus()||this.inlineReloadButton&&this.inlineReloadButton.hasFocus()?s=!0:this.hasFocus()&&(t=!0),this.reset();const i=o.TargetManager.TargetManager.instance().primaryPageTarget();if(!i)return;const{reload:n,jsCoveragePerBlock:a}={reload:!1,jsCoveragePerBlock:!1,...e};if(this.model&&!n||(this.model=i.model(g)),!this.model)return;c.userMetrics.actionTaken(c.UserMetrics.Action.CoverageStarted),a&&c.userMetrics.actionTaken(c.UserMetrics.Action.CoverageStartedPerBlock);if(!await this.model.start(Boolean(a)))return;this.selectCoverageType(Boolean(a)),this.model.addEventListener(h.CoverageUpdated,this.onCoverageDataReceived,this),this.model.addEventListener(h.SourceMapResolved,this.updateListView,this);const d=i.model(o.ResourceTreeModel.ResourceTreeModel);o.TargetManager.TargetManager.instance().addModelListener(o.ResourceTreeModel.ResourceTreeModel,o.ResourceTreeModel.Events.PrimaryPageChanged,this.onPrimaryPageChanged,this),this.decorationManager=new L(this.model,r.Workspace.WorkspaceImpl.instance(),l.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance(),l.CSSWorkspaceBinding.CSSWorkspaceBinding.instance()),this.toggleRecordAction.setToggled(!0),this.clearAction.setEnabled(!1),this.startWithReloadButton&&(this.startWithReloadButton.setEnabled(!1),this.startWithReloadButton.setVisible(!1),this.toggleRecordButton.setEnabled(!0),this.toggleRecordButton.setVisible(!0),s&&this.toggleRecordButton.focus()),this.coverageTypeComboBox.setEnabled(!1),this.filterInput.setEnabled(!0),this.filterByTypeComboBox.setEnabled(!0),this.contentScriptsCheckbox.setEnabled(!0),this.landingPage.isShowing()&&this.landingPage.detach(),this.listView.show(this.coverageResultsElement),t&&!s&&this.listView.focus(),n&&d?d.reloadPage():this.model.startPolling()}onCoverageDataReceived(e){const t=e.data;this.updateViews(t)}updateListView(){this.listView.update(this.model&&this.model.entries()||[])}async stopRecording(){o.TargetManager.TargetManager.instance().removeModelListener(o.ResourceTreeModel.ResourceTreeModel,o.ResourceTreeModel.Events.PrimaryPageChanged,this.onPrimaryPageChanged,this),this.hasFocus()&&this.listView.focus(),this.model&&(await this.model.stop(),this.model.removeEventListener(h.CoverageUpdated,this.onCoverageDataReceived,this)),this.toggleRecordAction.setToggled(!1),this.coverageTypeComboBox.setEnabled(!0),this.startWithReloadButton&&(this.startWithReloadButton.setEnabled(!0),this.startWithReloadButton.setVisible(!0),this.toggleRecordButton.setEnabled(!1),this.toggleRecordButton.setVisible(!1)),this.clearAction.setEnabled(!0)}processBacklog(){this.model&&this.model.processJSBacklog()}async onPrimaryPageChanged(e){const t=e.data.frame,o=t.resourceTreeModel().target().model(g);if(o){if(this.model!==o){this.model&&(await this.model.stop(),this.model.removeEventListener(h.CoverageUpdated,this.onCoverageDataReceived,this)),this.model=o;if(!await this.model.start(this.isBlockCoverageSelected()))return;this.model.addEventListener(h.CoverageUpdated,this.onCoverageDataReceived,this),this.decorationManager=new L(this.model,r.Workspace.WorkspaceImpl.instance(),l.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance(),l.CSSWorkspaceBinding.CSSWorkspaceBinding.instance())}this.bfcacheReloadPromptPage.isShowing()&&(this.bfcacheReloadPromptPage.detach(),this.listView.show(this.coverageResultsElement)),this.activationReloadPromptPage.isShowing()&&(this.activationReloadPromptPage.detach(),this.listView.show(this.coverageResultsElement)),t.backForwardCacheDetails.restoredFromCache&&(this.listView.detach(),this.bfcacheReloadPromptPage.show(this.coverageResultsElement)),"Activation"===e.data.type&&(this.listView.detach(),this.activationReloadPromptPage.show(this.coverageResultsElement)),this.model.reset(),this.decorationManager&&this.decorationManager.reset(),this.listView.reset(),this.model.startPolling()}}updateViews(e){this.updateStats(),this.listView.update(this.model&&this.model.entries()||[]),this.exportAction.setEnabled(null!==this.model&&this.model.entries().length>0),this.decorationManager&&this.decorationManager.update(e)}updateStats(){const e={total:0,unused:0},o={total:0,unused:0},s=null!==this.textFilterRegExp;if(this.model)for(const t of this.model.entries())if(e.total+=t.size(),e.unused+=t.unusedSize(),this.isVisible(!1,t))if(this.textFilterRegExp?.test(t.url()))o.total+=t.size(),o.unused+=t.unusedSize();else for(const e of t.sourcesURLCoverageInfo.values())this.isVisible(!1,e)&&(o.total+=e.size(),o.unused+=e.unusedSize());function r({total:e,unused:o}){const s=e-o,r=e?Math.round(100*s/e):0;return O(z.sOfSSUsedSoFarSUnused,{PH1:t.NumberUtilities.bytesToString(s),PH2:t.NumberUtilities.bytesToString(e),PH3:r,PH4:t.NumberUtilities.bytesToString(o)})}this.statusMessageElement.textContent=s?O(z.filteredSTotalS,{PH1:r(o),PH2:r(e)}):r(e)}onFilterChanged(){if(!this.listView)return;const e=this.filterInput.value();this.textFilterRegExp=e?t.StringUtilities.createPlainTextSearchRegex(e,"i"):null,this.listView.updateFilterAndHighlight(this.textFilterRegExp),this.updateStats()}onFilterByTypeChanged(){if(!this.listView)return;c.userMetrics.actionTaken(c.UserMetrics.Action.CoverageReportFiltered);const e=this.filterByTypeComboBox.selectedOption(),t=e&&e.value;this.typeFilterValue=parseInt(t||"",10)||null,this.listView.updateFilterAndHighlight(this.textFilterRegExp),this.updateStats()}isVisible(e,t){const o=t.url();if(o.startsWith(D.EXTENSION_BINDINGS_URL_PREFIX))return!1;if(t.isContentScript()&&!this.showContentScriptsSetting.get())return!1;if(this.typeFilterValue&&!(t.type()&this.typeFilterValue))return!1;if(t.sourcesURLCoverageInfo.size>0)for(const o of t.sourcesURLCoverageInfo.values())if(this.isVisible(e,o))return!0;return e||!this.textFilterRegExp||this.textFilterRegExp.test(o)}async exportReport(){const e=new l.FileUtils.FileOutputStream,o=`Coverage-${t.DateUtilities.toISO8601Compact(new Date)}.json`;await e.open(o)&&this.model&&await this.model.exportReport(e)}selectCoverageItemByUrl(e){this.listView.selectByUrl(e)}static EXTENSION_BINDINGS_URL_PREFIX="extensions::";wasShown(){a.Context.Context.instance().setFlavor(D,this),super.wasShown(),this.registerCSSFiles([E])}willHide(){super.willHide(),a.Context.Context.instance().setFlavor(D,null)}}var V=Object.freeze({__proto__:null,CoverageView:D,ActionDelegate:class{handleAction(e,t){const o="coverage";return a.ViewManager.ViewManager.instance().showView(o,!1,!0).then((()=>{const e=a.ViewManager.ViewManager.instance().view(o);return e&&e.widget()})).then((e=>this.innerHandleAction(e,t))),!0}innerHandleAction(e,t){switch(t){case"coverage.toggle-recording":e.toggleRecording();break;case"coverage.start-with-reload":e.startRecording({reload:!0,jsCoveragePerBlock:e.isBlockCoverageSelected()});break;case"coverage.clear":e.clear();break;case"coverage.export":e.exportReport();break;default:console.assert(!1,`Unknown action: ${t}`)}}}});export{F as CoverageDecorationManager,U as CoverageListView,S as CoverageModel,V as CoverageView};
