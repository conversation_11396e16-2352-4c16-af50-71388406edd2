# HadiHia - Moroccan Marketplace App

HadiHia is a Flutter-based marketplace app that connects Moroccan sellers with customers through live streaming and traditional e-commerce.

## Features

### Customer Interface
- Browse products in a grid view
- Watch live streams from sellers
- Chat with sellers
- Track orders
- Manage account settings

### Seller Interface
- Manage products
- Create and manage a store
- Host live streams to showcase products
- Process customer orders
- Track sales and analytics

## Getting Started

### Prerequisites
- Flutter SDK (latest version)
- Dart SDK (latest version)
- Android Studio or VS Code with Flutter extensions

### Installation

1. Clone the repository:
```
git clone https://github.com/yourusername/hadi_hia.git
```

2. Navigate to the project directory:
```
cd hadi_hia
```

3. Install dependencies:
```
flutter pub get
```

4. Run the app:
```
flutter run
```

## Demo Credentials

### Customer
- Email: <EMAIL>
- Password: password

### Seller
- Email: <EMAIL>
- Password: password

## Project Structure

- `lib/models/` - Data models
- `lib/pages/` - UI screens
  - `auth/` - Authentication screens
  - `customer/` - Customer interface screens
  - `seller/` - Seller interface screens
- `lib/services/` - Business logic and data services
- `lib/theme/` - App theme configuration
- `lib/widgets/` - Reusable UI components

## Note

This is a frontend-only implementation. Backend integration will be added in future versions.
