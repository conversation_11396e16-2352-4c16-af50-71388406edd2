const e=1836278016;var t,s;!function(e){e[e.Unknown=-1]="Unknown",e[e.Custom=0]="Custom",e[e.Type=1]="Type",e[e.Import=2]="Import",e[e.Function=3]="Function",e[e.Table=4]="Table",e[e.Memory=5]="Memory",e[e.Global=6]="Global",e[e.Export=7]="Export",e[e.Start=8]="Start",e[e.Element=9]="Element",e[e.Code=10]="Code",e[e.Data=11]="Data",e[e.Event=13]="Event"}(t||(t={})),function(e){e[e.unreachable=0]="unreachable",e[e.nop=1]="nop",e[e.block=2]="block",e[e.loop=3]="loop",e[e.if=4]="if",e[e.else=5]="else",e[e.try=6]="try",e[e.catch=7]="catch",e[e.throw=8]="throw",e[e.rethrow=9]="rethrow",e[e.unwind=10]="unwind",e[e.end=11]="end",e[e.br=12]="br",e[e.br_if=13]="br_if",e[e.br_table=14]="br_table",e[e.return=15]="return",e[e.call=16]="call",e[e.call_indirect=17]="call_indirect",e[e.return_call=18]="return_call",e[e.return_call_indirect=19]="return_call_indirect",e[e.call_ref=20]="call_ref",e[e.return_call_ref=21]="return_call_ref",e[e.let=23]="let",e[e.delegate=24]="delegate",e[e.catch_all=25]="catch_all",e[e.drop=26]="drop",e[e.select=27]="select",e[e.select_with_type=28]="select_with_type",e[e.local_get=32]="local_get",e[e.local_set=33]="local_set",e[e.local_tee=34]="local_tee",e[e.global_get=35]="global_get",e[e.global_set=36]="global_set",e[e.i32_load=40]="i32_load",e[e.i64_load=41]="i64_load",e[e.f32_load=42]="f32_load",e[e.f64_load=43]="f64_load",e[e.i32_load8_s=44]="i32_load8_s",e[e.i32_load8_u=45]="i32_load8_u",e[e.i32_load16_s=46]="i32_load16_s",e[e.i32_load16_u=47]="i32_load16_u",e[e.i64_load8_s=48]="i64_load8_s",e[e.i64_load8_u=49]="i64_load8_u",e[e.i64_load16_s=50]="i64_load16_s",e[e.i64_load16_u=51]="i64_load16_u",e[e.i64_load32_s=52]="i64_load32_s",e[e.i64_load32_u=53]="i64_load32_u",e[e.i32_store=54]="i32_store",e[e.i64_store=55]="i64_store",e[e.f32_store=56]="f32_store",e[e.f64_store=57]="f64_store",e[e.i32_store8=58]="i32_store8",e[e.i32_store16=59]="i32_store16",e[e.i64_store8=60]="i64_store8",e[e.i64_store16=61]="i64_store16",e[e.i64_store32=62]="i64_store32",e[e.current_memory=63]="current_memory",e[e.grow_memory=64]="grow_memory",e[e.i32_const=65]="i32_const",e[e.i64_const=66]="i64_const",e[e.f32_const=67]="f32_const",e[e.f64_const=68]="f64_const",e[e.i32_eqz=69]="i32_eqz",e[e.i32_eq=70]="i32_eq",e[e.i32_ne=71]="i32_ne",e[e.i32_lt_s=72]="i32_lt_s",e[e.i32_lt_u=73]="i32_lt_u",e[e.i32_gt_s=74]="i32_gt_s",e[e.i32_gt_u=75]="i32_gt_u",e[e.i32_le_s=76]="i32_le_s",e[e.i32_le_u=77]="i32_le_u",e[e.i32_ge_s=78]="i32_ge_s",e[e.i32_ge_u=79]="i32_ge_u",e[e.i64_eqz=80]="i64_eqz",e[e.i64_eq=81]="i64_eq",e[e.i64_ne=82]="i64_ne",e[e.i64_lt_s=83]="i64_lt_s",e[e.i64_lt_u=84]="i64_lt_u",e[e.i64_gt_s=85]="i64_gt_s",e[e.i64_gt_u=86]="i64_gt_u",e[e.i64_le_s=87]="i64_le_s",e[e.i64_le_u=88]="i64_le_u",e[e.i64_ge_s=89]="i64_ge_s",e[e.i64_ge_u=90]="i64_ge_u",e[e.f32_eq=91]="f32_eq",e[e.f32_ne=92]="f32_ne",e[e.f32_lt=93]="f32_lt",e[e.f32_gt=94]="f32_gt",e[e.f32_le=95]="f32_le",e[e.f32_ge=96]="f32_ge",e[e.f64_eq=97]="f64_eq",e[e.f64_ne=98]="f64_ne",e[e.f64_lt=99]="f64_lt",e[e.f64_gt=100]="f64_gt",e[e.f64_le=101]="f64_le",e[e.f64_ge=102]="f64_ge",e[e.i32_clz=103]="i32_clz",e[e.i32_ctz=104]="i32_ctz",e[e.i32_popcnt=105]="i32_popcnt",e[e.i32_add=106]="i32_add",e[e.i32_sub=107]="i32_sub",e[e.i32_mul=108]="i32_mul",e[e.i32_div_s=109]="i32_div_s",e[e.i32_div_u=110]="i32_div_u",e[e.i32_rem_s=111]="i32_rem_s",e[e.i32_rem_u=112]="i32_rem_u",e[e.i32_and=113]="i32_and",e[e.i32_or=114]="i32_or",e[e.i32_xor=115]="i32_xor",e[e.i32_shl=116]="i32_shl",e[e.i32_shr_s=117]="i32_shr_s",e[e.i32_shr_u=118]="i32_shr_u",e[e.i32_rotl=119]="i32_rotl",e[e.i32_rotr=120]="i32_rotr",e[e.i64_clz=121]="i64_clz",e[e.i64_ctz=122]="i64_ctz",e[e.i64_popcnt=123]="i64_popcnt",e[e.i64_add=124]="i64_add",e[e.i64_sub=125]="i64_sub",e[e.i64_mul=126]="i64_mul",e[e.i64_div_s=127]="i64_div_s",e[e.i64_div_u=128]="i64_div_u",e[e.i64_rem_s=129]="i64_rem_s",e[e.i64_rem_u=130]="i64_rem_u",e[e.i64_and=131]="i64_and",e[e.i64_or=132]="i64_or",e[e.i64_xor=133]="i64_xor",e[e.i64_shl=134]="i64_shl",e[e.i64_shr_s=135]="i64_shr_s",e[e.i64_shr_u=136]="i64_shr_u",e[e.i64_rotl=137]="i64_rotl",e[e.i64_rotr=138]="i64_rotr",e[e.f32_abs=139]="f32_abs",e[e.f32_neg=140]="f32_neg",e[e.f32_ceil=141]="f32_ceil",e[e.f32_floor=142]="f32_floor",e[e.f32_trunc=143]="f32_trunc",e[e.f32_nearest=144]="f32_nearest",e[e.f32_sqrt=145]="f32_sqrt",e[e.f32_add=146]="f32_add",e[e.f32_sub=147]="f32_sub",e[e.f32_mul=148]="f32_mul",e[e.f32_div=149]="f32_div",e[e.f32_min=150]="f32_min",e[e.f32_max=151]="f32_max",e[e.f32_copysign=152]="f32_copysign",e[e.f64_abs=153]="f64_abs",e[e.f64_neg=154]="f64_neg",e[e.f64_ceil=155]="f64_ceil",e[e.f64_floor=156]="f64_floor",e[e.f64_trunc=157]="f64_trunc",e[e.f64_nearest=158]="f64_nearest",e[e.f64_sqrt=159]="f64_sqrt",e[e.f64_add=160]="f64_add",e[e.f64_sub=161]="f64_sub",e[e.f64_mul=162]="f64_mul",e[e.f64_div=163]="f64_div",e[e.f64_min=164]="f64_min",e[e.f64_max=165]="f64_max",e[e.f64_copysign=166]="f64_copysign",e[e.i32_wrap_i64=167]="i32_wrap_i64",e[e.i32_trunc_f32_s=168]="i32_trunc_f32_s",e[e.i32_trunc_f32_u=169]="i32_trunc_f32_u",e[e.i32_trunc_f64_s=170]="i32_trunc_f64_s",e[e.i32_trunc_f64_u=171]="i32_trunc_f64_u",e[e.i64_extend_i32_s=172]="i64_extend_i32_s",e[e.i64_extend_i32_u=173]="i64_extend_i32_u",e[e.i64_trunc_f32_s=174]="i64_trunc_f32_s",e[e.i64_trunc_f32_u=175]="i64_trunc_f32_u",e[e.i64_trunc_f64_s=176]="i64_trunc_f64_s",e[e.i64_trunc_f64_u=177]="i64_trunc_f64_u",e[e.f32_convert_i32_s=178]="f32_convert_i32_s",e[e.f32_convert_i32_u=179]="f32_convert_i32_u",e[e.f32_convert_i64_s=180]="f32_convert_i64_s",e[e.f32_convert_i64_u=181]="f32_convert_i64_u",e[e.f32_demote_f64=182]="f32_demote_f64",e[e.f64_convert_i32_s=183]="f64_convert_i32_s",e[e.f64_convert_i32_u=184]="f64_convert_i32_u",e[e.f64_convert_i64_s=185]="f64_convert_i64_s",e[e.f64_convert_i64_u=186]="f64_convert_i64_u",e[e.f64_promote_f32=187]="f64_promote_f32",e[e.i32_reinterpret_f32=188]="i32_reinterpret_f32",e[e.i64_reinterpret_f64=189]="i64_reinterpret_f64",e[e.f32_reinterpret_i32=190]="f32_reinterpret_i32",e[e.f64_reinterpret_i64=191]="f64_reinterpret_i64",e[e.i32_extend8_s=192]="i32_extend8_s",e[e.i32_extend16_s=193]="i32_extend16_s",e[e.i64_extend8_s=194]="i64_extend8_s",e[e.i64_extend16_s=195]="i64_extend16_s",e[e.i64_extend32_s=196]="i64_extend32_s",e[e.prefix_0xfb=251]="prefix_0xfb",e[e.prefix_0xfc=252]="prefix_0xfc",e[e.prefix_0xfd=253]="prefix_0xfd",e[e.prefix_0xfe=254]="prefix_0xfe",e[e.i32_trunc_sat_f32_s=64512]="i32_trunc_sat_f32_s",e[e.i32_trunc_sat_f32_u=64513]="i32_trunc_sat_f32_u",e[e.i32_trunc_sat_f64_s=64514]="i32_trunc_sat_f64_s",e[e.i32_trunc_sat_f64_u=64515]="i32_trunc_sat_f64_u",e[e.i64_trunc_sat_f32_s=64516]="i64_trunc_sat_f32_s",e[e.i64_trunc_sat_f32_u=64517]="i64_trunc_sat_f32_u",e[e.i64_trunc_sat_f64_s=64518]="i64_trunc_sat_f64_s",e[e.i64_trunc_sat_f64_u=64519]="i64_trunc_sat_f64_u",e[e.memory_init=64520]="memory_init",e[e.data_drop=64521]="data_drop",e[e.memory_copy=64522]="memory_copy",e[e.memory_fill=64523]="memory_fill",e[e.table_init=64524]="table_init",e[e.elem_drop=64525]="elem_drop",e[e.table_copy=64526]="table_copy",e[e.table_grow=64527]="table_grow",e[e.table_size=64528]="table_size",e[e.table_fill=64529]="table_fill",e[e.table_get=37]="table_get",e[e.table_set=38]="table_set",e[e.ref_null=208]="ref_null",e[e.ref_is_null=209]="ref_is_null",e[e.ref_func=210]="ref_func",e[e.ref_as_non_null=211]="ref_as_non_null",e[e.br_on_null=212]="br_on_null",e[e.ref_eq=213]="ref_eq",e[e.br_on_non_null=214]="br_on_non_null",e[e.atomic_notify=65024]="atomic_notify",e[e.i32_atomic_wait=65025]="i32_atomic_wait",e[e.i64_atomic_wait=65026]="i64_atomic_wait",e[e.atomic_fence=65027]="atomic_fence",e[e.i32_atomic_load=65040]="i32_atomic_load",e[e.i64_atomic_load=65041]="i64_atomic_load",e[e.i32_atomic_load8_u=65042]="i32_atomic_load8_u",e[e.i32_atomic_load16_u=65043]="i32_atomic_load16_u",e[e.i64_atomic_load8_u=65044]="i64_atomic_load8_u",e[e.i64_atomic_load16_u=65045]="i64_atomic_load16_u",e[e.i64_atomic_load32_u=65046]="i64_atomic_load32_u",e[e.i32_atomic_store=65047]="i32_atomic_store",e[e.i64_atomic_store=65048]="i64_atomic_store",e[e.i32_atomic_store8=65049]="i32_atomic_store8",e[e.i32_atomic_store16=65050]="i32_atomic_store16",e[e.i64_atomic_store8=65051]="i64_atomic_store8",e[e.i64_atomic_store16=65052]="i64_atomic_store16",e[e.i64_atomic_store32=65053]="i64_atomic_store32",e[e.i32_atomic_rmw_add=65054]="i32_atomic_rmw_add",e[e.i64_atomic_rmw_add=65055]="i64_atomic_rmw_add",e[e.i32_atomic_rmw8_add_u=65056]="i32_atomic_rmw8_add_u",e[e.i32_atomic_rmw16_add_u=65057]="i32_atomic_rmw16_add_u",e[e.i64_atomic_rmw8_add_u=65058]="i64_atomic_rmw8_add_u",e[e.i64_atomic_rmw16_add_u=65059]="i64_atomic_rmw16_add_u",e[e.i64_atomic_rmw32_add_u=65060]="i64_atomic_rmw32_add_u",e[e.i32_atomic_rmw_sub=65061]="i32_atomic_rmw_sub",e[e.i64_atomic_rmw_sub=65062]="i64_atomic_rmw_sub",e[e.i32_atomic_rmw8_sub_u=65063]="i32_atomic_rmw8_sub_u",e[e.i32_atomic_rmw16_sub_u=65064]="i32_atomic_rmw16_sub_u",e[e.i64_atomic_rmw8_sub_u=65065]="i64_atomic_rmw8_sub_u",e[e.i64_atomic_rmw16_sub_u=65066]="i64_atomic_rmw16_sub_u",e[e.i64_atomic_rmw32_sub_u=65067]="i64_atomic_rmw32_sub_u",e[e.i32_atomic_rmw_and=65068]="i32_atomic_rmw_and",e[e.i64_atomic_rmw_and=65069]="i64_atomic_rmw_and",e[e.i32_atomic_rmw8_and_u=65070]="i32_atomic_rmw8_and_u",e[e.i32_atomic_rmw16_and_u=65071]="i32_atomic_rmw16_and_u",e[e.i64_atomic_rmw8_and_u=65072]="i64_atomic_rmw8_and_u",e[e.i64_atomic_rmw16_and_u=65073]="i64_atomic_rmw16_and_u",e[e.i64_atomic_rmw32_and_u=65074]="i64_atomic_rmw32_and_u",e[e.i32_atomic_rmw_or=65075]="i32_atomic_rmw_or",e[e.i64_atomic_rmw_or=65076]="i64_atomic_rmw_or",e[e.i32_atomic_rmw8_or_u=65077]="i32_atomic_rmw8_or_u",e[e.i32_atomic_rmw16_or_u=65078]="i32_atomic_rmw16_or_u",e[e.i64_atomic_rmw8_or_u=65079]="i64_atomic_rmw8_or_u",e[e.i64_atomic_rmw16_or_u=65080]="i64_atomic_rmw16_or_u",e[e.i64_atomic_rmw32_or_u=65081]="i64_atomic_rmw32_or_u",e[e.i32_atomic_rmw_xor=65082]="i32_atomic_rmw_xor",e[e.i64_atomic_rmw_xor=65083]="i64_atomic_rmw_xor",e[e.i32_atomic_rmw8_xor_u=65084]="i32_atomic_rmw8_xor_u",e[e.i32_atomic_rmw16_xor_u=65085]="i32_atomic_rmw16_xor_u",e[e.i64_atomic_rmw8_xor_u=65086]="i64_atomic_rmw8_xor_u",e[e.i64_atomic_rmw16_xor_u=65087]="i64_atomic_rmw16_xor_u",e[e.i64_atomic_rmw32_xor_u=65088]="i64_atomic_rmw32_xor_u",e[e.i32_atomic_rmw_xchg=65089]="i32_atomic_rmw_xchg",e[e.i64_atomic_rmw_xchg=65090]="i64_atomic_rmw_xchg",e[e.i32_atomic_rmw8_xchg_u=65091]="i32_atomic_rmw8_xchg_u",e[e.i32_atomic_rmw16_xchg_u=65092]="i32_atomic_rmw16_xchg_u",e[e.i64_atomic_rmw8_xchg_u=65093]="i64_atomic_rmw8_xchg_u",e[e.i64_atomic_rmw16_xchg_u=65094]="i64_atomic_rmw16_xchg_u",e[e.i64_atomic_rmw32_xchg_u=65095]="i64_atomic_rmw32_xchg_u",e[e.i32_atomic_rmw_cmpxchg=65096]="i32_atomic_rmw_cmpxchg",e[e.i64_atomic_rmw_cmpxchg=65097]="i64_atomic_rmw_cmpxchg",e[e.i32_atomic_rmw8_cmpxchg_u=65098]="i32_atomic_rmw8_cmpxchg_u",e[e.i32_atomic_rmw16_cmpxchg_u=65099]="i32_atomic_rmw16_cmpxchg_u",e[e.i64_atomic_rmw8_cmpxchg_u=65100]="i64_atomic_rmw8_cmpxchg_u",e[e.i64_atomic_rmw16_cmpxchg_u=65101]="i64_atomic_rmw16_cmpxchg_u",e[e.i64_atomic_rmw32_cmpxchg_u=65102]="i64_atomic_rmw32_cmpxchg_u",e[e.v128_load=64768]="v128_load",e[e.i16x8_load8x8_s=64769]="i16x8_load8x8_s",e[e.i16x8_load8x8_u=64770]="i16x8_load8x8_u",e[e.i32x4_load16x4_s=64771]="i32x4_load16x4_s",e[e.i32x4_load16x4_u=64772]="i32x4_load16x4_u",e[e.i64x2_load32x2_s=64773]="i64x2_load32x2_s",e[e.i64x2_load32x2_u=64774]="i64x2_load32x2_u",e[e.v8x16_load_splat=64775]="v8x16_load_splat",e[e.v16x8_load_splat=64776]="v16x8_load_splat",e[e.v32x4_load_splat=64777]="v32x4_load_splat",e[e.v64x2_load_splat=64778]="v64x2_load_splat",e[e.v128_store=64779]="v128_store",e[e.v128_const=64780]="v128_const",e[e.i8x16_shuffle=64781]="i8x16_shuffle",e[e.i8x16_swizzle=64782]="i8x16_swizzle",e[e.i8x16_splat=64783]="i8x16_splat",e[e.i16x8_splat=64784]="i16x8_splat",e[e.i32x4_splat=64785]="i32x4_splat",e[e.i64x2_splat=64786]="i64x2_splat",e[e.f32x4_splat=64787]="f32x4_splat",e[e.f64x2_splat=64788]="f64x2_splat",e[e.i8x16_extract_lane_s=64789]="i8x16_extract_lane_s",e[e.i8x16_extract_lane_u=64790]="i8x16_extract_lane_u",e[e.i8x16_replace_lane=64791]="i8x16_replace_lane",e[e.i16x8_extract_lane_s=64792]="i16x8_extract_lane_s",e[e.i16x8_extract_lane_u=64793]="i16x8_extract_lane_u",e[e.i16x8_replace_lane=64794]="i16x8_replace_lane",e[e.i32x4_extract_lane=64795]="i32x4_extract_lane",e[e.i32x4_replace_lane=64796]="i32x4_replace_lane",e[e.i64x2_extract_lane=64797]="i64x2_extract_lane",e[e.i64x2_replace_lane=64798]="i64x2_replace_lane",e[e.f32x4_extract_lane=64799]="f32x4_extract_lane",e[e.f32x4_replace_lane=64800]="f32x4_replace_lane",e[e.f64x2_extract_lane=64801]="f64x2_extract_lane",e[e.f64x2_replace_lane=64802]="f64x2_replace_lane",e[e.i8x16_eq=64803]="i8x16_eq",e[e.i8x16_ne=64804]="i8x16_ne",e[e.i8x16_lt_s=64805]="i8x16_lt_s",e[e.i8x16_lt_u=64806]="i8x16_lt_u",e[e.i8x16_gt_s=64807]="i8x16_gt_s",e[e.i8x16_gt_u=64808]="i8x16_gt_u",e[e.i8x16_le_s=64809]="i8x16_le_s",e[e.i8x16_le_u=64810]="i8x16_le_u",e[e.i8x16_ge_s=64811]="i8x16_ge_s",e[e.i8x16_ge_u=64812]="i8x16_ge_u",e[e.i16x8_eq=64813]="i16x8_eq",e[e.i16x8_ne=64814]="i16x8_ne",e[e.i16x8_lt_s=64815]="i16x8_lt_s",e[e.i16x8_lt_u=64816]="i16x8_lt_u",e[e.i16x8_gt_s=64817]="i16x8_gt_s",e[e.i16x8_gt_u=64818]="i16x8_gt_u",e[e.i16x8_le_s=64819]="i16x8_le_s",e[e.i16x8_le_u=64820]="i16x8_le_u",e[e.i16x8_ge_s=64821]="i16x8_ge_s",e[e.i16x8_ge_u=64822]="i16x8_ge_u",e[e.i32x4_eq=64823]="i32x4_eq",e[e.i32x4_ne=64824]="i32x4_ne",e[e.i32x4_lt_s=64825]="i32x4_lt_s",e[e.i32x4_lt_u=64826]="i32x4_lt_u",e[e.i32x4_gt_s=64827]="i32x4_gt_s",e[e.i32x4_gt_u=64828]="i32x4_gt_u",e[e.i32x4_le_s=64829]="i32x4_le_s",e[e.i32x4_le_u=64830]="i32x4_le_u",e[e.i32x4_ge_s=64831]="i32x4_ge_s",e[e.i32x4_ge_u=64832]="i32x4_ge_u",e[e.f32x4_eq=64833]="f32x4_eq",e[e.f32x4_ne=64834]="f32x4_ne",e[e.f32x4_lt=64835]="f32x4_lt",e[e.f32x4_gt=64836]="f32x4_gt",e[e.f32x4_le=64837]="f32x4_le",e[e.f32x4_ge=64838]="f32x4_ge",e[e.f64x2_eq=64839]="f64x2_eq",e[e.f64x2_ne=64840]="f64x2_ne",e[e.f64x2_lt=64841]="f64x2_lt",e[e.f64x2_gt=64842]="f64x2_gt",e[e.f64x2_le=64843]="f64x2_le",e[e.f64x2_ge=64844]="f64x2_ge",e[e.v128_not=64845]="v128_not",e[e.v128_and=64846]="v128_and",e[e.v128_andnot=64847]="v128_andnot",e[e.v128_or=64848]="v128_or",e[e.v128_xor=64849]="v128_xor",e[e.v128_bitselect=64850]="v128_bitselect",e[e.v128_any_true=64851]="v128_any_true",e[e.v128_load8_lane=64852]="v128_load8_lane",e[e.v128_load16_lane=64853]="v128_load16_lane",e[e.v128_load32_lane=64854]="v128_load32_lane",e[e.v128_load64_lane=64855]="v128_load64_lane",e[e.v128_store8_lane=64856]="v128_store8_lane",e[e.v128_store16_lane=64857]="v128_store16_lane",e[e.v128_store32_lane=64858]="v128_store32_lane",e[e.v128_store64_lane=64859]="v128_store64_lane",e[e.v128_load32_zero=64860]="v128_load32_zero",e[e.v128_load64_zero=64861]="v128_load64_zero",e[e.f32x4_demote_f64x2_zero=64862]="f32x4_demote_f64x2_zero",e[e.f64x2_promote_low_f32x4=64863]="f64x2_promote_low_f32x4",e[e.i8x16_abs=64864]="i8x16_abs",e[e.i8x16_neg=64865]="i8x16_neg",e[e.i8x16_popcnt=64866]="i8x16_popcnt",e[e.i8x16_all_true=64867]="i8x16_all_true",e[e.i8x16_bitmask=64868]="i8x16_bitmask",e[e.i8x16_narrow_i16x8_s=64869]="i8x16_narrow_i16x8_s",e[e.i8x16_narrow_i16x8_u=64870]="i8x16_narrow_i16x8_u",e[e.f32x4_ceil=64871]="f32x4_ceil",e[e.f32x4_floor=64872]="f32x4_floor",e[e.f32x4_trunc=64873]="f32x4_trunc",e[e.f32x4_nearest=64874]="f32x4_nearest",e[e.i8x16_shl=64875]="i8x16_shl",e[e.i8x16_shr_s=64876]="i8x16_shr_s",e[e.i8x16_shr_u=64877]="i8x16_shr_u",e[e.i8x16_add=64878]="i8x16_add",e[e.i8x16_add_sat_s=64879]="i8x16_add_sat_s",e[e.i8x16_add_sat_u=64880]="i8x16_add_sat_u",e[e.i8x16_sub=64881]="i8x16_sub",e[e.i8x16_sub_sat_s=64882]="i8x16_sub_sat_s",e[e.i8x16_sub_sat_u=64883]="i8x16_sub_sat_u",e[e.f64x2_ceil=64884]="f64x2_ceil",e[e.f64x2_floor=64885]="f64x2_floor",e[e.i8x16_min_s=64886]="i8x16_min_s",e[e.i8x16_min_u=64887]="i8x16_min_u",e[e.i8x16_max_s=64888]="i8x16_max_s",e[e.i8x16_max_u=64889]="i8x16_max_u",e[e.f64x2_trunc=64890]="f64x2_trunc",e[e.i8x16_avgr_u=64891]="i8x16_avgr_u",e[e.i16x8_extadd_pairwise_i8x16_s=64892]="i16x8_extadd_pairwise_i8x16_s",e[e.i16x8_extadd_pairwise_i8x16_u=64893]="i16x8_extadd_pairwise_i8x16_u",e[e.i32x4_extadd_pairwise_i16x8_s=64894]="i32x4_extadd_pairwise_i16x8_s",e[e.i32x4_extadd_pairwise_i16x8_u=64895]="i32x4_extadd_pairwise_i16x8_u",e[e.i16x8_abs=64896]="i16x8_abs",e[e.i16x8_neg=64897]="i16x8_neg",e[e.i16x8_q15mulr_sat_s=64898]="i16x8_q15mulr_sat_s",e[e.i16x8_all_true=64899]="i16x8_all_true",e[e.i16x8_bitmask=64900]="i16x8_bitmask",e[e.i16x8_narrow_i32x4_s=64901]="i16x8_narrow_i32x4_s",e[e.i16x8_narrow_i32x4_u=64902]="i16x8_narrow_i32x4_u",e[e.i16x8_extend_low_i8x16_s=64903]="i16x8_extend_low_i8x16_s",e[e.i16x8_extend_high_i8x16_s=64904]="i16x8_extend_high_i8x16_s",e[e.i16x8_extend_low_i8x16_u=64905]="i16x8_extend_low_i8x16_u",e[e.i16x8_extend_high_i8x16_u=64906]="i16x8_extend_high_i8x16_u",e[e.i16x8_shl=64907]="i16x8_shl",e[e.i16x8_shr_s=64908]="i16x8_shr_s",e[e.i16x8_shr_u=64909]="i16x8_shr_u",e[e.i16x8_add=64910]="i16x8_add",e[e.i16x8_add_sat_s=64911]="i16x8_add_sat_s",e[e.i16x8_add_sat_u=64912]="i16x8_add_sat_u",e[e.i16x8_sub=64913]="i16x8_sub",e[e.i16x8_sub_sat_s=64914]="i16x8_sub_sat_s",e[e.i16x8_sub_sat_u=64915]="i16x8_sub_sat_u",e[e.f64x2_nearest=64916]="f64x2_nearest",e[e.i16x8_mul=64917]="i16x8_mul",e[e.i16x8_min_s=64918]="i16x8_min_s",e[e.i16x8_min_u=64919]="i16x8_min_u",e[e.i16x8_max_s=64920]="i16x8_max_s",e[e.i16x8_max_u=64921]="i16x8_max_u",e[e.i16x8_avgr_u=64923]="i16x8_avgr_u",e[e.i16x8_extmul_low_i8x16_s=64924]="i16x8_extmul_low_i8x16_s",e[e.i16x8_extmul_high_i8x16_s=64925]="i16x8_extmul_high_i8x16_s",e[e.i16x8_extmul_low_i8x16_u=64926]="i16x8_extmul_low_i8x16_u",e[e.i16x8_extmul_high_i8x16_u=64927]="i16x8_extmul_high_i8x16_u",e[e.i32x4_abs=64928]="i32x4_abs",e[e.i32x4_neg=64929]="i32x4_neg",e[e.i32x4_all_true=64931]="i32x4_all_true",e[e.i32x4_bitmask=64932]="i32x4_bitmask",e[e.i32x4_extend_low_i16x8_s=64935]="i32x4_extend_low_i16x8_s",e[e.i32x4_extend_high_i16x8_s=64936]="i32x4_extend_high_i16x8_s",e[e.i32x4_extend_low_i16x8_u=64937]="i32x4_extend_low_i16x8_u",e[e.i32x4_extend_high_i16x8_u=64938]="i32x4_extend_high_i16x8_u",e[e.i32x4_shl=64939]="i32x4_shl",e[e.i32x4_shr_s=64940]="i32x4_shr_s",e[e.i32x4_shr_u=64941]="i32x4_shr_u",e[e.i32x4_add=64942]="i32x4_add",e[e.i32x4_sub=64945]="i32x4_sub",e[e.i32x4_mul=64949]="i32x4_mul",e[e.i32x4_min_s=64950]="i32x4_min_s",e[e.i32x4_min_u=64951]="i32x4_min_u",e[e.i32x4_max_s=64952]="i32x4_max_s",e[e.i32x4_max_u=64953]="i32x4_max_u",e[e.i32x4_dot_i16x8_s=64954]="i32x4_dot_i16x8_s",e[e.i32x4_extmul_low_i16x8_s=64956]="i32x4_extmul_low_i16x8_s",e[e.i32x4_extmul_high_i16x8_s=64957]="i32x4_extmul_high_i16x8_s",e[e.i32x4_extmul_low_i16x8_u=64958]="i32x4_extmul_low_i16x8_u",e[e.i32x4_extmul_high_i16x8_u=64959]="i32x4_extmul_high_i16x8_u",e[e.i64x2_abs=64960]="i64x2_abs",e[e.i64x2_neg=64961]="i64x2_neg",e[e.i64x2_all_true=64963]="i64x2_all_true",e[e.i64x2_bitmask=64964]="i64x2_bitmask",e[e.i64x2_extend_low_i32x4_s=64967]="i64x2_extend_low_i32x4_s",e[e.i64x2_extend_high_i32x4_s=64968]="i64x2_extend_high_i32x4_s",e[e.i64x2_extend_low_i32x4_u=64969]="i64x2_extend_low_i32x4_u",e[e.i64x2_extend_high_i32x4_u=64970]="i64x2_extend_high_i32x4_u",e[e.i64x2_shl=64971]="i64x2_shl",e[e.i64x2_shr_s=64972]="i64x2_shr_s",e[e.i64x2_shr_u=64973]="i64x2_shr_u",e[e.i64x2_add=64974]="i64x2_add",e[e.i64x2_sub=64977]="i64x2_sub",e[e.i64x2_mul=64981]="i64x2_mul",e[e.i64x2_eq=64982]="i64x2_eq",e[e.i64x2_ne=64983]="i64x2_ne",e[e.i64x2_lt_s=64984]="i64x2_lt_s",e[e.i64x2_gt_s=64985]="i64x2_gt_s",e[e.i64x2_le_s=64986]="i64x2_le_s",e[e.i64x2_ge_s=64987]="i64x2_ge_s",e[e.i64x2_extmul_low_i32x4_s=64988]="i64x2_extmul_low_i32x4_s",e[e.i64x2_extmul_high_i32x4_s=64989]="i64x2_extmul_high_i32x4_s",e[e.i64x2_extmul_low_i32x4_u=64990]="i64x2_extmul_low_i32x4_u",e[e.i64x2_extmul_high_i32x4_u=64991]="i64x2_extmul_high_i32x4_u",e[e.f32x4_abs=64992]="f32x4_abs",e[e.f32x4_neg=64993]="f32x4_neg",e[e.f32x4_sqrt=64995]="f32x4_sqrt",e[e.f32x4_add=64996]="f32x4_add",e[e.f32x4_sub=64997]="f32x4_sub",e[e.f32x4_mul=64998]="f32x4_mul",e[e.f32x4_div=64999]="f32x4_div",e[e.f32x4_min=65e3]="f32x4_min",e[e.f32x4_max=65001]="f32x4_max",e[e.f32x4_pmin=65002]="f32x4_pmin",e[e.f32x4_pmax=65003]="f32x4_pmax",e[e.f64x2_abs=65004]="f64x2_abs",e[e.f64x2_neg=65005]="f64x2_neg",e[e.f64x2_sqrt=65007]="f64x2_sqrt",e[e.f64x2_add=65008]="f64x2_add",e[e.f64x2_sub=65009]="f64x2_sub",e[e.f64x2_mul=65010]="f64x2_mul",e[e.f64x2_div=65011]="f64x2_div",e[e.f64x2_min=65012]="f64x2_min",e[e.f64x2_max=65013]="f64x2_max",e[e.f64x2_pmin=65014]="f64x2_pmin",e[e.f64x2_pmax=65015]="f64x2_pmax",e[e.i32x4_trunc_sat_f32x4_s=65016]="i32x4_trunc_sat_f32x4_s",e[e.i32x4_trunc_sat_f32x4_u=65017]="i32x4_trunc_sat_f32x4_u",e[e.f32x4_convert_i32x4_s=65018]="f32x4_convert_i32x4_s",e[e.f32x4_convert_i32x4_u=65019]="f32x4_convert_i32x4_u",e[e.i32x4_trunc_sat_f64x2_s_zero=65020]="i32x4_trunc_sat_f64x2_s_zero",e[e.i32x4_trunc_sat_f64x2_u_zero=65021]="i32x4_trunc_sat_f64x2_u_zero",e[e.f64x2_convert_low_i32x4_s=65022]="f64x2_convert_low_i32x4_s",e[e.f64x2_convert_low_i32x4_u=65023]="f64x2_convert_low_i32x4_u",e[e.struct_new_with_rtt=64257]="struct_new_with_rtt",e[e.struct_new_default_with_rtt=64258]="struct_new_default_with_rtt",e[e.struct_get=64259]="struct_get",e[e.struct_get_s=64260]="struct_get_s",e[e.struct_get_u=64261]="struct_get_u",e[e.struct_set=64262]="struct_set",e[e.struct_new=64263]="struct_new",e[e.struct_new_default=64264]="struct_new_default",e[e.array_new_with_rtt=64273]="array_new_with_rtt",e[e.array_new_default_with_rtt=64274]="array_new_default_with_rtt",e[e.array_get=64275]="array_get",e[e.array_get_s=64276]="array_get_s",e[e.array_get_u=64277]="array_get_u",e[e.array_set=64278]="array_set",e[e.array_len=64279]="array_len",e[e.array_copy=64280]="array_copy",e[e.array_init=64281]="array_init",e[e.array_init_static=64282]="array_init_static",e[e.array_new=64283]="array_new",e[e.array_new_default=64284]="array_new_default",e[e.i31_new=64288]="i31_new",e[e.i31_get_s=64289]="i31_get_s",e[e.i31_get_u=64290]="i31_get_u",e[e.rtt_canon=64304]="rtt_canon",e[e.rtt_sub=64305]="rtt_sub",e[e.rtt_fresh_sub=64306]="rtt_fresh_sub",e[e.ref_test=64320]="ref_test",e[e.ref_test_static=64324]="ref_test_static",e[e.ref_cast=64321]="ref_cast",e[e.ref_cast_static=64325]="ref_cast_static",e[e.br_on_cast=64322]="br_on_cast",e[e.br_on_cast_static=64326]="br_on_cast_static",e[e.br_on_cast_fail=64323]="br_on_cast_fail",e[e.br_on_cast_static_fail=64327]="br_on_cast_static_fail",e[e.ref_is_func=64336]="ref_is_func",e[e.ref_is_data=64337]="ref_is_data",e[e.ref_is_i31=64338]="ref_is_i31",e[e.ref_as_func=64344]="ref_as_func",e[e.ref_as_data=64345]="ref_as_data",e[e.ref_as_i31=64346]="ref_as_i31",e[e.br_on_func=64352]="br_on_func",e[e.br_on_data=64353]="br_on_data",e[e.br_on_i31=64354]="br_on_i31",e[e.br_on_non_func=64355]="br_on_non_func",e[e.br_on_non_data=64356]="br_on_non_data",e[e.br_on_non_i31=64357]="br_on_non_i31"}(s||(s={}));const a=["unreachable","nop","block","loop","if","else","try","catch","throw","rethrow","unwind","end","br","br_if","br_table","return","call","call_indirect","return_call","return_call_indirect","call_ref","return_call_ref",void 0,"let","delegate","catch_all","drop","select","select",void 0,void 0,void 0,"local.get","local.set","local.tee","global.get","global.set","table.get","table.set",void 0,"i32.load","i64.load","f32.load","f64.load","i32.load8_s","i32.load8_u","i32.load16_s","i32.load16_u","i64.load8_s","i64.load8_u","i64.load16_s","i64.load16_u","i64.load32_s","i64.load32_u","i32.store","i64.store","f32.store","f64.store","i32.store8","i32.store16","i64.store8","i64.store16","i64.store32","current_memory","memory.grow","i32.const","i64.const","f32.const","f64.const","i32.eqz","i32.eq","i32.ne","i32.lt_s","i32.lt_u","i32.gt_s","i32.gt_u","i32.le_s","i32.le_u","i32.ge_s","i32.ge_u","i64.eqz","i64.eq","i64.ne","i64.lt_s","i64.lt_u","i64.gt_s","i64.gt_u","i64.le_s","i64.le_u","i64.ge_s","i64.ge_u","f32.eq","f32.ne","f32.lt","f32.gt","f32.le","f32.ge","f64.eq","f64.ne","f64.lt","f64.gt","f64.le","f64.ge","i32.clz","i32.ctz","i32.popcnt","i32.add","i32.sub","i32.mul","i32.div_s","i32.div_u","i32.rem_s","i32.rem_u","i32.and","i32.or","i32.xor","i32.shl","i32.shr_s","i32.shr_u","i32.rotl","i32.rotr","i64.clz","i64.ctz","i64.popcnt","i64.add","i64.sub","i64.mul","i64.div_s","i64.div_u","i64.rem_s","i64.rem_u","i64.and","i64.or","i64.xor","i64.shl","i64.shr_s","i64.shr_u","i64.rotl","i64.rotr","f32.abs","f32.neg","f32.ceil","f32.floor","f32.trunc","f32.nearest","f32.sqrt","f32.add","f32.sub","f32.mul","f32.div","f32.min","f32.max","f32.copysign","f64.abs","f64.neg","f64.ceil","f64.floor","f64.trunc","f64.nearest","f64.sqrt","f64.add","f64.sub","f64.mul","f64.div","f64.min","f64.max","f64.copysign","i32.wrap_i64","i32.trunc_f32_s","i32.trunc_f32_u","i32.trunc_f64_s","i32.trunc_f64_u","i64.extend_i32_s","i64.extend_i32_u","i64.trunc_f32_s","i64.trunc_f32_u","i64.trunc_f64_s","i64.trunc_f64_u","f32.convert_i32_s","f32.convert_i32_u","f32.convert_i64_s","f32.convert_i64_u","f32.demote_f64","f64.convert_i32_s","f64.convert_i32_u","f64.convert_i64_s","f64.convert_i64_u","f64.promote_f32","i32.reinterpret_f32","i64.reinterpret_f64","f32.reinterpret_i32","f64.reinterpret_i64","i32.extend8_s","i32.extend16_s","i64.extend8_s","i64.extend16_s","i64.extend32_s",void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,"ref.null","ref.is_null","ref.func","ref.as_non_null","br_on_null","ref.eq","br_on_non_null",void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0];var i,r,_,n,c,o,h,u,d,l,f,x;["i32.trunc_sat_f32_s","i32.trunc_sat_f32_u","i32.trunc_sat_f64_s","i32.trunc_sat_f64_u","i64.trunc_sat_f32_s","i64.trunc_sat_f32_u","i64.trunc_sat_f64_s","i64.trunc_sat_f64_u","memory.init","data.drop","memory.copy","memory.fill","table.init","elem.drop","table.copy","table.grow","table.size","table.fill"].forEach(((e,t)=>{a[64512|t]=e})),["v128.load","i16x8.load8x8_s","i16x8.load8x8_u","i32x4.load16x4_s","i32x4.load16x4_u","i64x2.load32x2_s","i64x2.load32x2_u","v8x16.load_splat","v16x8.load_splat","v32x4.load_splat","v64x2.load_splat","v128.store","v128.const","i8x16.shuffle","i8x16.swizzle","i8x16.splat","i16x8.splat","i32x4.splat","i64x2.splat","f32x4.splat","f64x2.splat","i8x16.extract_lane_s","i8x16.extract_lane_u","i8x16.replace_lane","i16x8.extract_lane_s","i16x8.extract_lane_u","i16x8.replace_lane","i32x4.extract_lane","i32x4.replace_lane","i64x2.extract_lane","i64x2.replace_lane","f32x4.extract_lane","f32x4.replace_lane","f64x2.extract_lane","f64x2.replace_lane","i8x16.eq","i8x16.ne","i8x16.lt_s","i8x16.lt_u","i8x16.gt_s","i8x16.gt_u","i8x16.le_s","i8x16.le_u","i8x16.ge_s","i8x16.ge_u","i16x8.eq","i16x8.ne","i16x8.lt_s","i16x8.lt_u","i16x8.gt_s","i16x8.gt_u","i16x8.le_s","i16x8.le_u","i16x8.ge_s","i16x8.ge_u","i32x4.eq","i32x4.ne","i32x4.lt_s","i32x4.lt_u","i32x4.gt_s","i32x4.gt_u","i32x4.le_s","i32x4.le_u","i32x4.ge_s","i32x4.ge_u","f32x4.eq","f32x4.ne","f32x4.lt","f32x4.gt","f32x4.le","f32x4.ge","f64x2.eq","f64x2.ne","f64x2.lt","f64x2.gt","f64x2.le","f64x2.ge","v128.not","v128.and","v128.andnot","v128.or","v128.xor","v128.bitselect","v128.any_true","v128.load8_lane","v128.load16_lane","v128.load32_lane","v128.load64_lane","v128.store8_lane","v128.store16_lane","v128.store32_lane","v128.store64_lane","v128.load32_zero","v128.load64_zero","f32x4.demote_f64x2_zero","f64x2.promote_low_f32x4","i8x16.abs","i8x16.neg","i8x16_popcnt","i8x16.all_true","i8x16.bitmask","i8x16.narrow_i16x8_s","i8x16.narrow_i16x8_u","f32x4.ceil","f32x4.floor","f32x4.trunc","f32x4.nearest","i8x16.shl","i8x16.shr_s","i8x16.shr_u","i8x16.add","i8x16.add_sat_s","i8x16.add_sat_u","i8x16.sub","i8x16.sub_sat_s","i8x16.sub_sat_u","f64x2.ceil","f64x2.floor","i8x16.min_s","i8x16.min_u","i8x16.max_s","i8x16.max_u","f64x2.trunc","i8x16.avgr_u","i16x8.extadd_pairwise_i8x16_s","i16x8.extadd_pairwise_i8x16_u","i32x4.extadd_pairwise_i16x8_s","i32x4.extadd_pairwise_i16x8_u","i16x8.abs","i16x8.neg","i16x8.q15mulr_sat_s","i16x8.all_true","i16x8.bitmask","i16x8.narrow_i32x4_s","i16x8.narrow_i32x4_u","i16x8.extend_low_i8x16_s","i16x8.extend_high_i8x16_s","i16x8.extend_low_i8x16_u","i16x8.extend_high_i8x16_u","i16x8.shl","i16x8.shr_s","i16x8.shr_u","i16x8.add","i16x8.add_sat_s","i16x8.add_sat_u","i16x8.sub","i16x8.sub_sat_s","i16x8.sub_sat_u","f64x2.nearest","i16x8.mul","i16x8.min_s","i16x8.min_u","i16x8.max_s","i16x8.max_u",void 0,"i16x8.avgr_u","i16x8.extmul_low_i8x16_s","i16x8.extmul_high_i8x16_s","i16x8.extmul_low_i8x16_u","i16x8.extmul_high_i8x16_u","i32x4.abs","i32x4.neg",void 0,"i32x4.all_true","i32x4.bitmask",void 0,void 0,"i32x4.extend_low_i16x8_s","i32x4.extend_high_i16x8_s","i32x4.extend_low_i16x8_u","i32x4.extend_high_i16x8_u","i32x4.shl","i32x4.shr_s","i32x4.shr_u","i32x4.add",void 0,void 0,"i32x4.sub",void 0,void 0,void 0,"i32x4.mul","i32x4.min_s","i32x4.min_u","i32x4.max_s","i32x4.max_u","i32x4.dot_i16x8_s",void 0,"i32x4.extmul_low_i16x8_s","i32x4.extmul_high_i16x8_s","i32x4.extmul_low_i16x8_u","i32x4.extmul_high_i16x8_u","i64x2.abs","i64x2.neg",void 0,"i64x2.all_true","i64x2.bitmask",void 0,void 0,"i64x2.extend_low_i32x4_s","i64x2.extend_high_i32x4_s","i64x2.extend_low_i32x4_u","i64x2.extend_high_i32x4_u","i64x2.shl","i64x2.shr_s","i64x2.shr_u","i64x2.add",void 0,void 0,"i64x2.sub",void 0,void 0,void 0,"i64x2.mul","i64x2.eq","i64x2.ne","i64x2.lt_s","i64x2.gt_s","i64x2.le_s","i64x2.ge_s","i64x2.extmul_low_i32x4_s","i64x2.extmul_high_i32x4_s","i64x2.extmul_low_i32x4_u","i64x2.extmul_high_i32x4_u","f32x4.abs","f32x4.neg",void 0,"f32x4.sqrt","f32x4.add","f32x4.sub","f32x4.mul","f32x4.div","f32x4.min","f32x4.max","f32x4.pmin","f32x4.pmax","f64x2.abs","f64x2.neg",void 0,"f64x2.sqrt","f64x2.add","f64x2.sub","f64x2.mul","f64x2.div","f64x2.min","f64x2.max","f64x2.pmin","f64x2.pmax","i32x4.trunc_sat_f32x4_s","i32x4.trunc_sat_f32x4_u","f32x4.convert_i32x4_s","f32x4.convert_i32x4_u","i32x4.trunc_sat_f64x2_s_zero","i32x4.trunc_sat_f64x2_u_zero","f64x2.convert_low_i32x4_s","f64x2.convert_low_i32x4_u"].forEach(((e,t)=>{a[64768|t]=e})),["atomic.notify","i32.atomic.wait","i64.atomic.wait","atomic.fence",void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,"i32.atomic.load","i64.atomic.load","i32.atomic.load8_u","i32.atomic.load16_u","i64.atomic.load8_u","i64.atomic.load16_u","i64.atomic.load32_u","i32.atomic.store","i64.atomic.store","i32.atomic.store8","i32.atomic.store16","i64.atomic.store8","i64.atomic.store16","i64.atomic.store32","i32.atomic.rmw.add","i64.atomic.rmw.add","i32.atomic.rmw8.add_u","i32.atomic.rmw16.add_u","i64.atomic.rmw8.add_u","i64.atomic.rmw16.add_u","i64.atomic.rmw32.add_u","i32.atomic.rmw.sub","i64.atomic.rmw.sub","i32.atomic.rmw8.sub_u","i32.atomic.rmw16.sub_u","i64.atomic.rmw8.sub_u","i64.atomic.rmw16.sub_u","i64.atomic.rmw32.sub_u","i32.atomic.rmw.and","i64.atomic.rmw.and","i32.atomic.rmw8.and_u","i32.atomic.rmw16.and_u","i64.atomic.rmw8.and_u","i64.atomic.rmw16.and_u","i64.atomic.rmw32.and_u","i32.atomic.rmw.or","i64.atomic.rmw.or","i32.atomic.rmw8.or_u","i32.atomic.rmw16.or_u","i64.atomic.rmw8.or_u","i64.atomic.rmw16.or_u","i64.atomic.rmw32.or_u","i32.atomic.rmw.xor","i64.atomic.rmw.xor","i32.atomic.rmw8.xor_u","i32.atomic.rmw16.xor_u","i64.atomic.rmw8.xor_u","i64.atomic.rmw16.xor_u","i64.atomic.rmw32.xor_u","i32.atomic.rmw.xchg","i64.atomic.rmw.xchg","i32.atomic.rmw8.xchg_u","i32.atomic.rmw16.xchg_u","i64.atomic.rmw8.xchg_u","i64.atomic.rmw16.xchg_u","i64.atomic.rmw32.xchg_u","i32.atomic.rmw.cmpxchg","i64.atomic.rmw.cmpxchg","i32.atomic.rmw8.cmpxchg_u","i32.atomic.rmw16.cmpxchg_u","i64.atomic.rmw8.cmpxchg_u","i64.atomic.rmw16.cmpxchg_u","i64.atomic.rmw32.cmpxchg_u"].forEach(((e,t)=>{a[65024|t]=e})),a[64257]="struct.new_with_rtt",a[64258]="struct.new_default_with_rtt",a[64259]="struct.get",a[64260]="struct.get_s",a[64261]="struct.get_u",a[64262]="struct.set",a[64263]="struct.new",a[64264]="struct.new_default",a[64273]="array.new_with_rtt",a[64274]="array.new_default_with_rtt",a[64275]="array.get",a[64276]="array.get_s",a[64277]="array.get_u",a[64278]="array.set",a[64279]="array.len",a[64280]="array.copy",a[64281]="array.init",a[64282]="array.init_static",a[64283]="array.new",a[64284]="array.new_default",a[64288]="i31.new",a[64289]="i31.get_s",a[64290]="i31.get_u",a[64304]="rtt.canon",a[64305]="rtt.sub",a[64306]="rtt.fresh_sub",a[64320]="ref.test",a[64321]="ref.cast",a[64322]="br_on_cast",a[64323]="br_on_cast_fail",a[64324]="ref.test_static",a[64325]="ref.cast_static",a[64326]="br_on_cast_static",a[64327]="br_on_cast_static_fail",a[64336]="ref.is_func",a[64337]="ref.is_data",a[64338]="ref.is_i31",a[64344]="ref.as_func",a[64345]="ref.as_data",a[64346]="ref.as_i31",a[64352]="br_on_func",a[64353]="br_on_data",a[64354]="br_on_i31",a[64355]="br_on_non_func",a[64356]="br_on_non_data",a[64357]="br_on_non_i31",function(e){e[e.Function=0]="Function",e[e.Table=1]="Table",e[e.Memory=2]="Memory",e[e.Global=3]="Global",e[e.Event=4]="Event"}(i||(i={})),function(e){e[e.unspecified=0]="unspecified",e[e.i32=-1]="i32",e[e.i64=-2]="i64",e[e.f32=-3]="f32",e[e.f64=-4]="f64",e[e.v128=-5]="v128",e[e.i8=-6]="i8",e[e.i16=-7]="i16",e[e.funcref=-16]="funcref",e[e.externref=-17]="externref",e[e.anyref=-18]="anyref",e[e.eqref=-19]="eqref",e[e.optref=-20]="optref",e[e.ref=-21]="ref",e[e.i31ref=-22]="i31ref",e[e.rtt_d=-23]="rtt_d",e[e.rtt=-24]="rtt",e[e.dataref=-25]="dataref",e[e.func=-32]="func",e[e.struct=-33]="struct",e[e.array=-34]="array",e[e.func_subtype=-35]="func_subtype",e[e.struct_subtype=-36]="struct_subtype",e[e.array_subtype=-37]="array_subtype",e[e.empty_block_type=-64]="empty_block_type"}(r||(r={}));class p{constructor(e,t=-1,s=-1){if(!(e<0||0===e&&t>=0))throw new Error(`invalid type: ${e}/${t}/${s}`);this.kind=e,this.index=t,this.depth=s,(-16===t&&-20===e||-17===t&&-20===e||-18===t&&-20===e||-19===t&&-20===e||-22===t&&-21===e||-25===t&&-21===e)&&(this.kind=t,this.index=-1)}}p.funcref=new p(-16),p.externref=new p(-17),function(e){e[e.FunctionIndex_LEB=0]="FunctionIndex_LEB",e[e.TableIndex_SLEB=1]="TableIndex_SLEB",e[e.TableIndex_I32=2]="TableIndex_I32",e[e.GlobalAddr_LEB=3]="GlobalAddr_LEB",e[e.GlobalAddr_SLEB=4]="GlobalAddr_SLEB",e[e.GlobalAddr_I32=5]="GlobalAddr_I32",e[e.TypeIndex_LEB=6]="TypeIndex_LEB",e[e.GlobalIndex_LEB=7]="GlobalIndex_LEB"}(_||(_={})),function(e){e[e.StackPointer=1]="StackPointer"}(n||(n={})),function(e){e[e.Module=0]="Module",e[e.Function=1]="Function",e[e.Local=2]="Local",e[e.Event=3]="Event",e[e.Type=4]="Type",e[e.Table=5]="Table",e[e.Memory=6]="Memory",e[e.Global=7]="Global",e[e.Field=10]="Field"}(c||(c={})),function(e){e[e.ERROR=-1]="ERROR",e[e.INITIAL=0]="INITIAL",e[e.BEGIN_WASM=1]="BEGIN_WASM",e[e.END_WASM=2]="END_WASM",e[e.BEGIN_SECTION=3]="BEGIN_SECTION",e[e.END_SECTION=4]="END_SECTION",e[e.SKIPPING_SECTION=5]="SKIPPING_SECTION",e[e.READING_SECTION_RAW_DATA=6]="READING_SECTION_RAW_DATA",e[e.SECTION_RAW_DATA=7]="SECTION_RAW_DATA",e[e.TYPE_SECTION_ENTRY=11]="TYPE_SECTION_ENTRY",e[e.IMPORT_SECTION_ENTRY=12]="IMPORT_SECTION_ENTRY",e[e.FUNCTION_SECTION_ENTRY=13]="FUNCTION_SECTION_ENTRY",e[e.TABLE_SECTION_ENTRY=14]="TABLE_SECTION_ENTRY",e[e.MEMORY_SECTION_ENTRY=15]="MEMORY_SECTION_ENTRY",e[e.GLOBAL_SECTION_ENTRY=16]="GLOBAL_SECTION_ENTRY",e[e.EXPORT_SECTION_ENTRY=17]="EXPORT_SECTION_ENTRY",e[e.DATA_SECTION_ENTRY=18]="DATA_SECTION_ENTRY",e[e.NAME_SECTION_ENTRY=19]="NAME_SECTION_ENTRY",e[e.ELEMENT_SECTION_ENTRY=20]="ELEMENT_SECTION_ENTRY",e[e.LINKING_SECTION_ENTRY=21]="LINKING_SECTION_ENTRY",e[e.START_SECTION_ENTRY=22]="START_SECTION_ENTRY",e[e.EVENT_SECTION_ENTRY=23]="EVENT_SECTION_ENTRY",e[e.BEGIN_INIT_EXPRESSION_BODY=25]="BEGIN_INIT_EXPRESSION_BODY",e[e.INIT_EXPRESSION_OPERATOR=26]="INIT_EXPRESSION_OPERATOR",e[e.END_INIT_EXPRESSION_BODY=27]="END_INIT_EXPRESSION_BODY",e[e.BEGIN_FUNCTION_BODY=28]="BEGIN_FUNCTION_BODY",e[e.READING_FUNCTION_HEADER=29]="READING_FUNCTION_HEADER",e[e.CODE_OPERATOR=30]="CODE_OPERATOR",e[e.END_FUNCTION_BODY=31]="END_FUNCTION_BODY",e[e.SKIPPING_FUNCTION_BODY=32]="SKIPPING_FUNCTION_BODY",e[e.BEGIN_ELEMENT_SECTION_ENTRY=33]="BEGIN_ELEMENT_SECTION_ENTRY",e[e.ELEMENT_SECTION_ENTRY_BODY=34]="ELEMENT_SECTION_ENTRY_BODY",e[e.END_ELEMENT_SECTION_ENTRY=35]="END_ELEMENT_SECTION_ENTRY",e[e.BEGIN_DATA_SECTION_ENTRY=36]="BEGIN_DATA_SECTION_ENTRY",e[e.DATA_SECTION_ENTRY_BODY=37]="DATA_SECTION_ENTRY_BODY",e[e.END_DATA_SECTION_ENTRY=38]="END_DATA_SECTION_ENTRY",e[e.BEGIN_GLOBAL_SECTION_ENTRY=39]="BEGIN_GLOBAL_SECTION_ENTRY",e[e.END_GLOBAL_SECTION_ENTRY=40]="END_GLOBAL_SECTION_ENTRY",e[e.RELOC_SECTION_HEADER=41]="RELOC_SECTION_HEADER",e[e.RELOC_SECTION_ENTRY=42]="RELOC_SECTION_ENTRY",e[e.SOURCE_MAPPING_URL=43]="SOURCE_MAPPING_URL",e[e.BEGIN_OFFSET_EXPRESSION_BODY=44]="BEGIN_OFFSET_EXPRESSION_BODY",e[e.OFFSET_EXPRESSION_OPERATOR=45]="OFFSET_EXPRESSION_OPERATOR",e[e.END_OFFSET_EXPRESSION_BODY=46]="END_OFFSET_EXPRESSION_BODY"}(o||(o={})),function(e){e[e.Active=0]="Active",e[e.Passive=1]="Passive",e[e.ActiveWithMemoryIndex=2]="ActiveWithMemoryIndex"}(h||(h={})),function(e){e[e.Active=0]="Active",e[e.Passive=1]="Passive"}(u||(u={})),function(e){e[e.LegacyActiveFuncrefExternval=0]="LegacyActiveFuncrefExternval",e[e.PassiveExternval=1]="PassiveExternval",e[e.ActiveExternval=2]="ActiveExternval",e[e.DeclaredExternval=3]="DeclaredExternval",e[e.LegacyActiveFuncrefElemexpr=4]="LegacyActiveFuncrefElemexpr",e[e.PassiveElemexpr=5]="PassiveElemexpr",e[e.ActiveElemexpr=6]="ActiveElemexpr",e[e.DeclaredElemexpr=7]="DeclaredElemexpr"}(d||(d={})),function(e){e[e.Active=0]="Active",e[e.Passive=1]="Passive",e[e.Declarative=2]="Declarative"}(l||(l={}));class m{constructor(e,t){this.start=e,this.end=t}offset(e){this.start+=e,this.end+=e}}class b{constructor(e){this._data=e||new Uint8Array(8)}toInt32(){return this._data[0]|this._data[1]<<8|this._data[2]<<16|this._data[3]<<24}toDouble(){var e,t=1;if(128&this._data[7]){e=-1;for(var s=0;s<8;s++,t*=256)e-=t*(255^this._data[s])}else{e=0;for(s=0;s<8;s++,t*=256)e+=t*this._data[s]}return e}toString(){var e=(this._data[0]|this._data[1]<<8|this._data[2]<<16|this._data[3]<<24)>>>0,t=(this._data[4]|this._data[5]<<8|this._data[6]<<16|this._data[7]<<24)>>>0;if(0===e&&0===t)return"0";var s=!1;t>>31&&(t=4294967296-t,e>0&&(t--,e=4294967296-e),s=!0);for(var a=[];t>0;){var i=t%10*4294967296+e;t=Math.floor(t/10),a.unshift((i%10).toString()),e=Math.floor(i/10)}for(;e>0;)a.unshift((e%10).toString()),e=Math.floor(e/10);return s&&a.unshift("-"),a.join("")}get data(){return this._data}}if("undefined"!=typeof TextDecoder)try{(x=new TextDecoder("utf-8")).decode(new Uint8Array([97,208,144])),f=e=>x.decode(e)}catch(e){}f||(f=e=>{var t=String.fromCharCode.apply(null,e);return decodeURIComponent(escape(t))});var y=Object.freeze({__proto__:null,get SectionCode(){return t},get OperatorCode(){return s},OperatorCodeNames:a,get ExternalKind(){return i},get TypeKind(){return r},Type:p,get RelocType(){return _},get LinkingType(){return n},get NameType(){return c},get BinaryReaderState(){return o},get DataMode(){return u},get ElementMode(){return l},Int64:b,BinaryReader:class{constructor(){this._data=null,this._pos=0,this._length=0,this._eof=!1,this.state=0,this.result=null,this.error=null,this._sectionEntriesLeft=0,this._sectionId=-1,this._sectionRange=null,this._functionRange=null,this._segmentType=0,this._segmentEntriesLeft=0}get data(){return this._data}get position(){return this._pos}get length(){return this._length}setData(e,t,s,a){var i=t-this._pos;this._data=new Uint8Array(e),this._pos=t,this._length=s,this._eof=void 0===a||a,this._sectionRange&&this._sectionRange.offset(i),this._functionRange&&this._functionRange.offset(i)}hasBytes(e){return this._pos+e<=this._length}hasMoreBytes(){return this.hasBytes(1)}readUint8(){return this._data[this._pos++]}readInt32(){return this._data[this._pos++]|this._data[this._pos++]<<8|this._data[this._pos++]<<16|this._data[this._pos++]<<24}readUint32(){return this.readInt32()}peekInt32(){return this._data[this._pos]|this._data[this._pos+1]<<8|this._data[this._pos+2]<<16|this._data[this._pos+3]<<24}hasVarIntBytes(){for(var e=this._pos;e<this._length;)if(0==(128&this._data[e++]))return!0;return!1}readVarUint1(){return this.readUint8()}readVarInt7(){return this.readUint8()<<25>>25}readVarUint7(){return this.readUint8()}readVarInt32(){for(var e=0,t=0;;){var s=this.readUint8();if(e|=(127&s)<<t,t+=7,0==(128&s))break}if(t>=32)return e;var a=32-t;return e<<a>>a}readVarUint32(){for(var e=0,t=0;;){var s=this.readUint8();if(e|=(127&s)<<t,t+=7,0==(128&s))break}return e>>>0}readVarInt64(){for(var e=new Uint8Array(8),t=0,s=0,a=0;;){var i=this.readUint8();if(s|=(127&i)<<a,(a+=7)>8&&(e[t++]=255&s,s>>=8,a-=8),0==(128&i))break}var r=32-a;for(s=s<<r>>r;t<8;)e[t++]=255&s,s>>=8;return new b(e)}readHeapType(){for(var e,t=0,s=0;;){if(e=this.readUint8(),28===s)return(e<<25>>25)*Math.pow(2,28)+t;if(t|=(127&e)<<s,s+=7,0==(128&e))break}return t<<(s=32-s)>>s}readTypeInternal(e){if(-21===e||-20===e||-24===e){var t=this.readHeapType();return new p(e,t)}if(-23===e){t=this.readHeapType();var s=this.readVarUint32();return new p(e,t,s)}return new p(e)}readType(){var e=this.readVarInt7();return this.readTypeInternal(e)}readBlockType(){var e=this.readHeapType();return e<0?this.readTypeInternal(e):new p(0,e)}readStringBytes(){var e=this.readVarUint32();return this.readBytes(e)}readBytes(e){var t=this._data.subarray(this._pos,this._pos+e);return this._pos+=e,new Uint8Array(t)}skipBytes(e){this._pos+=e}hasStringBytes(){if(!this.hasVarIntBytes())return!1;var e=this._pos,t=this.readVarUint32(),s=this.hasBytes(t);return this._pos=e,s}hasSectionPayload(){return this.hasBytes(this._sectionRange.end-this._pos)}readFuncType(){for(var e=this.readVarUint32(),t=new Array(e),s=0;s<e;s++)t[s]=this.readType();var a=this.readVarUint1(),i=new Array(a);for(s=0;s<a;s++)i[s]=this.readType();return{form:-32,params:t,returns:i}}readFuncSubtype(){var e=this.readFuncType();return e.form=-35,e.supertype=this.readHeapType(),e}readStructType(){for(var e=this.readVarUint32(),t=new Array(e),s=new Array(e),a=0;a<e;a++)t[a]=this.readType(),s[a]=!!this.readVarUint1();return{form:-33,fields:t,mutabilities:s}}readStructSubtype(){var e=this.readStructType();return e.form=-36,e.supertype=this.readHeapType(),e}readArrayType(){return{form:-34,elementType:this.readType(),mutability:!!this.readVarUint1()}}readArraySubtype(){var e=this.readArrayType();return e.form=-37,e.supertype=this.readHeapType(),e}readResizableLimits(e){var t,s=this.readVarUint32();return e&&(t=this.readVarUint32()),{initial:s,maximum:t}}readTableType(){var e=this.readType(),t=this.readVarUint32();return{elementType:e,limits:this.readResizableLimits(!!(1&t))}}readMemoryType(){var e=this.readVarUint32(),t=!!(2&e);return{limits:this.readResizableLimits(!!(1&e)),shared:t}}readGlobalType(){if(!this.hasVarIntBytes())return null;var e=this._pos,t=this.readType();return this.hasVarIntBytes()?{contentType:t,mutability:this.readVarUint1()}:(this._pos=e,null)}readEventType(){return{attribute:this.readVarUint32(),typeIndex:this.readVarUint32()}}readTypeEntry(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();this.state=11;var e=this.readVarInt7();switch(e){case-32:this.result=this.readFuncType();break;case-35:this.result=this.readFuncSubtype();break;case-33:this.result=this.readStructType();break;case-36:this.result=this.readStructSubtype();break;case-34:this.result=this.readArrayType();break;case-37:this.result=this.readArraySubtype();break;default:throw new Error(`Unknown type kind: ${e}`)}return this._sectionEntriesLeft--,!0}readImportEntry(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();this.state=12;var e,t,s=this.readStringBytes(),a=this.readStringBytes(),i=this.readUint8();switch(i){case 0:e=this.readVarUint32();break;case 1:t=this.readTableType();break;case 2:t=this.readMemoryType();break;case 3:t=this.readGlobalType();break;case 4:t=this.readEventType()}return this.result={module:s,field:a,kind:i,funcTypeIndex:e,type:t},this._sectionEntriesLeft--,!0}readExportEntry(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();var e=this.readStringBytes(),t=this.readUint8(),s=this.readVarUint32();return this.state=17,this.result={field:e,kind:t,index:s},this._sectionEntriesLeft--,!0}readFunctionEntry(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();var e=this.readVarUint32();return this.state=13,this.result={typeIndex:e},this._sectionEntriesLeft--,!0}readTableEntry(){return 0===this._sectionEntriesLeft?(this.skipSection(),this.read()):(this.state=14,this.result=this.readTableType(),this._sectionEntriesLeft--,!0)}readMemoryEntry(){return 0===this._sectionEntriesLeft?(this.skipSection(),this.read()):(this.state=15,this.result=this.readMemoryType(),this._sectionEntriesLeft--,!0)}readEventEntry(){return 0===this._sectionEntriesLeft?(this.skipSection(),this.read()):(this.state=23,this.result=this.readEventType(),this._sectionEntriesLeft--,!0)}readGlobalEntry(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();var e=this.readGlobalType();return e?(this.state=39,this.result={type:e},this._sectionEntriesLeft--,!0):(this.state=16,!1)}readElementEntry(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();const e=this._pos;if(!this.hasMoreBytes())return this.state=20,!1;const t=this.readUint8();let s,a;switch(t){case 0:case 4:s=0,a=0;break;case 1:case 5:s=1;break;case 2:case 6:if(s=0,!this.hasVarIntBytes())return this.state=20,this._pos=e,!1;a=this.readVarUint32();break;case 3:case 7:s=2;break;default:throw new Error(`Unsupported element segment type ${t}`)}return this.state=33,this.result={mode:s,tableIndex:a},this._sectionEntriesLeft--,this._segmentType=t,!0}readElementEntryBody(){let e=p.funcref;switch(this._segmentType){case 1:case 2:case 3:if(!this.hasMoreBytes())return!1;this.skipBytes(1);break;case 5:case 6:case 7:if(!this.hasMoreBytes())return!1;e=this.readType();break;case 0:case 4:break;default:throw new Error(`Unsupported element segment type ${this._segmentType}`)}return this.state=34,this.result={elementType:e},!0}readDataEntry(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();const e=this._pos;if(!this.hasVarIntBytes())return this.state=18,!1;const t=this.readVarUint32();let s,a;switch(t){case 0:s=0,a=0;break;case 1:s=1;break;case 2:if(s=0,!this.hasVarIntBytes())return this._pos=e,this.state=18,!1;a=this.readVarUint32();break;default:throw new Error(`Unsupported data segment type ${t}`)}return this.state=36,this.result={mode:s,memoryIndex:a},this._sectionEntriesLeft--,this._segmentType=t,!0}readDataEntryBody(){return!!this.hasStringBytes()&&(this.state=37,this.result={data:this.readStringBytes()},!0)}readInitExpressionBody(){return this.state=25,this.result=null,!0}readOffsetExpressionBody(){return this.state=44,this.result=null,!0}readMemoryImmediate(){return{flags:this.readVarUint32(),offset:this.readVarUint32()}}readNameMap(){for(var e=this.readVarUint32(),t=[],s=0;s<e;s++){var a=this.readVarUint32(),i=this.readStringBytes();t.push({index:a,name:i})}return t}readNameEntry(){var e=this._pos;if(e>=this._sectionRange.end)return this.skipSection(),this.read();if(!this.hasVarIntBytes())return!1;var t=this.readVarUint7();if(!this.hasVarIntBytes())return this._pos=e,!1;var s,a=this.readVarUint32();if(!this.hasBytes(a))return this._pos=e,!1;switch(t){case 0:s={type:t,moduleName:this.readStringBytes()};break;case 1:case 3:case 4:case 5:case 6:case 7:s={type:t,names:this.readNameMap()};break;case 2:for(var i=this.readVarUint32(),r=[],_=0;_<i;_++){var n=this.readVarUint32();r.push({index:n,locals:this.readNameMap()})}s={type:t,funcs:r};break;case 10:var c=this.readVarUint32(),o=[];for(_=0;_<c;_++){var h=this.readVarUint32();o.push({index:h,fields:this.readNameMap()})}s={type:t,types:o};break;default:return this.skipBytes(a),this.read()}return this.state=19,this.result=s,!0}readRelocHeader(){if(!this.hasVarIntBytes())return!1;var e,t=this._pos,s=this.readVarUint7();if(0===s){if(!this.hasStringBytes())return this._pos=t,!1;e=this.readStringBytes()}return this.state=41,this.result={id:s,name:e},!0}readLinkingEntry(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();if(!this.hasVarIntBytes())return!1;var e,t=this._pos,s=this.readVarUint32();return 1!==s?(this.error=new Error(`Bad linking type: ${s}`),this.state=-1,!0):this.hasVarIntBytes()?(e=this.readVarUint32(),this.state=21,this.result={type:s,index:e},this._sectionEntriesLeft--,!0):(this._pos=t,!1)}readSourceMappingURL(){if(!this.hasStringBytes())return!1;var e=this.readStringBytes();return this.state=43,this.result={url:e},!0}readRelocEntry(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();if(!this.hasVarIntBytes())return!1;var e=this._pos,t=this.readVarUint7();if(!this.hasVarIntBytes())return this._pos=e,!1;var s=this.readVarUint32();if(!this.hasVarIntBytes())return this._pos=e,!1;var a,i=this.readVarUint32();switch(t){case 0:case 1:case 2:case 6:case 7:break;case 3:case 4:case 5:if(!this.hasVarIntBytes())return this._pos=e,!1;a=this.readVarUint32();break;default:return this.error=new Error(`Bad relocation type: ${t}`),this.state=-1,!0}return this.state=42,this.result={type:t,offset:s,index:i,addend:a},this._sectionEntriesLeft--,!0}readCodeOperator_0xfb(){if(!this._eof&&!this.hasBytes(12))return!1;var e,t,s,a,i;switch(e=64256|this._data[this._pos++]){case 64322:case 64323:case 64352:case 64355:case 64353:case 64356:case 64354:case 64357:t=this.readVarUint32();break;case 64326:case 64327:t=this.readVarUint32(),s=this.readHeapType();break;case 64275:case 64276:case 64277:case 64279:case 64278:case 64283:case 64273:case 64284:case 64274:case 64263:case 64257:case 64264:case 64258:case 64304:case 64305:case 64306:case 64324:case 64325:s=this.readHeapType();break;case 64280:s=this.readHeapType(),a=this.readHeapType();break;case 64259:case 64260:case 64261:case 64262:s=this.readHeapType(),i=this.readVarUint32();break;case 64281:case 64282:s=this.readHeapType(),t=this.readVarUint32();break;case 64336:case 64337:case 64338:case 64344:case 64345:case 64346:case 64320:case 64321:case 64288:case 64289:case 64290:break;default:return this.error=new Error(`Unknown operator: 0x${e.toString(16).padStart(4,"0")}`),this.state=-1,!0}return this.result={code:e,blockType:void 0,refType:s,srcType:a,brDepth:t,brTable:void 0,tableIndex:void 0,funcIndex:void 0,typeIndex:void 0,localIndex:void 0,globalIndex:void 0,fieldIndex:i,memoryAddress:void 0,literal:void 0,segmentIndex:void 0,destinationIndex:void 0,lines:void 0,lineIndex:void 0},!0}readCodeOperator_0xfc(){if(!this.hasVarIntBytes())return!1;var e,t,s,a=64512|this.readVarUint32();switch(a){case 64512:case 64513:case 64514:case 64515:case 64516:case 64517:case 64518:case 64519:break;case 64522:this.readVarUint1(),this.readVarUint1();break;case 64523:this.readVarUint1();break;case 64524:e=this.readVarUint32(),s=this.readVarUint32();break;case 64526:s=this.readVarUint32(),t=this.readVarUint32();break;case 64527:case 64528:case 64529:s=this.readVarUint32();break;case 64520:e=this.readVarUint32(),this.readVarUint1();break;case 64521:case 64525:e=this.readVarUint32();break;default:return this.error=new Error(`Unknown operator: 0x${a.toString(16).padStart(4,"0")}`),this.state=-1,!0}return this.result={code:a,blockType:void 0,selectType:void 0,refType:void 0,srcType:void 0,brDepth:void 0,brTable:void 0,funcIndex:void 0,typeIndex:void 0,tableIndex:s,localIndex:void 0,globalIndex:void 0,fieldIndex:void 0,memoryAddress:void 0,literal:void 0,segmentIndex:e,destinationIndex:t,lines:void 0,lineIndex:void 0},!0}readCodeOperator_0xfd(){var e=this._pos;if(!this._eof&&e+17>this._length)return!1;if(!this.hasVarIntBytes())return!1;var t,s,a,i,r=64768|this.readVarUint32();switch(r){case 64768:case 64769:case 64770:case 64771:case 64772:case 64773:case 64774:case 64775:case 64776:case 64777:case 64778:case 64779:case 64860:case 64861:t=this.readMemoryImmediate();break;case 64780:s=this.readBytes(16);break;case 64781:i=new Uint8Array(16);for(var _=0;_<i.length;_++)i[_]=this.readUint8();break;case 64789:case 64790:case 64791:case 64792:case 64793:case 64794:case 64795:case 64796:case 64797:case 64798:case 64799:case 64800:case 64801:case 64802:a=this.readUint8();break;case 64782:case 64783:case 64784:case 64785:case 64786:case 64787:case 64788:case 64803:case 64804:case 64805:case 64806:case 64807:case 64808:case 64809:case 64810:case 64811:case 64812:case 64813:case 64814:case 64815:case 64816:case 64817:case 64818:case 64819:case 64820:case 64821:case 64822:case 64823:case 64824:case 64825:case 64826:case 64827:case 64828:case 64829:case 64830:case 64831:case 64832:case 64833:case 64834:case 64835:case 64836:case 64837:case 64838:case 64839:case 64840:case 64841:case 64842:case 64843:case 64844:case 64845:case 64846:case 64847:case 64848:case 64849:case 64850:case 64851:case 64862:case 64863:case 64864:case 64865:case 64866:case 64867:case 64868:case 64869:case 64870:case 64871:case 64872:case 64873:case 64874:case 64875:case 64876:case 64877:case 64878:case 64879:case 64880:case 64881:case 64882:case 64883:case 64884:case 64885:case 64886:case 64887:case 64888:case 64889:case 64890:case 64891:case 64892:case 64893:case 64894:case 64895:case 64896:case 64897:case 64898:case 64899:case 64900:case 64901:case 64902:case 64903:case 64904:case 64905:case 64906:case 64907:case 64908:case 64909:case 64910:case 64911:case 64912:case 64913:case 64914:case 64915:case 64916:case 64917:case 64918:case 64919:case 64920:case 64921:case 64923:case 64924:case 64925:case 64926:case 64927:case 64928:case 64929:case 64931:case 64932:case 64935:case 64936:case 64937:case 64938:case 64939:case 64940:case 64941:case 64942:case 64945:case 64949:case 64950:case 64951:case 64952:case 64953:case 64954:case 64956:case 64957:case 64958:case 64959:case 64960:case 64961:case 64963:case 64964:case 64967:case 64968:case 64969:case 64970:case 64971:case 64972:case 64973:case 64974:case 64977:case 64981:case 64982:case 64983:case 64984:case 64985:case 64986:case 64987:case 64988:case 64989:case 64988:case 64989:case 64992:case 64992:case 64993:case 64995:case 64996:case 64997:case 64998:case 64999:case 65e3:case 65001:case 65002:case 65003:case 65004:case 65005:case 65007:case 65008:case 65009:case 65010:case 65011:case 65012:case 65013:case 65014:case 65015:case 65016:case 65017:case 65018:case 65019:case 65020:case 65021:case 65022:case 65023:break;default:return this.error=new Error(`Unknown operator: 0x${r.toString(16).padStart(4,"0")}`),this.state=-1,!0}return this.result={code:r,blockType:void 0,selectType:void 0,refType:void 0,srcType:void 0,brDepth:void 0,brTable:void 0,funcIndex:void 0,typeIndex:void 0,localIndex:void 0,globalIndex:void 0,fieldIndex:void 0,memoryAddress:t,literal:s,segmentIndex:void 0,destinationIndex:void 0,lines:i,lineIndex:a},!0}readCodeOperator_0xfe(){var e=this._pos;if(!this._eof&&e+11>this._length)return!1;if(!this.hasVarIntBytes())return!1;var t,s=65024|this.readVarUint32();switch(s){case 65024:case 65025:case 65026:case 65040:case 65041:case 65042:case 65043:case 65044:case 65045:case 65046:case 65047:case 65048:case 65049:case 65050:case 65051:case 65052:case 65053:case 65054:case 65055:case 65056:case 65057:case 65058:case 65059:case 65060:case 65061:case 65062:case 65063:case 65064:case 65065:case 65066:case 65067:case 65068:case 65069:case 65070:case 65071:case 65072:case 65073:case 65074:case 65075:case 65076:case 65077:case 65078:case 65079:case 65080:case 65081:case 65082:case 65083:case 65084:case 65085:case 65086:case 65087:case 65088:case 65089:case 65090:case 65091:case 65092:case 65093:case 65094:case 65095:case 65096:case 65097:case 65098:case 65099:case 65100:case 65101:case 65102:t=this.readMemoryImmediate();break;case 65027:if(0!=this.readUint8())return this.error=new Error("atomic.fence consistency model must be 0"),this.state=-1,!0;break;default:return this.error=new Error(`Unknown operator: 0x${s.toString(16).padStart(4,"0")}`),this.state=-1,!0}return this.result={code:s,blockType:void 0,selectType:void 0,refType:void 0,srcType:void 0,brDepth:void 0,brTable:void 0,funcIndex:void 0,typeIndex:void 0,localIndex:void 0,globalIndex:void 0,fieldIndex:void 0,memoryAddress:t,literal:void 0,segmentIndex:void 0,destinationIndex:void 0,lines:void 0,lineIndex:void 0},!0}readCodeOperator(){switch(this.state){case 30:if(this._pos>=this._functionRange.end)return this.skipFunctionBody(),this.read();break;case 26:if(this.result&&11===this.result.code)return this.state=27,this.result=null,!0;break;case 45:if(this.result&&11===this.result.code)return this.state=46,this.result=null,!0}var e,t,s,a,i,r,_,n,c,o,h,u,d,l,f;if(26===this.state&&9===this._sectionId&&function(e){switch(e){case 0:case 1:case 2:case 3:return!0;default:return!1}}(this._segmentType))if(this.result&&210===this.result.code)e=11;else{if(!this.hasVarIntBytes())return!1;e=210,n=this.readVarUint32()}else{const b=11;var x=this._pos;if(!this._eof&&x+b>this._length)return!1;switch(e=this._data[this._pos++]){case 2:case 3:case 4:case 6:t=this.readBlockType();break;case 12:case 13:case 212:case 214:i=this.readVarUint32();break;case 14:var p=this.readVarUint32();if(!this.hasBytes(p+1))return this._pos=x,!1;r=[];for(var m=0;m<=p;m++){if(!this.hasVarIntBytes())return this._pos=x,!1;r.push(this.readVarUint32())}break;case 9:case 24:_=this.readVarUint32();break;case 7:case 8:d=this.readVarInt32();break;case 208:a=this.readHeapType();break;case 16:case 18:case 210:n=this.readVarUint32();break;case 17:case 19:c=this.readVarUint32(),this.readVarUint1();break;case 32:case 33:case 34:h=this.readVarUint32();break;case 35:case 36:u=this.readVarUint32();break;case 37:case 38:o=this.readVarUint32();break;case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 58:case 59:case 60:case 61:case 62:l=this.readMemoryImmediate();break;case 63:case 64:this.readVarUint1();break;case 65:f=this.readVarInt32();break;case 66:f=this.readVarInt64();break;case 67:f=new DataView(this._data.buffer,this._data.byteOffset).getFloat32(this._pos,!0),this._pos+=4;break;case 68:f=new DataView(this._data.buffer,this._data.byteOffset).getFloat64(this._pos,!0),this._pos+=8;break;case 28:1==this.readVarInt32()&&(s=this.readType());break;case 251:return!!this.readCodeOperator_0xfb()||(this._pos=x,!1);case 252:return!!this.readCodeOperator_0xfc()||(this._pos=x,!1);case 253:return!!this.readCodeOperator_0xfd()||(this._pos=x,!1);case 254:return!!this.readCodeOperator_0xfe()||(this._pos=x,!1);case 0:case 1:case 5:case 10:case 11:case 15:case 25:case 26:case 27:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 91:case 92:case 93:case 94:case 95:case 96:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:case 123:case 124:case 125:case 126:case 127:case 128:case 129:case 130:case 131:case 132:case 133:case 134:case 135:case 136:case 137:case 138:case 139:case 140:case 141:case 142:case 143:case 144:case 145:case 146:case 147:case 148:case 149:case 150:case 151:case 152:case 153:case 154:case 155:case 156:case 157:case 158:case 159:case 160:case 161:case 162:case 163:case 164:case 165:case 166:case 167:case 168:case 169:case 170:case 171:case 172:case 173:case 174:case 175:case 176:case 177:case 178:case 179:case 180:case 181:case 182:case 183:case 184:case 185:case 186:case 187:case 188:case 189:case 190:case 191:case 192:case 193:case 194:case 195:case 196:case 20:case 21:case 209:case 211:case 213:break;default:return this.error=new Error(`Unknown operator: ${e}`),this.state=-1,!0}}return this.result={code:e,blockType:t,selectType:s,refType:a,srcType:void 0,brDepth:i,brTable:r,relativeDepth:_,tableIndex:o,funcIndex:n,typeIndex:c,localIndex:h,globalIndex:u,fieldIndex:void 0,eventIndex:d,memoryAddress:l,literal:f,segmentIndex:void 0,destinationIndex:void 0,lines:void 0,lineIndex:void 0},!0}readFunctionBody(){if(0===this._sectionEntriesLeft)return this.skipSection(),this.read();if(!this.hasVarIntBytes())return!1;var e=this._pos,t=this.readVarUint32(),s=this._pos+t;if(!this.hasVarIntBytes())return this._pos=e,!1;for(var a=this.readVarUint32(),i=[],r=0;r<a;r++){if(!this.hasVarIntBytes())return this._pos=e,!1;var _=this.readVarUint32();if(!this.hasVarIntBytes())return this._pos=e,!1;var n=this.readType();i.push({count:_,type:n})}var c=this._pos;return this.state=28,this.result={locals:i},this._functionRange=new m(c,s),this._sectionEntriesLeft--,!0}readSectionHeader(){if(this._pos>=this._length&&this._eof)return this._sectionId=-1,this._sectionRange=null,this.result=null,this.state=2,!0;if(this._pos<this._length-4&&this.peekInt32()===e)return this._sectionId=-1,this._sectionRange=null,this.result=null,this.state=2,!0;if(!this.hasVarIntBytes())return!1;var t=this._pos,s=this.readVarUint7();if(!this.hasVarIntBytes())return this._pos=t,!1;var a=this.readVarUint32(),i=null,r=this._pos+a;if(0==s){if(!this.hasStringBytes())return this._pos=t,!1;i=this.readStringBytes()}return this.result={id:s,name:i},this._sectionId=s,this._sectionRange=new m(this._pos,r),this.state=3,!0}readSectionRawData(){var e=this._sectionRange.end-this._sectionRange.start;return!!this.hasBytes(e)&&(this.state=7,this.result=this.readBytes(e),!0)}readSectionBody(){if(this._pos>=this._sectionRange.end)return this.result=null,this.state=4,this._sectionId=-1,this._sectionRange=null,!0;var e=this.result;switch(e.id){case 1:return!!this.hasSectionPayload()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readTypeEntry());case 2:return!!this.hasSectionPayload()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readImportEntry());case 7:return!!this.hasSectionPayload()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readExportEntry());case 3:return!!this.hasSectionPayload()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readFunctionEntry());case 4:return!!this.hasSectionPayload()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readTableEntry());case 5:return!!this.hasSectionPayload()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readMemoryEntry());case 6:return!!this.hasVarIntBytes()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readGlobalEntry());case 8:return!!this.hasVarIntBytes()&&(this.state=22,this.result={index:this.readVarUint32()},!0);case 10:return!!this.hasVarIntBytes()&&(this._sectionEntriesLeft=this.readVarUint32(),this.state=29,this.readFunctionBody());case 9:return!!this.hasVarIntBytes()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readElementEntry());case 11:return!!this.hasVarIntBytes()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readDataEntry());case 13:return!!this.hasVarIntBytes()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readEventEntry());case 0:var t=f(e.name);return"name"===t?this.readNameEntry():0===t.indexOf("reloc.")?this.readRelocHeader():"linking"===t?!!this.hasVarIntBytes()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readLinkingEntry()):"sourceMappingURL"===t?this.readSourceMappingURL():this.readSectionRawData();default:return this.error=new Error(`Unsupported section: ${this._sectionId}`),this.state=-1,!0}}read(){switch(this.state){case 0:if(!this.hasBytes(8))return!1;var t=this.readUint32();if(t!=e)return this.error=new Error("Bad magic number"),this.state=-1,!0;var s=this.readUint32();return 1!=s&&13!=s?(this.error=new Error(`Bad version number ${s}`),this.state=-1,!0):(this.result={magicNumber:t,version:s},this.state=1,!0);case 2:return this.result=null,this.state=1,!!this.hasMoreBytes()&&(this.state=0,this.read());case-1:return!0;case 1:case 4:return this.readSectionHeader();case 3:return this.readSectionBody();case 5:return!!this.hasSectionPayload()&&(this.state=4,this._pos=this._sectionRange.end,this._sectionId=-1,this._sectionRange=null,this.result=null,!0);case 32:return this.state=31,this._pos=this._functionRange.end,this._functionRange=null,this.result=null,!0;case 11:return this.readTypeEntry();case 12:return this.readImportEntry();case 17:return this.readExportEntry();case 13:return this.readFunctionEntry();case 14:return this.readTableEntry();case 15:return this.readMemoryEntry();case 23:return this.readEventEntry();case 16:case 40:return this.readGlobalEntry();case 39:return this.readInitExpressionBody();case 20:case 35:return this.readElementEntry();case 33:return function(e){switch(e){case 0:case 2:case 4:case 6:return!0;default:return!1}}(this._segmentType)?this.readOffsetExpressionBody():this.readElementEntryBody();case 34:return!!this.hasVarIntBytes()&&(this._segmentEntriesLeft=this.readVarUint32(),0===this._segmentEntriesLeft?(this.state=35,this.result=null,!0):this.readInitExpressionBody());case 18:case 38:return this.readDataEntry();case 36:return function(e){switch(e){case 0:case 2:return!0;default:return!1}}(this._segmentType)?this.readOffsetExpressionBody():this.readDataEntryBody();case 37:return this.state=38,this.result=null,!0;case 27:switch(this._sectionId){case 6:return this.state=40,!0;case 9:return--this._segmentEntriesLeft>0?this.readInitExpressionBody():(this.state=35,this.result=null,!0)}return this.error=new Error(`Unexpected section type: ${this._sectionId}`),this.state=-1,!0;case 46:return 11===this._sectionId?this.readDataEntryBody():this.readElementEntryBody();case 19:return this.readNameEntry();case 41:return!!this.hasVarIntBytes()&&(this._sectionEntriesLeft=this.readVarUint32(),this.readRelocEntry());case 21:return this.readLinkingEntry();case 43:return this.state=4,this.result=null,!0;case 42:return this.readRelocEntry();case 29:case 31:return this.readFunctionBody();case 28:return this.state=30,this.readCodeOperator();case 25:return this.state=26,this.readCodeOperator();case 44:return this.state=45,this.readCodeOperator();case 30:case 26:case 45:return this.readCodeOperator();case 6:return this.readSectionRawData();case 22:case 7:return this.state=4,this.result=null,!0;default:return this.error=new Error(`Unsupported state: ${this.state}`),this.state=-1,!0}}skipSection(){-1!==this.state&&0!==this.state&&4!==this.state&&1!==this.state&&2!==this.state&&(this.state=5)}skipFunctionBody(){28!==this.state&&30!==this.state||(this.state=32)}skipInitExpression(){for(;26===this.state;)this.readCodeOperator()}fetchSectionRawData(){if(3!==this.state)return this.error=new Error(`Unsupported state: ${this.state}`),void(this.state=-1);this.state=6}},get bytesToString(){return f}});const g="name",v=/[^0-9A-Za-z!#$%&'*+.:<=>?@^_`|~\/\-]/,E=new RegExp(v.source,"g");function N(e){return e.initial+(void 0!==e.maximum?" "+e.maximum:"")}var w=["0","00","000"];function T(e,t){var s=(e>>>0).toString(16).toUpperCase();if(void 0===t||s.length>=t)return s;for(var a=t-s.length-1;a>=w.length;)w.push(w[w.length-1]+"0");return w[a]+s}function I(e){return!v.test(e)}class B{getTypeName(e,t){return"$type"+e}getTableName(e,t){return"$table"+e}getMemoryName(e,t){return"$memory"+e}getGlobalName(e,t){return"$global"+e}getElementName(e,t){return`$elem${e}`}getEventName(e,t){return`$event${e}`}getFunctionName(e,t,s){return(t?"$import":"$func")+e}getVariableName(e,t,s){return"$var"+t}getFieldName(e,t,s){return"$field"+t}getLabel(e){return"$label"+e}}const k=[];class S{constructor(e,t,s,a,i){this._functionExportNames=e,this._globalExportNames=t,this._memoryExportNames=s,this._tableExportNames=a,this._eventExportNames=i}getFunctionExportNames(e){var t;return null!==(t=this._functionExportNames[e])&&void 0!==t?t:k}getGlobalExportNames(e){var t;return null!==(t=this._globalExportNames[e])&&void 0!==t?t:k}getMemoryExportNames(e){var t;return null!==(t=this._memoryExportNames[e])&&void 0!==t?t:k}getTableExportNames(e){var t;return null!==(t=this._tableExportNames[e])&&void 0!==t?t:k}getEventExportNames(e){var t;return null!==(t=this._eventExportNames[e])&&void 0!==t?t:k}}var O;!function(e){e[e.Depth=0]="Depth",e[e.WhenUsed=1]="WhenUsed",e[e.Always=2]="Always"}(O||(O={}));const R="unknown";class $ extends B{constructor(e,t,s,a,i,r,_,n){super(),this._functionNames=e,this._localNames=t,this._eventNames=s,this._typeNames=a,this._tableNames=i,this._memoryNames=r,this._globalNames=_,this._fieldNames=n}getTypeName(e,t){const s=this._typeNames[e];return s?t?`$${s}`:`$${s} (;${e};)`:super.getTypeName(e,t)}getTableName(e,t){const s=this._tableNames[e];return s?t?`$${s}`:`$${s} (;${e};)`:super.getTableName(e,t)}getMemoryName(e,t){const s=this._memoryNames[e];return s?t?`$${s}`:`$${s} (;${e};)`:super.getMemoryName(e,t)}getGlobalName(e,t){const s=this._globalNames[e];return s?t?`$${s}`:`$${s} (;${e};)`:super.getGlobalName(e,t)}getEventName(e,t){const s=this._eventNames[e];return s?t?`$${s}`:`$${s} (;${e};)`:super.getEventName(e,t)}getFunctionName(e,t,s){const a=this._functionNames[e];return a?s?`$${a}`:`$${a} (;${e};)`:`$${R}${e}`}getVariableName(e,t,s){const a=this._localNames[e]&&this._localNames[e][t];return a?s?`$${a}`:`$${a} (;${t};)`:super.getVariableName(e,t,s)}getFieldName(e,t,s){const a=this._fieldNames[e]&&this._fieldNames[e][t];return a?s?`$${a}`:`$${a} (;${t};)`:super.getFieldName(e,t,s)}}class L extends ${constructor(e,t,s,a,i,r,_,n){super(e,t,s,a,i,r,_,n)}getFunctionName(e,t,s){const a=this._functionNames[e];return a?s?`$${a}`:`$${a} (;${e};)`:t?`$import${e}`:`$func${e}`}}var U=Object.freeze({__proto__:null,DefaultNameResolver:B,NumericNameResolver:class{getTypeName(e,t){return t?""+e:`(;${e};)`}getTableName(e,t){return t?""+e:`(;${e};)`}getMemoryName(e,t){return t?""+e:`(;${e};)`}getGlobalName(e,t){return t?""+e:`(;${e};)`}getElementName(e,t){return t?""+e:`(;${e};)`}getEventName(e,t){return t?""+e:`(;${e};)`}getFunctionName(e,t,s){return s?""+e:`(;${e};)`}getVariableName(e,t,s){return s?""+t:`(;${t};)`}getFieldName(e,t,s){return s?"":t+`(;${t};)`}getLabel(e){return null}},get LabelMode(){return O},WasmDisassembler:class{constructor(){this._skipTypes=!0,this._exportMetadata=null,this._lines=[],this._offsets=[],this._buffer="",this._indent=null,this._indentLevel=0,this._addOffsets=!1,this._done=!1,this._currentPosition=0,this._nameResolver=new B,this._labelMode=O.WhenUsed,this._functionBodyOffsets=[],this._currentFunctionBodyOffset=0,this._currentSectionId=-1,this._logFirstInstruction=!1,this._reset()}_reset(){this._types=[],this._funcIndex=0,this._funcTypes=[],this._importCount=0,this._globalCount=0,this._memoryCount=0,this._eventCount=0,this._tableCount=0,this._elementCount=0,this._expression=[],this._backrefLabels=null,this._labelIndex=0}get addOffsets(){return this._addOffsets}set addOffsets(e){if(this._currentPosition)throw new Error("Cannot switch addOffsets during processing.");this._addOffsets=e}get skipTypes(){return this._skipTypes}set skipTypes(e){if(this._currentPosition)throw new Error("Cannot switch skipTypes during processing.");this._skipTypes=e}get labelMode(){return this._labelMode}set labelMode(e){if(this._currentPosition)throw new Error("Cannot switch labelMode during processing.");this._labelMode=e}get exportMetadata(){return this._exportMetadata}set exportMetadata(e){if(this._currentPosition)throw new Error("Cannot switch exportMetadata during processing.");this._exportMetadata=e}get nameResolver(){return this._nameResolver}set nameResolver(e){if(this._currentPosition)throw new Error("Cannot switch nameResolver during processing.");this._nameResolver=e}appendBuffer(e){this._buffer+=e}newLine(){this.addOffsets&&this._offsets.push(this._currentPosition),this._lines.push(this._buffer),this._buffer=""}logStartOfFunctionBodyOffset(){this.addOffsets&&(this._currentFunctionBodyOffset=this._currentPosition)}logEndOfFunctionBodyOffset(){this.addOffsets&&this._functionBodyOffsets.push({start:this._currentFunctionBodyOffset,end:this._currentPosition})}typeIndexToString(e){if(e>=0)return this._nameResolver.getTypeName(e,!0);switch(e){case-16:return"func";case-17:return"extern";case-18:return"any";case-19:return"eq";case-22:return"i31";case-25:return"data"}}typeToString(e){switch(e.kind){case-1:return"i32";case-2:return"i64";case-3:return"f32";case-4:return"f64";case-5:return"v128";case-6:return"i8";case-7:return"i16";case-16:return"funcref";case-17:return"externref";case-18:return"anyref";case-19:return"eqref";case-22:return"i31ref";case-25:return"dataref";case-21:return`(ref ${this.typeIndexToString(e.index)})`;case-20:return`(ref null ${this.typeIndexToString(e.index)})`;case-24:return`(rtt ${this.typeIndexToString(e.index)})`;case-23:return`(rtt ${e.depth} ${this.typeIndexToString(e.index)})`;default:throw new Error(`Unexpected type ${JSON.stringify(e)}`)}}maybeMut(e,t){return t?`(mut ${e})`:e}globalTypeToString(e){const t=this.typeToString(e.contentType);return this.maybeMut(t,!!e.mutability)}printFuncType(e){var t=this._types[e];if(t.params.length>0){this.appendBuffer(" (param");for(var s=0;s<t.params.length;s++)this.appendBuffer(" "),this.appendBuffer(this.typeToString(t.params[s]));this.appendBuffer(")")}if(t.returns.length>0){this.appendBuffer(" (result");for(s=0;s<t.returns.length;s++)this.appendBuffer(" "),this.appendBuffer(this.typeToString(t.returns[s]));this.appendBuffer(")")}}printStructType(e){var t=this._types[e];if(0!==t.fields.length)for(var s=0;s<t.fields.length;s++){const a=this.maybeMut(this.typeToString(t.fields[s]),t.mutabilities[s]),i=this._nameResolver.getFieldName(e,s,!1);this.appendBuffer(` (field ${i} ${a})`)}}printArrayType(e){var t=this._types[e];this.appendBuffer(" (field "),this.appendBuffer(this.maybeMut(this.typeToString(t.elementType),t.mutability))}printBlockType(e){if(-64!==e.kind){if(0===e.kind)return this.printFuncType(e.index);this.appendBuffer(" (result "),this.appendBuffer(this.typeToString(e)),this.appendBuffer(")")}}printString(e){this.appendBuffer('"');for(var t=0;t<e.length;t++){var s=e[t];s<32||s>=127||34==s||92==s?this.appendBuffer("\\"+(s>>4).toString(16)+(15&s).toString(16)):this.appendBuffer(String.fromCharCode(s))}this.appendBuffer('"')}printExpression(e){for(const t of e)this.appendBuffer("("),this.printOperator(t),this.appendBuffer(")")}useLabel(e,t=0){if(!this._backrefLabels)return""+e;var s=this._backrefLabels.length-e-1-t;if(s<0)return""+e;var a=this._backrefLabels[s];if(!a.useLabel){a.useLabel=!0,a.label=this._nameResolver.getLabel(this._labelIndex);var i=this._lines[a.line];this._lines[a.line]=i.substring(0,a.position)+" "+a.label+i.substring(a.position),this._labelIndex++}return a.label||""+e}printOperator(e){var t=e.code;switch(this.appendBuffer(a[t]),t){case 2:case 3:case 4:case 6:if(this._labelMode!==O.Depth){const e={line:this._lines.length,position:this._buffer.length,useLabel:!1,label:null};this._labelMode===O.Always&&(e.useLabel=!0,e.label=this._nameResolver.getLabel(this._labelIndex++),e.label&&(this.appendBuffer(" "),this.appendBuffer(e.label))),this._backrefLabels.push(e)}this.printBlockType(e.blockType);break;case 11:if(this._labelMode===O.Depth)break;const t=this._backrefLabels.pop();t.label&&(this.appendBuffer(" "),this.appendBuffer(t.label));break;case 12:case 13:case 212:case 214:case 64322:case 64323:case 64352:case 64355:case 64353:case 64356:case 64354:case 64357:this.appendBuffer(" "),this.appendBuffer(this.useLabel(e.brDepth));break;case 64326:case 64327:{const t=this.useLabel(e.brDepth),s=this._nameResolver.getTypeName(e.refType,!0);this.appendBuffer(` ${t} ${s}`);break}case 14:for(var s=0;s<e.brTable.length;s++)this.appendBuffer(" "),this.appendBuffer(this.useLabel(e.brTable[s]));break;case 9:this.appendBuffer(" "),this.appendBuffer(this.useLabel(e.relativeDepth));break;case 24:this.appendBuffer(" "),this.appendBuffer(this.useLabel(e.relativeDepth,1));break;case 7:case 8:var i=this._nameResolver.getEventName(e.eventIndex,!0);this.appendBuffer(` ${i}`);break;case 208:this.appendBuffer(" "),this.appendBuffer(this.typeIndexToString(e.refType));break;case 16:case 18:case 210:var r=this._nameResolver.getFunctionName(e.funcIndex,e.funcIndex<this._importCount,!0);this.appendBuffer(` ${r}`);break;case 17:case 19:this.printFuncType(e.typeIndex);break;case 28:{const t=this.typeToString(e.selectType);this.appendBuffer(` ${t}`);break}case 32:case 33:case 34:var _=this._nameResolver.getVariableName(this._funcIndex,e.localIndex,!0);this.appendBuffer(` ${_}`);break;case 35:case 36:var n=this._nameResolver.getGlobalName(e.globalIndex,!0);this.appendBuffer(` ${n}`);break;case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 58:case 59:case 60:case 61:case 62:case 65024:case 65025:case 65026:case 65040:case 65041:case 65042:case 65043:case 65044:case 65045:case 65046:case 65047:case 65048:case 65049:case 65050:case 65051:case 65052:case 65053:case 65054:case 65055:case 65056:case 65057:case 65058:case 65059:case 65060:case 65061:case 65062:case 65063:case 65064:case 65065:case 65066:case 65067:case 65068:case 65069:case 65070:case 65071:case 65072:case 65073:case 65074:case 65075:case 65076:case 65077:case 65078:case 65079:case 65080:case 65081:case 65082:case 65083:case 65084:case 65085:case 65086:case 65087:case 65088:case 65089:case 65090:case 65091:case 65092:case 65093:case 65094:case 65095:case 65096:case 65097:case 65098:case 65099:case 65100:case 65101:case 65102:case 64768:case 64769:case 64770:case 64771:case 64772:case 64773:case 64774:case 64775:case 64776:case 64777:case 64778:case 64779:case 64860:case 64861:var c=function(e,t){var s;switch(t){case 64768:case 64769:case 64770:case 64771:case 64772:case 64773:case 64774:case 64775:case 64776:case 64777:case 64778:case 64779:s=4;break;case 41:case 55:case 43:case 57:case 65026:case 65041:case 65048:case 65055:case 65062:case 65069:case 65076:case 65083:case 65090:case 65097:case 64861:s=3;break;case 40:case 52:case 53:case 54:case 62:case 42:case 56:case 65024:case 65025:case 65040:case 65046:case 65047:case 65053:case 65054:case 65060:case 65061:case 65067:case 65068:case 65074:case 65075:case 65081:case 65082:case 65088:case 65089:case 65095:case 65096:case 65102:case 64860:s=2;break;case 46:case 47:case 50:case 51:case 59:case 61:case 65043:case 65045:case 65050:case 65052:case 65057:case 65059:case 65064:case 65066:case 65071:case 65073:case 65078:case 65080:case 65085:case 65087:case 65092:case 65094:case 65099:case 65101:s=1;break;case 44:case 45:case 48:case 49:case 58:case 60:case 65042:case 65044:case 65049:case 65051:case 65056:case 65058:case 65063:case 65065:case 65070:case 65072:case 65077:case 65079:case 65084:case 65086:case 65091:case 65093:case 65098:case 65100:s=0}return e.flags==s?e.offset?`offset=${e.offset}`:null:e.offset?`offset=${0|e.offset} align=${1<<e.flags}`:"align="+(1<<e.flags)}(e.memoryAddress,e.code);null!==c&&(this.appendBuffer(" "),this.appendBuffer(c));break;case 63:case 64:break;case 65:case 66:this.appendBuffer(` ${e.literal.toString()}`);break;case 67:this.appendBuffer(` ${function(e){if(0===e)return 1/e<0?"-0.0":"0.0";if(isFinite(e))return e.toString();if(!isNaN(e))return e<0?"-inf":"inf";var t=new DataView(new ArrayBuffer(8));t.setFloat32(0,e,!0);var s=t.getInt32(0,!0),a=8388607&s;const i=4194304;return s>0&&a===i?"nan":a===i?"-nan":(s<0?"-":"+")+"nan:0x"+a.toString(16)}(e.literal)}`);break;case 68:this.appendBuffer(` ${function(e){if(0===e)return 1/e<0?"-0.0":"0.0";if(isFinite(e))return e.toString();if(!isNaN(e))return e<0?"-inf":"inf";var t=new DataView(new ArrayBuffer(8));t.setFloat64(0,e,!0);var s=t.getUint32(0,!0),a=t.getInt32(4,!0),i=s+4294967296*(1048575&a);const r=0x8000000000000;return a>0&&i===r?"nan":i===r?"-nan":(a<0?"-":"+")+"nan:0x"+i.toString(16)}(e.literal)}`);break;case 64780:this.appendBuffer(` i32x4 ${function(e,t){for(var s=new DataView(e.buffer,e.byteOffset,e.byteLength),a=[],i=0;i<t;i++)a.push(`0x${T(s.getInt32(i<<2,!0),8)}`);return a.join(" ")}(e.literal,4)}`);break;case 64781:this.appendBuffer(` ${function(e,t){for(var s=new DataView(e.buffer,e.byteOffset,e.byteLength),a=[],i=0;i<t;i++)a.push(`${s.getInt8(i)}`);return a.join(" ")}(e.lines,16)}`);break;case 64789:case 64790:case 64791:case 64792:case 64793:case 64794:case 64795:case 64796:case 64799:case 64800:case 64797:case 64798:case 64801:case 64802:this.appendBuffer(` ${e.lineIndex}`);break;case 64520:case 64521:this.appendBuffer(` ${e.segmentIndex}`);break;case 64525:const a=this._nameResolver.getElementName(e.segmentIndex,!0);this.appendBuffer(` ${a}`);break;case 38:case 37:case 64529:{const t=this._nameResolver.getTableName(e.tableIndex,!0);this.appendBuffer(` ${t}`);break}case 64526:if(0!==e.tableIndex||0!==e.destinationIndex){const t=this._nameResolver.getTableName(e.tableIndex,!0),s=this._nameResolver.getTableName(e.destinationIndex,!0);this.appendBuffer(` ${s} ${t}`)}break;case 64524:{if(0!==e.tableIndex){const t=this._nameResolver.getTableName(e.tableIndex,!0);this.appendBuffer(` ${t}`)}const t=this._nameResolver.getElementName(e.segmentIndex,!0);this.appendBuffer(` ${t}`);break}case 64259:case 64260:case 64261:case 64262:{const t=this._nameResolver.getTypeName(e.refType,!0),s=this._nameResolver.getFieldName(e.refType,e.fieldIndex,!0);this.appendBuffer(` ${t} ${s}`);break}case 64304:case 64305:case 64306:case 64324:case 64325:case 64264:case 64258:case 64263:case 64257:case 64284:case 64274:case 64283:case 64273:case 64275:case 64276:case 64277:case 64278:case 64279:{const t=this._nameResolver.getTypeName(e.refType,!0);this.appendBuffer(` ${t}`);break}case 64280:{const t=this._nameResolver.getTypeName(e.refType,!0),s=this._nameResolver.getTypeName(e.srcType,!0);this.appendBuffer(` ${t} ${s}`);break}case 64281:case 64282:{const t=this._nameResolver.getTypeName(e.refType,!0),s=e.brDepth;this.appendBuffer(` ${t} ${s}`);break}}}printImportSource(e){this.printString(e.module),this.appendBuffer(" "),this.printString(e.field)}increaseIndent(){this._indent+="  ",this._indentLevel++}decreaseIndent(){this._indent=this._indent.slice(0,-2),this._indentLevel--}disassemble(e){if(!this.disassembleChunk(e))return null;let t=this._lines;this._addOffsets&&(t=t.map(((e,t)=>e+" ;; @"+T(this._offsets[t],4)))),t.push("");const s=t.join("\n");return this._lines.length=0,this._offsets.length=0,this._functionBodyOffsets.length=0,s}getResult(){let e=this._lines.length;if(this._backrefLabels&&this._labelMode===O.WhenUsed&&this._backrefLabels.some((t=>!t.useLabel&&(e=t.line,!0))),0===e)return{lines:[],offsets:this._addOffsets?[]:void 0,done:this._done,functionBodyOffsets:this._addOffsets?[]:void 0};if(e===this._lines.length){const e={lines:this._lines,offsets:this._addOffsets?this._offsets:void 0,done:this._done,functionBodyOffsets:this._addOffsets?this._functionBodyOffsets:void 0};return this._lines=[],this._addOffsets&&(this._offsets=[],this._functionBodyOffsets=[]),e}const t={lines:this._lines.splice(0,e),offsets:this._addOffsets?this._offsets.splice(0,e):void 0,done:!1,functionBodyOffsets:this._addOffsets?this._functionBodyOffsets:void 0};return this._backrefLabels&&this._backrefLabels.forEach((t=>{t.line-=e})),t}disassembleChunk(e,t=0){if(this._done)throw new Error("Invalid state: disassembly process was already finished.");for(;;){if(this._currentPosition=e.position+t,!e.read())return!1;switch(e.state){case 2:if(this.appendBuffer(")"),this.newLine(),this._reset(),!e.hasMoreBytes())return this._done=!0,!0;break;case-1:throw e.error;case 1:this.appendBuffer("(module"),this.newLine();break;case 4:this._currentSectionId=-1;break;case 3:var s=e.result;switch(s.id){case 1:case 2:case 7:case 6:case 3:case 8:case 10:case 5:case 11:case 4:case 9:case 13:this._currentSectionId=s.id;break;default:e.skipSection()}break;case 15:var a=e.result,i=this._memoryCount++,r=this._nameResolver.getMemoryName(i,!1);if(this.appendBuffer(`  (memory ${r}`),null!==this._exportMetadata)for(const e of this._exportMetadata.getMemoryExportNames(i))this.appendBuffer(` (export ${JSON.stringify(e)})`);this.appendBuffer(` ${N(a.limits)}`),a.shared&&this.appendBuffer(" shared"),this.appendBuffer(")"),this.newLine();break;case 23:var _=e.result,n=this._eventCount++,c=this._nameResolver.getEventName(n,!1);if(this.appendBuffer(`  (event ${c}`),null!==this._exportMetadata)for(const e of this._exportMetadata.getEventExportNames(n))this.appendBuffer(` (export ${JSON.stringify(e)})`);this.printFuncType(_.typeIndex),this.appendBuffer(")"),this.newLine();break;case 14:var o=e.result,h=this._tableCount++,u=this._nameResolver.getTableName(h,!1);if(this.appendBuffer(`  (table ${u}`),null!==this._exportMetadata)for(const e of this._exportMetadata.getTableExportNames(h))this.appendBuffer(` (export ${JSON.stringify(e)})`);this.appendBuffer(` ${N(o.limits)} ${this.typeToString(o.elementType)})`),this.newLine();break;case 17:if(null===this._exportMetadata){var d=e.result;switch(this.appendBuffer("  (export "),this.printString(d.field),this.appendBuffer(" "),d.kind){case 0:var l=this._nameResolver.getFunctionName(d.index,d.index<this._importCount,!0);this.appendBuffer(`(func ${l})`);break;case 1:u=this._nameResolver.getTableName(d.index,!0);this.appendBuffer(`(table ${u})`);break;case 2:r=this._nameResolver.getMemoryName(d.index,!0);this.appendBuffer(`(memory ${r})`);break;case 3:var f=this._nameResolver.getGlobalName(d.index,!0);this.appendBuffer(`(global ${f})`);break;case 4:c=this._nameResolver.getEventName(d.index,!0);this.appendBuffer(`(event ${c})`);break;default:throw new Error(`Unsupported export ${d.kind}`)}this.appendBuffer(")"),this.newLine()}break;case 12:var x=e.result;switch(x.kind){case 0:this._importCount++;var p=this._funcIndex++;l=this._nameResolver.getFunctionName(p,!0,!1);if(this.appendBuffer(`  (func ${l}`),null!==this._exportMetadata)for(const e of this._exportMetadata.getFunctionExportNames(p))this.appendBuffer(` (export ${JSON.stringify(e)})`);this.appendBuffer(" (import "),this.printImportSource(x),this.appendBuffer(")"),this.printFuncType(x.funcTypeIndex),this.appendBuffer(")");break;case 3:var m=x.type,b=this._globalCount++;f=this._nameResolver.getGlobalName(b,!1);if(this.appendBuffer(`  (global ${f}`),null!==this._exportMetadata)for(const e of this._exportMetadata.getGlobalExportNames(b))this.appendBuffer(` (export ${JSON.stringify(e)})`);this.appendBuffer(" (import "),this.printImportSource(x),this.appendBuffer(`) ${this.globalTypeToString(m)})`);break;case 2:var y=x.type;i=this._memoryCount++,r=this._nameResolver.getMemoryName(i,!1);if(this.appendBuffer(`  (memory ${r}`),null!==this._exportMetadata)for(const e of this._exportMetadata.getMemoryExportNames(i))this.appendBuffer(` (export ${JSON.stringify(e)})`);this.appendBuffer(" (import "),this.printImportSource(x),this.appendBuffer(`) ${N(y.limits)}`),y.shared&&this.appendBuffer(" shared"),this.appendBuffer(")");break;case 1:var g=x.type;h=this._tableCount++,u=this._nameResolver.getTableName(h,!1);if(this.appendBuffer(`  (table ${u}`),null!==this._exportMetadata)for(const e of this._exportMetadata.getTableExportNames(h))this.appendBuffer(` (export ${JSON.stringify(e)})`);this.appendBuffer(" (import "),this.printImportSource(x),this.appendBuffer(`) ${N(g.limits)} ${this.typeToString(g.elementType)})`);break;case 4:var v=x.type;n=this._eventCount++,c=this._nameResolver.getEventName(n,!1);if(this.appendBuffer(`  (event ${c}`),null!==this._exportMetadata)for(const e of this._exportMetadata.getEventExportNames(n))this.appendBuffer(` (export ${JSON.stringify(e)})`);this.appendBuffer(" (import "),this.printImportSource(x),this.appendBuffer(")"),this.printFuncType(v.typeIndex),this.appendBuffer(")");break;default:throw new Error(`NYI other import types: ${x.kind}`)}this.newLine();break;case 33:var E=e.result,w=this._elementCount++,T=this._nameResolver.getElementName(w,!1);switch(this.appendBuffer(`  (elem ${T}`),E.mode){case 0:if(0!==E.tableIndex){const e=this._nameResolver.getTableName(E.tableIndex,!1);this.appendBuffer(` (table ${e})`)}break;case 1:break;case 2:this.appendBuffer(" declare")}break;case 35:this.appendBuffer(")"),this.newLine();break;case 34:const t=e.result;this.appendBuffer(` ${this.typeToString(t.elementType)}`);break;case 39:var I=e.result;b=this._globalCount++,f=this._nameResolver.getGlobalName(b,!1);if(this.appendBuffer(`  (global ${f}`),null!==this._exportMetadata)for(const e of this._exportMetadata.getGlobalExportNames(b))this.appendBuffer(` (export ${JSON.stringify(e)})`);this.appendBuffer(` ${this.globalTypeToString(I.type)}`);break;case 40:this.appendBuffer(")"),this.newLine();break;case 11:var B=e.result,k=this._types.length;if(this._types.push(B),!this._skipTypes){var S=this._nameResolver.getTypeName(k,!1),R=void 0;if(void 0!==B.supertype&&(R=this.typeIndexToString(B.supertype)),-32===B.form)this.appendBuffer(`  (type ${S} (func`),this.printFuncType(k),this.appendBuffer("))");else if(-35===B.form)this.appendBuffer(`  (type ${S} (func_subtype`),this.printFuncType(k),this.appendBuffer(` (supertype ${R})))`);else if(-33===B.form)this.appendBuffer(`  (type ${S} (struct`),this.printStructType(k),this.appendBuffer("))");else if(-36===B.form)this.appendBuffer(`  (type ${S} (struct_subtype`),this.printStructType(k),this.appendBuffer(` (supertype ${R})))`);else if(-34===B.form)this.appendBuffer(`  (type ${S} (array`),this.printArrayType(k),this.appendBuffer("))");else{if(-37!==B.form)throw new Error(`Unknown type form: ${B.form}`);this.appendBuffer(`  (type ${S} (array_subtype`),this.printArrayType(k),this.appendBuffer(`) (supertype ${R})))`)}this.newLine()}break;case 22:var $=e.result;l=this._nameResolver.getFunctionName($.index,$.index<this._importCount,!0);this.appendBuffer(`  (start ${l})`),this.newLine();break;case 36:this.appendBuffer("  (data");break;case 37:var L=e.result;this.appendBuffer(" "),this.printString(L.data);break;case 38:this.appendBuffer(")"),this.newLine();break;case 25:case 44:this._expression=[];break;case 26:case 45:11!==(F=e.result).code&&this._expression.push(F);break;case 46:this._expression.length>1?(this.appendBuffer(" (offset "),this.printExpression(this._expression),this.appendBuffer(")")):(this.appendBuffer(" "),this.printExpression(this._expression)),this._expression=[];break;case 27:this._expression.length>1&&9===this._currentSectionId?(this.appendBuffer(" (item "),this.printExpression(this._expression),this.appendBuffer(")")):(this.appendBuffer(" "),this.printExpression(this._expression)),this._expression=[];break;case 13:this._funcTypes.push(e.result.typeIndex);break;case 28:var U=e.result,V=this._types[this._funcTypes[this._funcIndex-this._importCount]];if(this.appendBuffer("  (func "),this.appendBuffer(this._nameResolver.getFunctionName(this._funcIndex,!1,!1)),null!==this._exportMetadata)for(const e of this._exportMetadata.getFunctionExportNames(this._funcIndex))this.appendBuffer(` (export ${JSON.stringify(e)})`);for(var C=0;C<V.params.length;C++){var M=this._nameResolver.getVariableName(this._funcIndex,C,!1);this.appendBuffer(` (param ${M} ${this.typeToString(V.params[C])})`)}for(C=0;C<V.returns.length;C++)this.appendBuffer(` (result ${this.typeToString(V.returns[C])})`);this.newLine();var A=V.params.length;if(U.locals.length>0){for(var D of(this.appendBuffer("   "),U.locals))for(C=0;C<D.count;C++){M=this._nameResolver.getVariableName(this._funcIndex,A++,!1);this.appendBuffer(` (local ${M} ${this.typeToString(D.type)})`)}this.newLine()}this._indent="    ",this._indentLevel=0,this._labelIndex=0,this._backrefLabels=this._labelMode===O.Depth?null:[],this._logFirstInstruction=!0;break;case 30:var F;if(this._logFirstInstruction&&(this.logStartOfFunctionBodyOffset(),this._logFirstInstruction=!1),11==(F=e.result).code&&0==this._indentLevel){this.appendBuffer("  )"),this.newLine();break}switch(F.code){case 11:case 5:case 7:case 25:case 10:case 24:this.decreaseIndent()}switch(this.appendBuffer(this._indent),this.printOperator(F),this.newLine(),F.code){case 4:case 2:case 3:case 5:case 6:case 7:case 25:case 10:this.increaseIndent()}break;case 31:this._funcIndex++,this._backrefLabels=null,this.logEndOfFunctionBodyOffset();break;default:throw new Error(`Expectected state: ${e.state}`)}}}},NameSectionReader:class{constructor(){this._done=!1,this._functionsCount=0,this._functionImportsCount=0,this._functionNames=null,this._functionLocalNames=null,this._eventNames=null,this._typeNames=null,this._tableNames=null,this._memoryNames=null,this._globalNames=null,this._fieldNames=null,this._hasNames=!1}read(e){if(this._done)throw new Error("Invalid state: disassembly process was already finished.");for(;;){if(!e.read())return!1;switch(e.state){case 2:if(!e.hasMoreBytes())return this._done=!0,!0;break;case-1:throw e.error;case 1:this._functionsCount=0,this._functionImportsCount=0,this._functionNames=[],this._functionLocalNames=[],this._eventNames=[],this._typeNames=[],this._tableNames=[],this._memoryNames=[],this._globalNames=[],this._fieldNames=[],this._hasNames=!1;break;case 4:break;case 3:var t=e.result;if(0===t.id&&f(t.name)===g)break;if(3===t.id||2===t.id)break;e.skipSection();break;case 12:0===e.result.kind&&this._functionImportsCount++;break;case 13:this._functionsCount++;break;case 19:const s=e.result;if(1===s.type){const{names:e}=s;e.forEach((({index:e,name:t})=>{this._functionNames[e]=f(t)})),this._hasNames=!0}else if(2===s.type){const{funcs:e}=s;e.forEach((({index:e,locals:t})=>{const s=this._functionLocalNames[e]=[];t.forEach((({index:e,name:t})=>{s[e]=f(t)}))})),this._hasNames=!0}else if(3===s.type){const{names:e}=s;e.forEach((({index:e,name:t})=>{this._eventNames[e]=f(t)})),this._hasNames=!0}else if(4===s.type){const{names:e}=s;e.forEach((({index:e,name:t})=>{this._typeNames[e]=f(t)})),this._hasNames=!0}else if(5===s.type){const{names:e}=s;e.forEach((({index:e,name:t})=>{this._tableNames[e]=f(t)})),this._hasNames=!0}else if(6===s.type){const{names:e}=s;e.forEach((({index:e,name:t})=>{this._memoryNames[e]=f(t)})),this._hasNames=!0}else if(7===s.type){const{names:e}=s;e.forEach((({index:e,name:t})=>{this._globalNames[e]=f(t)})),this._hasNames=!0}else if(10===s.type){const{types:e}=s;e.forEach((({index:e,fields:t})=>{const s=this._fieldNames[e]=[];t.forEach((({index:e,name:t})=>{s[e]=f(t)}))}))}break;default:throw new Error(`Expectected state: ${e.state}`)}}}hasValidNames(){return this._hasNames}getNameResolver(){if(!this.hasValidNames())throw new Error("Has no valid name section");const e=this._functionImportsCount+this._functionsCount,t=this._functionNames.slice(0,e),s=Object.create(null);for(let e=0;e<t.length;e++){const a=t[e];if(!a)continue;!(a in s)&&I(a)&&0!==a.indexOf(R)?s[a]=e:(s[a]>=0&&(t[s[a]]=null,s[a]=-1),t[e]=null)}return new $(t,this._functionLocalNames,this._eventNames,this._typeNames,this._tableNames,this._memoryNames,this._globalNames,this._fieldNames)}},DevToolsNameResolver:L,DevToolsNameGenerator:class{constructor(){this._done=!1,this._functionImportsCount=0,this._memoryImportsCount=0,this._tableImportsCount=0,this._globalImportsCount=0,this._eventImportsCount=0,this._functionNames=null,this._functionLocalNames=null,this._eventNames=null,this._memoryNames=null,this._typeNames=null,this._tableNames=null,this._globalNames=null,this._fieldNames=null,this._functionExportNames=null,this._globalExportNames=null,this._memoryExportNames=null,this._tableExportNames=null,this._eventExportNames=null}_addExportName(e,t,s){const a=e[t];a?a.push(s):e[t]=[s]}_setName(e,t,s,a){if(s)if(a){if(!I(s))return;e[t]=s}else e[t]||(e[t]=s.replace(E,"_"))}read(e){if(this._done)throw new Error("Invalid state: disassembly process was already finished.");for(;;){if(!e.read())return!1;switch(e.state){case 2:if(!e.hasMoreBytes())return this._done=!0,!0;break;case-1:throw e.error;case 1:this._functionImportsCount=0,this._memoryImportsCount=0,this._tableImportsCount=0,this._globalImportsCount=0,this._eventImportsCount=0,this._functionNames=[],this._functionLocalNames=[],this._eventNames=[],this._memoryNames=[],this._typeNames=[],this._tableNames=[],this._globalNames=[],this._fieldNames=[],this._functionExportNames=[],this._globalExportNames=[],this._memoryExportNames=[],this._tableExportNames=[],this._eventExportNames=[];break;case 4:break;case 3:var t=e.result;if(0===t.id&&f(t.name)===g)break;switch(t.id){case 2:case 7:break;default:e.skipSection()}break;case 12:var s=e.result;const i=`${f(s.module)}.${f(s.field)}`;switch(s.kind){case 0:this._setName(this._functionNames,this._functionImportsCount++,i,!1);break;case 1:this._setName(this._tableNames,this._tableImportsCount++,i,!1);break;case 2:this._setName(this._memoryNames,this._memoryImportsCount++,i,!1);break;case 3:this._setName(this._globalNames,this._globalImportsCount++,i,!1);break;case 4:this._setName(this._eventNames,this._eventImportsCount++,i,!1);default:throw new Error(`Unsupported export ${s.kind}`)}break;case 19:const r=e.result;if(1===r.type){const{names:e}=r;e.forEach((({index:e,name:t})=>{this._setName(this._functionNames,e,f(t),!0)}))}else if(2===r.type){const{funcs:e}=r;e.forEach((({index:e,locals:t})=>{const s=this._functionLocalNames[e]=[];t.forEach((({index:e,name:t})=>{s[e]=f(t)}))}))}else if(3===r.type){const{names:e}=r;e.forEach((({index:e,name:t})=>{this._setName(this._eventNames,e,f(t),!0)}))}else if(4===r.type){const{names:e}=r;e.forEach((({index:e,name:t})=>{this._setName(this._typeNames,e,f(t),!0)}))}else if(5===r.type){const{names:e}=r;e.forEach((({index:e,name:t})=>{this._setName(this._tableNames,e,f(t),!0)}))}else if(6===r.type){const{names:e}=r;e.forEach((({index:e,name:t})=>{this._setName(this._memoryNames,e,f(t),!0)}))}else if(7===r.type){const{names:e}=r;e.forEach((({index:e,name:t})=>{this._setName(this._globalNames,e,f(t),!0)}))}else if(10===r.type){const{types:e}=r;e.forEach((({index:e,fields:t})=>{const s=this._fieldNames[e]=[];t.forEach((({index:e,name:t})=>{s[e]=f(t)}))}))}break;case 17:var a=e.result;const _=f(a.field);switch(a.kind){case 0:this._addExportName(this._functionExportNames,a.index,_),this._setName(this._functionNames,a.index,_,!1);break;case 3:this._addExportName(this._globalExportNames,a.index,_),this._setName(this._globalNames,a.index,_,!1);break;case 2:this._addExportName(this._memoryExportNames,a.index,_),this._setName(this._memoryNames,a.index,_,!1);break;case 1:this._addExportName(this._tableExportNames,a.index,_),this._setName(this._tableNames,a.index,_,!1);break;case 4:this._addExportName(this._eventExportNames,a.index,_),this._setName(this._eventNames,a.index,_,!1);break;default:throw new Error(`Unsupported export ${a.kind}`)}break;default:throw new Error(`Expectected state: ${e.state}`)}}}getExportMetadata(){return new S(this._functionExportNames,this._globalExportNames,this._memoryExportNames,this._tableExportNames,this._eventExportNames)}getNameResolver(){return new L(this._functionNames,this._functionLocalNames,this._eventNames,this._typeNames,this._tableNames,this._memoryNames,this._globalNames,this._fieldNames)}}});export{U as WasmDis,y as WasmParser};
