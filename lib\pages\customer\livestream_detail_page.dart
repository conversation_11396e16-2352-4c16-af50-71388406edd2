import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/livestream_model.dart';
import '../../models/product_model.dart';
import '../../services/product_service.dart';
import '../../services/message_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/product_card.dart';

class LivestreamDetailPage extends StatefulWidget {
  final LiveStream livestream;

  const LivestreamDetailPage({Key? key, required this.livestream})
    : super(key: key);

  @override
  State<LivestreamDetailPage> createState() => _LivestreamDetailPageState();
}

class _LivestreamDetailPageState extends State<LivestreamDetailPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _chatScrollController = ScrollController();
  final List<Map<String, dynamic>> _messages = [];
  final List<Map<String, dynamic>> _reactions = [];
  bool _isFullScreen = false;

  @override
  void initState() {
    super.initState();
    _loadInitialMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _chatScrollController.dispose();
    super.dispose();
  }

  void _loadInitialMessages() {
    // Simulate initial messages
    setState(() {
      _messages.addAll([
        {
          'id': '1',
          'senderId': 'seller',
          'senderName': widget.livestream.storeName,
          'senderImage': widget.livestream.storeImageUrl,
          'text':
              'Welcome to our livestream! We\'re excited to show you our latest products.',
          'time': DateTime.now().subtract(const Duration(minutes: 5)),
          'isSystem': false,
        },
        {
          'id': '2',
          'senderId': 'system',
          'senderName': 'System',
          'text': 'Stream started',
          'time': DateTime.now().subtract(const Duration(minutes: 5)),
          'isSystem': true,
        },
        {
          'id': '3',
          'senderId': 'user1',
          'senderName': 'Ahmed',
          'senderImage': 'https://randomuser.me/api/portraits/men/32.jpg',
          'text': 'Hello! I\'m excited to see the new collection.',
          'time': DateTime.now().subtract(const Duration(minutes: 3)),
          'isSystem': false,
        },
        {
          'id': '4',
          'senderId': 'user2',
          'senderName': 'Fatima',
          'senderImage': 'https://randomuser.me/api/portraits/women/44.jpg',
          'text': 'The colors look amazing!',
          'time': DateTime.now().subtract(const Duration(minutes: 2)),
          'isSystem': false,
        },
      ]);
    });
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    setState(() {
      _messages.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'senderId': 'currentUser',
        'senderName': 'You',
        'senderImage': 'https://randomuser.me/api/portraits/men/1.jpg',
        'text': _messageController.text,
        'time': DateTime.now(),
        'isSystem': false,
      });
    });

    _messageController.clear();
    _scrollToBottom();

    // Simulate seller response after 5 seconds
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _messages.add({
            'id': DateTime.now().millisecondsSinceEpoch.toString(),
            'senderId': 'seller',
            'senderName': widget.livestream.storeName,
            'senderImage': widget.livestream.storeImageUrl,
            'text':
                'Thanks for your message! Let me know if you have any questions about our products.',
            'time': DateTime.now(),
            'isSystem': false,
          });
        });
        _scrollToBottom();
      }
    });
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _addReaction(String type) {
    setState(() {
      _reactions.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'type': type,
        'position': _reactions.length % 5, // Distribute across 5 positions
      });
    });

    // Remove reaction after animation
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          if (_reactions.isNotEmpty) {
            _reactions.removeAt(0);
          }
        });
      }
    });
  }

  void _toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Video Stream Section
            _buildVideoSection(),

            // Chat and Products Section
            if (!_isFullScreen)
              Expanded(
                child: Row(
                  children: [
                    // Chat Section
                    Expanded(flex: 3, child: _buildChatSection()),

                    // Featured Products Section
                    Expanded(flex: 2, child: _buildProductsSection()),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoSection() {
    return Stack(
      children: [
        // Video Player (Placeholder)
        Container(
          height: _isFullScreen ? MediaQuery.of(context).size.height : 250,
          width: double.infinity,
          color: Colors.black,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.videocam, color: AppColors.primary, size: 48),
                const SizedBox(height: 16),
                Text(
                  'Live Stream Video',
                  style: TextStyle(color: Colors.white),
                ),
                const SizedBox(height: 8),
                Text(
                  'This is a placeholder for the actual video stream',
                  style: TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),
          ),
        ),

        // Stream Info Overlay
        Positioned(
          top: 16,
          left: 16,
          child: Row(
            children: [
              // Back Button
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(Icons.arrow_back, color: Colors.white),
                ),
              ),
              const SizedBox(width: 12),

              // Stream Info
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.circle,
                      color: AppColors.liveIndicator,
                      size: 10,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'LIVE',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '${widget.livestream.viewerCount} watching',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Stream Title and Store Info
        Positioned(
          bottom: 16,
          left: 16,
          right: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.livestream.title,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  CircleAvatar(
                    radius: 14,
                    backgroundImage: NetworkImage(
                      widget.livestream.storeImageUrl,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.livestream.storeName,
                    style: TextStyle(color: Colors.white, fontSize: 14),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Fullscreen Toggle
        Positioned(
          top: 16,
          right: 16,
          child: GestureDetector(
            onTap: _toggleFullScreen,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                _isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen,
                color: Colors.white,
              ),
            ),
          ),
        ),

        // Reaction Buttons
        if (!_isFullScreen)
          Positioned(
            bottom: 16,
            right: 16,
            child: Column(
              children: [
                _buildReactionButton(
                  Icons.favorite,
                  Colors.red,
                  () => _addReaction('heart'),
                ),
                const SizedBox(height: 8),
                _buildReactionButton(
                  Icons.thumb_up,
                  Colors.blue,
                  () => _addReaction('like'),
                ),
                const SizedBox(height: 8),
                _buildReactionButton(
                  Icons.emoji_emotions,
                  Colors.amber,
                  () => _addReaction('smile'),
                ),
              ],
            ),
          ),

        // Floating Reactions Animation
        ..._buildFloatingReactions(),
      ],
    );
  }

  Widget _buildReactionButton(IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(icon, color: color, size: 20),
      ),
    );
  }

  List<Widget> _buildFloatingReactions() {
    return _reactions.map((reaction) {
      final icons = {
        'heart': Icons.favorite,
        'like': Icons.thumb_up,
        'smile': Icons.emoji_emotions,
      };

      final colors = {
        'heart': Colors.red,
        'like': Colors.blue,
        'smile': Colors.amber,
      };

      // Calculate position based on the reaction's position value
      final positionFactor = reaction['position'] * 0.2; // 0.0 to 0.8
      final horizontalPosition =
          100.0 + (positionFactor * 200.0); // Distribute horizontally as double

      return Positioned(
        bottom: 100,
        right: horizontalPosition,
        child: TweenAnimationBuilder<double>(
          tween: Tween(begin: 0.0, end: 200.0),
          duration: const Duration(seconds: 2),
          builder: (context, value, child) {
            return Opacity(
              opacity: 1.0 - (value / 200.0),
              child: Transform.translate(
                offset: Offset(0, -value),
                child: Icon(
                  icons[reaction['type']] ?? Icons.favorite,
                  color: colors[reaction['type']] ?? Colors.red,
                  size: 30,
                ),
              ),
            );
          },
        ),
      );
    }).toList();
  }

  Widget _buildChatSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(right: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Column(
        children: [
          // Chat Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13), // 0.05 opacity
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              children: [
                const Icon(Icons.chat, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'Live Chat',
                  style: AppTextStyles.heading3.copyWith(fontSize: 16),
                ),
                const Spacer(),
                Text(
                  '${_messages.length} messages',
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),

          // Chat Messages
          Expanded(
            child: ListView.builder(
              controller: _chatScrollController,
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return _buildChatMessage(message);
              },
            ),
          ),

          // Message Input
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13), // 0.05 opacity
                  blurRadius: 4,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: 'Type a message...',
                      border: InputBorder.none,
                    ),
                    textCapitalization: TextCapitalization.sentences,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.send),
                  color: AppColors.primary,
                  onPressed: _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatMessage(Map<String, dynamic> message) {
    final isSystem = message['isSystem'] ?? false;
    final isSeller = message['senderId'] == 'seller';
    final isCurrentUser = message['senderId'] == 'currentUser';

    if (isSystem) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              message['text'],
              style: TextStyle(color: Colors.grey.shade700, fontSize: 12),
            ),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isCurrentUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundImage: NetworkImage(message['senderImage']),
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment:
                  isCurrentUser
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
              children: [
                if (!isCurrentUser)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      message['senderName'],
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color:
                            isSeller ? AppColors.primary : Colors.grey.shade700,
                      ),
                    ),
                  ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color:
                        isCurrentUser
                            ? AppColors.primary.withAlpha(51)
                            : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    message['text'],
                    style: TextStyle(
                      color: isCurrentUser ? AppColors.primary : Colors.black,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    _formatTime(message['time']),
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade500),
                  ),
                ),
              ],
            ),
          ),
          if (isCurrentUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundImage: NetworkImage(message['senderImage']),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductsSection() {
    return Consumer<ProductService>(
      builder: (context, productService, _) {
        final featuredProductIds = widget.livestream.featuredProductIds;
        final featuredProducts =
            productService.products
                .where((product) => featuredProductIds.contains(product.id))
                .toList();

        return Container(
          color: Colors.grey.shade50,
          child: Column(
            children: [
              // Products Header
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(13), // 0.05 opacity
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Icon(Icons.shopping_bag, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'Featured Products',
                      style: AppTextStyles.heading3.copyWith(fontSize: 16),
                    ),
                    const Spacer(),
                    Text(
                      '${featuredProducts.length} items',
                      style: AppTextStyles.bodySmall,
                    ),
                  ],
                ),
              ),

              // Products List
              Expanded(
                child:
                    featuredProducts.isEmpty
                        ? Center(
                          child: Text(
                            'No featured products',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        )
                        : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: featuredProducts.length,
                          itemBuilder: (context, index) {
                            final product = featuredProducts[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: InkWell(
                                onTap: () {
                                  // Navigate to product details
                                  Navigator.pushNamed(
                                    context,
                                    '/product-details',
                                    arguments: product,
                                  );
                                },
                                borderRadius: BorderRadius.circular(12),
                                child: Padding(
                                  padding: const EdgeInsets.all(12),
                                  child: Row(
                                    children: [
                                      // Product Image
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: Image.network(
                                          product.imageUrl,
                                          width: 60,
                                          height: 60,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      const SizedBox(width: 12),

                                      // Product Info
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              product.name,
                                              style: AppTextStyles.bodyMedium
                                                  .copyWith(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              '\$${product.price.toStringAsFixed(2)}',
                                              style: AppTextStyles.bodyMedium
                                                  .copyWith(
                                                    color: AppColors.primary,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // Add to Cart Button
                                      IconButton(
                                        icon: const Icon(
                                          Icons.add_shopping_cart,
                                          color: AppColors.primary,
                                        ),
                                        onPressed: () {
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                '${product.name} added to cart',
                                              ),
                                              duration: const Duration(
                                                seconds: 1,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
