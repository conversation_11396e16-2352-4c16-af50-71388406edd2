/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * `console` methods that have no behaviour other than emitting a
 * Runtime.consoleAPICalled message.
 */

// console.clear
FORWARDING_CONSOLE_METHOD(clear, ConsoleAPIType::kClear)
// console.debug
FORWARDING_CONSOLE_METHOD(debug, ConsoleAPIType::kDebug)
// console.dir
FORWARDING_CONSOLE_METHOD(dir, ConsoleAPIType::kDir)
// console.dirxml
FORWARDING_CONSOLE_METHOD(dirxml, ConsoleAPIType::kDirXML)
// console.error
FORWARDING_CONSOLE_METHOD(error, ConsoleAPIType::kError)
// console.group
FORWARDING_CONSOLE_METHOD(group, ConsoleAPIType::kStartGroup)
// console.groupCollapsed
FORWARDING_CONSOLE_METHOD(groupCollapsed, ConsoleAPIType::kStartGroupCollapsed)
// console.groupEnd
FORWARDING_CONSOLE_METHOD(groupEnd, ConsoleAPIType::kEndGroup)
// console.info
FORWARDING_CONSOLE_METHOD(info, ConsoleAPIType::kInfo)
// console.log
FORWARDING_CONSOLE_METHOD(log, ConsoleAPIType::kLog)
// console.table
FORWARDING_CONSOLE_METHOD(table, ConsoleAPIType::kTable)
// console.trace
FORWARDING_CONSOLE_METHOD(trace, ConsoleAPIType::kTrace)
// console.warn
FORWARDING_CONSOLE_METHOD(warn, ConsoleAPIType::kWarning)
