import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as o from"../../ui/legacy/legacy.js";const s={console:"Console",showConsole:"Show Console",toggleConsole:"Toggle Console",clearConsole:"Clear console",clearConsoleHistory:"Clear console history",createLiveExpression:"Create live expression",hideNetworkMessages:"Hide network messages",showNetworkMessages:"Show network messages",selectedContextOnly:"Selected context only",onlyShowMessagesFromTheCurrent:"Only show messages from the current context (`top`, `iframe`, `worker`, extension)",showMessagesFromAllContexts:"Show messages from all contexts",logXmlhttprequests:"Log XMLHttpRequests",timestamps:"Timestamps",showTimestamps:"Show timestamps",hideTimestamps:"Hide timestamps",autocompleteFromHistory:"Autocomplete from history",doNotAutocompleteFromHistory:"Do not autocomplete from history",autocompleteOnEnter:"Accept autocomplete suggestion on Enter",doNotAutocompleteOnEnter:"Do not accept autocomplete suggestion on Enter",groupSimilarMessagesInConsole:"Group similar messages in console",doNotGroupSimilarMessagesIn:"Do not group similar messages in console",showCorsErrorsInConsole:"Show `CORS` errors in console",doNotShowCorsErrorsIn:"Do not show `CORS` errors in console",eagerEvaluation:"Eager evaluation",eagerlyEvaluateConsolePromptText:"Eagerly evaluate console prompt text",doNotEagerlyEvaluateConsole:"Do not eagerly evaluate console prompt text",evaluateTriggersUserActivation:"Treat code evaluation as user action",treatEvaluationAsUserActivation:"Treat evaluation as user activation",doNotTreatEvaluationAsUser:"Do not treat evaluation as user activation",expandConsoleTraceMessagesByDefault:"Automatically expand `console.trace()` messages",collapseConsoleTraceMessagesByDefault:"Do not automatically expand `console.trace()` messages"},n=t.i18n.registerUIStrings("panels/console/console-meta.ts",s),a=t.i18n.getLazilyComputedLocalizedString.bind(void 0,n);let i;async function l(){return i||(i=await import("./console.js")),i}o.ViewManager.registerViewExtension({location:"panel",id:"console",title:a(s.console),commandPrompt:a(s.showConsole),order:20,loadView:async()=>(await l()).ConsolePanel.ConsolePanel.instance()}),o.ViewManager.registerViewExtension({location:"drawer-view",id:"console-view",title:a(s.console),commandPrompt:a(s.showConsole),persistence:"permanent",order:0,loadView:async()=>(await l()).ConsolePanel.WrapperView.instance()}),o.ActionRegistration.registerActionExtension({actionId:"console.toggle",category:"CONSOLE",title:a(s.toggleConsole),loadActionDelegate:async()=>new((await l()).ConsoleView.ActionDelegate),bindings:[{shortcut:"Ctrl+`",keybindSets:["devToolsDefault","vsCode"]}]}),o.ActionRegistration.registerActionExtension({actionId:"console.clear",category:"CONSOLE",title:a(s.clearConsole),iconClass:"clear",loadActionDelegate:async()=>new((await l()).ConsoleView.ActionDelegate),contextTypes(){return e=e=>[e.ConsoleView.ConsoleView],void 0===i?[]:e(i);var e},bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),o.ActionRegistration.registerActionExtension({actionId:"console.clear.history",category:"CONSOLE",title:a(s.clearConsoleHistory),loadActionDelegate:async()=>new((await l()).ConsoleView.ActionDelegate)}),o.ActionRegistration.registerActionExtension({actionId:"console.create-pin",category:"CONSOLE",title:a(s.createLiveExpression),iconClass:"eye",loadActionDelegate:async()=>new((await l()).ConsoleView.ActionDelegate)}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:a(s.hideNetworkMessages),settingName:"hide-network-messages",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:a(s.hideNetworkMessages)},{value:!1,title:a(s.showNetworkMessages)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:a(s.selectedContextOnly),settingName:"selected-context-filter-enabled",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:a(s.onlyShowMessagesFromTheCurrent)},{value:!1,title:a(s.showMessagesFromAllContexts)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:a(s.logXmlhttprequests),settingName:"monitoring-xhr-enabled",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:a(s.timestamps),settingName:"console-timestamps-enabled",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:a(s.showTimestamps)},{value:!1,title:a(s.hideTimestamps)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",title:a(s.autocompleteFromHistory),settingName:"console-history-autocomplete",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:a(s.autocompleteFromHistory)},{value:!1,title:a(s.doNotAutocompleteFromHistory)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:a(s.autocompleteOnEnter),settingName:"console-autocomplete-on-enter",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:a(s.autocompleteOnEnter)},{value:!1,title:a(s.doNotAutocompleteOnEnter)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:a(s.groupSimilarMessagesInConsole),settingName:"console-group-similar",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:a(s.groupSimilarMessagesInConsole)},{value:!1,title:a(s.doNotGroupSimilarMessagesIn)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",title:a(s.showCorsErrorsInConsole),settingName:"console-shows-cors-errors",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:a(s.showCorsErrorsInConsole)},{value:!1,title:a(s.doNotShowCorsErrorsIn)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:a(s.eagerEvaluation),settingName:"console-eager-eval",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:a(s.eagerlyEvaluateConsolePromptText)},{value:!1,title:a(s.doNotEagerlyEvaluateConsole)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:a(s.evaluateTriggersUserActivation),settingName:"console-user-activation-eval",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:a(s.treatEvaluationAsUserActivation)},{value:!1,title:a(s.doNotTreatEvaluationAsUser)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:a(s.expandConsoleTraceMessagesByDefault),settingName:"console-trace-expand",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:a(s.expandConsoleTraceMessagesByDefault)},{value:!1,title:a(s.collapseConsoleTraceMessagesByDefault)}]}),e.Revealer.registerRevealer({contextTypes:()=>[e.Console.Console],destination:void 0,loadRevealer:async()=>new((await l()).ConsolePanel.ConsoleRevealer)});
