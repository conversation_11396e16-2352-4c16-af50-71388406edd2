/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// @generated by enums.py
// clang-format off
#pragma once

#include <cstdint>
#include <yoga/YGEnums.h>
#include <yoga/enums/YogaEnums.h>

namespace facebook::yoga {

enum class Wrap : uint8_t {
  NoWrap = YGWrapNoWrap,
  Wrap = YGWrapWrap,
  WrapReverse = YGWrapWrapReverse,
};

template <>
constexpr int32_t ordinalCount<Wrap>() {
  return 3;
}

constexpr Wrap scopedEnum(YGWrap unscoped) {
  return static_cast<Wrap>(unscoped);
}

constexpr YGWrap unscopedEnum(Wrap scoped) {
  return static_cast<YGWrap>(scoped);
}

inline const char* toString(Wrap e) {
  return YGWrapToString(unscopedEnum(e));
}

} // namespace facebook::yoga
