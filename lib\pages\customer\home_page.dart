import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/product_model.dart';
import '../../services/product_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/product_card.dart';
import '../../widgets/search_bar.dart';
import '../../widgets/enhanced_image_slider.dart';
import '../../widgets/welcome_notification.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _sortOption = 'Newest';
  bool _showScrollToTop = false;
  bool _showWelcomeNotification = true;

  final List<String> _categories = [
    'All',
    'Home Decor',
    'Beauty',
    'Furniture',
    'Kitchen',
    'Food',
    'Clothing',
    'Technology',
    'Digital Products',
    'Other',
  ];

  final List<String> _sortOptions = [
    'Newest',
    'Price: Low to High',
    'Price: High to Low',
  ];

  // Sample promotional banners for carousel
  final List<Map<String, dynamic>> _promotions = [
    {
      'title': 'Summer Sale',
      'subtitle': 'Up to 50% off',
      'image':
          'https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
      'color': Colors.orange,
    },
    {
      'title': 'New Arrivals',
      'subtitle': 'Check out our latest products',
      'image':
          'https://images.unsplash.com/photo-1607083206968-13611e3d76db?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
      'color': Colors.blue,
    },
    {
      'title': 'Handcrafted',
      'subtitle': 'Authentic Moroccan crafts',
      'image':
          'https://images.unsplash.com/photo-1590736969955-71cc94c4628b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
      'color': AppColors.secondary,
    },
  ];

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);

    // Auto-hide welcome notification after 3 seconds
    if (_showWelcomeNotification) {
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _showWelcomeNotification = false;
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.offset >= 300 && !_showScrollToTop) {
      setState(() {
        _showScrollToTop = true;
      });
    } else if (_scrollController.offset < 300 && _showScrollToTop) {
      setState(() {
        _showScrollToTop = false;
      });
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton:
          _showScrollToTop
              ? FloatingActionButton(
                onPressed: _scrollToTop,
                backgroundColor: AppColors.primary,
                mini: true,
                child: const Icon(Icons.arrow_upward),
              )
              : null,
      body: Stack(
        children: [
          SafeArea(
            child: RefreshIndicator(
              onRefresh: () async {
                final productService = Provider.of<ProductService>(
                  context,
                  listen: false,
                );
                await productService.refreshProducts();
              },
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // App Bar with Search
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          // App Logo
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primary.withAlpha(
                                    51,
                                  ), // 0.2 opacity
                                  blurRadius: 8,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: const Center(
                              child: Text(
                                'HH',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),

                          // Search Bar
                          Expanded(
                            child: CustomSearchBar(
                              controller: _searchController,
                              onChanged: (value) {
                                setState(() {
                                  _searchQuery = value;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Categories
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        children: [
                          Text(
                            'Categories',
                            style: AppTextStyles.heading3.copyWith(
                              fontSize: 16,
                            ),
                          ),
                          const Spacer(),

                          // Sort dropdown
                          PopupMenuButton<String>(
                            initialValue: _sortOption,
                            icon: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Sort: $_sortOption',
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                const Icon(
                                  Icons.arrow_drop_down,
                                  color: AppColors.primary,
                                  size: 20,
                                ),
                              ],
                            ),
                            onSelected: (String value) {
                              setState(() {
                                _sortOption = value;
                              });
                            },
                            itemBuilder: (BuildContext context) {
                              return _sortOptions.map((String option) {
                                return PopupMenuItem<String>(
                                  value: option,
                                  child: Text(option),
                                );
                              }).toList();
                            },
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Categories Chips
                  SliverToBoxAdapter(
                    child: SizedBox(
                      height: 50,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          final isSelected = category == _selectedCategory;

                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: ChoiceChip(
                              label: Text(category),
                              selected: isSelected,
                              selectedColor: AppColors.primary,
                              backgroundColor: Colors.white,
                              labelStyle: TextStyle(
                                color:
                                    isSelected
                                        ? Colors.white
                                        : AppColors.textPrimary,
                              ),
                              onSelected: (selected) {
                                if (selected) {
                                  setState(() {
                                    _selectedCategory = category;
                                  });
                                }
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  // Promotional Carousel
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 16,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Text(
                              'Featured Promotions',
                              style: AppTextStyles.heading3.copyWith(
                                fontSize: 16,
                              ),
                            ),
                          ),
                          EnhancedImageSlider(
                            height: 180,
                            autoPlayInterval: const Duration(seconds: 3),
                            indicatorActiveColor: AppColors.primary,
                            indicatorInactiveColor: Colors.grey.withAlpha(
                              76,
                            ), // 0.3 opacity
                            borderRadius: 16,
                            onPageChanged: (index) {
                              // Track page changes if needed
                            },
                            items:
                                _promotions
                                    .map(
                                      (promotion) => SliderItem(
                                        imageUrl: promotion['image'],
                                        title: promotion['title'],
                                        subtitle: promotion['subtitle'],
                                        onTap: () {
                                          // Handle promotion tap
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Promotion: ${promotion['title']}',
                                              ),
                                              duration: const Duration(
                                                seconds: 1,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                    .toList(),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Products Grid
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Text(
                            'Products',
                            style: AppTextStyles.heading3.copyWith(
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Products Grid
                  SliverPadding(
                    padding: const EdgeInsets.all(16),
                    sliver: Consumer<ProductService>(
                      builder: (context, productService, _) {
                        if (productService.isLoading) {
                          return const SliverFillRemaining(
                            child: Center(
                              child: CircularProgressIndicator(
                                color: AppColors.primary,
                              ),
                            ),
                          );
                        }

                        // Show error state with retry option
                        if (productService.error != null) {
                          return SliverFillRemaining(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: AppColors.error,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Failed to load products',
                                    style: AppTextStyles.bodyLarge.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    productService.error!,
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 16),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      productService.clearError();
                                      productService.refreshProducts();
                                    },
                                    icon: const Icon(Icons.refresh),
                                    label: const Text('Retry'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.primary,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        List<Product> filteredProducts =
                            productService.products;

                        // Apply category filter
                        if (_selectedCategory != 'All') {
                          filteredProducts =
                              filteredProducts
                                  .where(
                                    (product) =>
                                        product.category == _selectedCategory,
                                  )
                                  .toList();
                        }

                        // Apply search filter
                        if (_searchQuery.isNotEmpty) {
                          filteredProducts = productService.searchProducts(
                            _searchQuery,
                          );

                          // Apply category filter to search results
                          if (_selectedCategory != 'All') {
                            filteredProducts =
                                filteredProducts
                                    .where(
                                      (product) =>
                                          product.category == _selectedCategory,
                                    )
                                    .toList();
                          }
                        }

                        // Apply sorting
                        switch (_sortOption) {
                          case 'Price: Low to High':
                            filteredProducts.sort(
                              (a, b) => a.price.compareTo(b.price),
                            );
                            break;
                          case 'Price: High to Low':
                            filteredProducts.sort(
                              (a, b) => b.price.compareTo(a.price),
                            );
                            break;
                          case 'Newest':
                          default:
                            filteredProducts.sort(
                              (a, b) => b.createdAt.compareTo(a.createdAt),
                            );
                            break;
                        }

                        if (filteredProducts.isEmpty) {
                          return SliverFillRemaining(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.search_off,
                                    size: 64,
                                    color: AppColors.textSecondary,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No products found',
                                    style: AppTextStyles.bodyLarge.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        return SliverGrid(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                mainAxisSpacing: 16,
                                crossAxisSpacing: 16,
                                childAspectRatio:
                                    0.65, // Adjusted for better fit
                              ),
                          delegate: SliverChildBuilderDelegate((
                            context,
                            index,
                          ) {
                            final product = filteredProducts[index];
                            return ProductCard(product: product);
                          }, childCount: filteredProducts.length),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Welcome notification
          if (_showWelcomeNotification)
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              left: 16,
              right: 16,
              child: WelcomeNotification(
                message:
                    'Welcome to HadiHia! Discover amazing Moroccan products.',
                backgroundColor: AppColors.primary,
                onDismiss: () {
                  setState(() {
                    _showWelcomeNotification = false;
                  });
                },
              ),
            ),
        ],
      ),
    );
  }
}
