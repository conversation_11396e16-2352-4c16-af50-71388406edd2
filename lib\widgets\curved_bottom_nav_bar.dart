import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../theme/app_colors.dart';
import '../services/message_service.dart';
import '../services/auth_service.dart';
import '../models/user_model.dart';

class CurvedBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<BottomNavItem> items;
  final Color backgroundColor;
  final Color selectedItemColor;
  final Color unselectedItemColor;

  const CurvedBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor = Colors.white,
    this.selectedItemColor = AppColors.primary,
    this.unselectedItemColor = AppColors.textSecondary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Curved shape
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 20,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 8,
                    offset: const Offset(0, -3),
                  ),
                ],
              ),
            ),
          ),

          // Nav items
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(items.length, (index) {
              final isSelected = currentIndex == index;
              return _buildNavItem(index, isSelected);
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(int index, bool isSelected) {
    final item = items[index];

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap(index);
      },
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        width: isSelected ? 70 : 60,
        height: isSelected ? 70 : 60,
        margin: EdgeInsets.only(top: isSelected ? 5 : 15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon with background
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              padding: EdgeInsets.all(isSelected ? 12 : 8),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? selectedItemColor.withAlpha(26) // 0.1 opacity
                        : Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: Icon(
                item.icon,
                color: isSelected ? selectedItemColor : unselectedItemColor,
                size: isSelected ? 26 : 22,
              ),
            ),

            const SizedBox(height: 4),

            // Label
            Text(
              item.label,
              style: TextStyle(
                color: isSelected ? selectedItemColor : unselectedItemColor,
                fontSize: isSelected ? 12 : 11,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class BottomNavItem {
  final IconData icon;
  final String label;

  const BottomNavItem({required this.icon, required this.label});
}

class CustomerCurvedBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomerCurvedBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CurvedBottomNavBar(
      currentIndex: currentIndex,
      onTap: onTap,
      selectedItemColor: AppColors.primary,
      items: const [
        BottomNavItem(icon: Icons.shopping_bag, label: 'Home'),
        BottomNavItem(icon: Icons.live_tv, label: 'Live'),
        BottomNavItem(icon: Icons.chat, label: 'Chat'),
        BottomNavItem(icon: Icons.shopping_cart, label: 'Cart'),
        BottomNavItem(icon: Icons.person, label: 'Account'),
      ],
    );
  }
}

class SellerCurvedBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const SellerCurvedBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get message service and auth service
    final messageService = Provider.of<MessageService>(context, listen: true);
    final authService = Provider.of<AuthService>(context, listen: true);

    // Get unread messages count
    int unreadCount = 0;
    if (authService.currentUser != null) {
      final conversations = messageService.getConversationsForUser(
        authService.currentUser!.id,
        UserType.seller,
      );

      unreadCount = conversations.fold(
        0,
        (sum, conversation) => sum + conversation.unreadCount,
      );
    }

    return CurvedBottomNavBar(
      currentIndex: currentIndex,
      onTap: onTap,
      selectedItemColor: AppColors.secondary,
      items: [
        const BottomNavItem(icon: Icons.inventory, label: 'Products'),
        const BottomNavItem(icon: Icons.receipt, label: 'Orders'),
        BottomNavItem(
          icon: unreadCount > 0 ? Icons.chat : Icons.chat_bubble_outline,
          label:
              unreadCount > 0
                  ? 'Messages (${unreadCount > 9 ? '9+' : unreadCount})'
                  : 'Messages',
        ),
        const BottomNavItem(icon: Icons.live_tv, label: 'Live'),
        const BottomNavItem(icon: Icons.person, label: 'Account'),
      ],
    );
  }
}
