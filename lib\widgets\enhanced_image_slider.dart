import 'dart:async';
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

class SliderItem {
  final String imageUrl;
  final String? title;
  final String? subtitle;
  final VoidCallback? onTap;

  SliderItem({required this.imageUrl, this.title, this.subtitle, this.onTap});
}

class EnhancedImageSlider extends StatefulWidget {
  final List<SliderItem> items;
  final double height;
  final Duration autoPlayInterval;
  final Duration animationDuration;
  final Curve animationCurve;
  final bool showIndicators;
  final Color indicatorActiveColor;
  final Color indicatorInactiveColor;
  final double indicatorSize;
  final double borderRadius;
  final BoxFit imageFit;
  final bool enableGradientOverlay;
  final Function(int)? onPageChanged;

  const EnhancedImageSlider({
    Key? key,
    required this.items,
    this.height = 200.0,
    this.autoPlayInterval = const Duration(seconds: 3),
    this.animationDuration = const Duration(milliseconds: 500),
    this.animationCurve = Curves.easeInOut,
    this.showIndicators = true,
    this.indicatorActiveColor = Colors.white,
    this.indicatorInactiveColor = Colors.white54,
    this.indicatorSize = 8.0,
    this.borderRadius = 12.0,
    this.imageFit = BoxFit.cover,
    this.enableGradientOverlay = true,
    this.onPageChanged,
  }) : super(key: key);

  @override
  State<EnhancedImageSlider> createState() => _EnhancedImageSliderState();
}

class _EnhancedImageSliderState extends State<EnhancedImageSlider>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late Timer _autoPlayTimer;
  int _currentPage = 0;
  bool _isDisposed = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0, viewportFraction: 0.95);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
    _startAutoPlay();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _autoPlayTimer.cancel();
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _startAutoPlay() {
    _autoPlayTimer = Timer.periodic(widget.autoPlayInterval, (timer) {
      if (!_isDisposed && mounted) {
        if (_currentPage < widget.items.length - 1) {
          _currentPage++;
        } else {
          _currentPage = 0;
        }

        _pageController.animateToPage(
          _currentPage,
          duration: widget.animationDuration,
          curve: widget.animationCurve,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: widget.height,
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.items.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
                _animationController.reset();
                _animationController.forward();
              });
              if (widget.onPageChanged != null) {
                widget.onPageChanged!(index);
              }
            },
            itemBuilder: (context, index) {
              return AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _currentPage == index ? _scaleAnimation.value : 0.9,
                    child: child,
                  );
                },
                child: GestureDetector(
                  onTap: widget.items[index].onTap,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 5),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      child: _buildSliderItem(widget.items[index]),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        if (widget.showIndicators) ...[
          const SizedBox(height: 12),
          _buildPageIndicator(),
        ],
      ],
    );
  }

  Widget _buildSliderItem(SliderItem item) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // Image
        Image.network(
          item.imageUrl,
          fit: widget.imageFit,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                value:
                    loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                        : null,
                color: AppColors.primary,
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[300],
              child: const Center(
                child: Icon(Icons.error_outline, color: Colors.grey, size: 40),
              ),
            );
          },
        ),

        // Gradient overlay for better text readability
        if (widget.enableGradientOverlay &&
            (item.title != null || item.subtitle != null))
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withAlpha(26), // 0.1 opacity
                    Colors.black.withAlpha(128), // 0.5 opacity
                  ],
                  stops: const [0.6, 0.8, 1.0],
                ),
              ),
            ),
          ),

        // Text content
        if (item.title != null || item.subtitle != null)
          Positioned(
            left: 16,
            right: 16,
            bottom: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (item.title != null)
                  Text(
                    item.title!,
                    style: AppTextStyles.heading2.copyWith(
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 3.0,
                          color: Colors.black.withAlpha(128), // 0.5 opacity
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                if (item.subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    item.subtitle!,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 2.0,
                          color: Colors.black.withAlpha(128), // 0.5 opacity
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.items.length,
        (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          height: widget.indicatorSize,
          width:
              _currentPage == index
                  ? widget.indicatorSize * 2.5
                  : widget.indicatorSize,
          decoration: BoxDecoration(
            color:
                _currentPage == index
                    ? widget.indicatorActiveColor
                    : widget.indicatorInactiveColor,
            borderRadius: BorderRadius.circular(widget.indicatorSize / 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(51), // 0.2 opacity
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
