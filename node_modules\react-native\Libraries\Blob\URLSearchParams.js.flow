/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow
 */

declare export class URLSearchParams {
  _searchParams: Array<[string, string]>;
  constructor(params?: Record<string, string>): void;
  append(key: string, value: string): void;
  delete(name: string): empty;
  get(name: string): empty;
  getAll(name: string): empty;
  has(name: string): empty;
  set(name: string, value: string): empty;
  sort(): empty;
  @@iterator(): Iterator<[string, string]>;
  toString(): string;
}
