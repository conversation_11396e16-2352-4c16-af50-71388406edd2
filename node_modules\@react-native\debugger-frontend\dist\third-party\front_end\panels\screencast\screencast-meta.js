import*as e from"../../core/common/common.js";import*as r from"../../ui/legacy/legacy.js";let o;async function a(){return o||(o=await import("./screencast.js")),o}r.Toolbar.registerToolbarItem({loadItem:async()=>(await a()).ScreencastApp.ToolbarButtonProvider.instance(),order:1,location:"main-toolbar-left"}),e.AppProvider.registerAppProvider({loadAppProvider:async()=>(await a()).ScreencastApp.ScreencastAppProvider.instance(),order:1}),r.ContextMenu.registerItem({location:"mainMenu",order:10,actionId:"components.request-app-banner"});
