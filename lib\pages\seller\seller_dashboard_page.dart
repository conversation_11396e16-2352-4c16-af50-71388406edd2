import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/order_model.dart';
import '../../models/product_model.dart';
import '../../services/order_service.dart';
import '../../services/product_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/welcome_popup.dart';
import 'components/my_products_section.dart';
import 'components/orders_section.dart';
import 'components/messages_section.dart';

class SellerDashboardPage extends StatefulWidget {
  const SellerDashboardPage({Key? key}) : super(key: key);

  @override
  State<SellerDashboardPage> createState() => _SellerDashboardPageState();
}

class _SellerDashboardPageState extends State<SellerDashboardPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showWelcomePopup = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Show welcome popup after a short delay
    if (_showWelcomePopup) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted && _showWelcomePopup) {
          _showWelcomeDialog();
        }
      });
    }
  }

  void _showWelcomeDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => WelcomePopup(
        userName: 'Seller',
        backgroundColor: AppColors.secondary,
        onDismiss: () {
          setState(() {
            _showWelcomePopup = false;
          });
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Seller Dashboard'),
        backgroundColor: AppColors.secondary,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'My Products'),
            Tab(text: 'Orders'),
            Tab(text: 'Messages'),
          ],
        ),
        actions: [
          // Notification icon with badge
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () {
                  // Handle notifications
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Notifications not implemented in this demo'),
                    ),
                  );
                },
              ),
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: const Text(
                    '3', // Number of new notifications
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          // My Products Tab
          MyProductsSection(),
          
          // Orders Tab
          OrdersSection(),
          
          // Messages Tab
          MessagesSection(),
        ],
      ),
      floatingActionButton: _tabController.index == 0
          ? FloatingActionButton(
              onPressed: () {
                // Add new product
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Add product functionality not implemented in this demo'),
                  ),
                );
              },
              backgroundColor: AppColors.secondary,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }
}
