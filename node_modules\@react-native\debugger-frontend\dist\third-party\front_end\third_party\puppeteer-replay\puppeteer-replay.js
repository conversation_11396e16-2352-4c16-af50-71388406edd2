var e,t,n;!function(e){e.CSS="css",e.ARIA="aria",e.Text="text",e.XPath="xpath",e.<PERSON>="pierce"}(e||(e={})),function(e){e.Change="change",e.Click="click",e.Close="close",e.CustomStep="customStep",e.DoubleClick="doubleClick",e.EmulateNetworkConditions="emulateNetworkConditions",e.<PERSON><PERSON>="hover",e.KeyDown="keyDown",e.KeyUp="keyUp",e.Navigate="navigate",e.Scroll="scroll",e.SetViewport="setViewport",e.WaitForElement="waitForElement",e.WaitForExpression="waitForExpression"}(t||(t={})),function(e){e.Navigation="navigation"}(n||(n={}));var r=Object.freeze({__proto__:null,get AssertedEventType(){return n},get SelectorType(){return e},get StepType(){return t}});function i(e){throw new Error(`Unknown step type: ${e.type}`)}const a=new Set(["textarea","text","url","tel","search","password","number","email"]),o=new Set(["mouse","pen","touch"]),s=new Map([["primary","left"],["auxiliary","middle"],["secondary","right"],["back","back"],["forward","forward"]]);function p(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))return!1;return void 0!==e[t]}function l(e){return"object"==typeof e&&null!==e}function c(e){return"string"==typeof e}function u(e){return"number"==typeof e}function f(e){return Array.isArray(e)}function d(e){if(p(e,"target")&&c(e.target))return e.target}function w(e){if(p(e,"frame")){if(f(t=e.frame)&&t.every((e=>Number.isInteger(e))))return e.frame;throw new Error("Step `frame` is not an integer array")}var t}function h(e,t){if(p(e,t)){const n=e[t];if(u(n))return n}throw new Error(`Step.${t} is not a number`)}function m(e,t){if(p(e,t)){const n=e[t];if("boolean"==typeof n)return n}throw new Error(`Step.${t} is not a boolean`)}function g(e,t){if(p(e,t))return h(e,t)}function y(e,t){if(p(e,t))return S(e,t)}function b(e,t){if(p(e,t))return m(e,t)}function S(e,t){if(p(e,t)){const n=e[t];if(c(n))return n}throw new Error(`Step.${t} is not a string`)}function E(e){if(!p(e,"selectors"))throw new Error("Step does not have required selectors");if(!f(e.selectors))throw new Error("Step selectors are not an array");if(0===e.selectors.length)throw new Error("Step does not have required selectors");return e.selectors.map((e=>{if(!c(e)&&!f(e))throw new Error("Selector is not an array or string");return f(e)?e.map((e=>{if(!c(e))throw new Error("Selector element is not a string");return e})):e}))}function v(e){if(p(e,"selectors"))return E(e)}function L(e){if(!l(e))throw new Error("Asserted event is not an object");if(!p(e,"type"))throw new Error("Asserted event is missing type");if(e.type===n.Navigation)return{type:n.Navigation,url:y(e,"url"),title:y(e,"title")};throw new Error("Unknown assertedEvent type")}function k(e){if(f(e))return e.map(L)}function F(e,t){if(p(t,"timeout")&&u(t.timeout)&&!j(t.timeout))throw new Error(R);return{type:e,assertedEvents:p(t,"assertedEvents")?k(t.assertedEvents):void 0,timeout:p(t,"timeout")&&u(t.timeout)?t.timeout:void 0}}function A(e,t){return{...F(e,t),target:d(t)}}function x(e,t){return{...A(e,t),frame:w(t)}}function C(e,t){return{...x(e,t),selectors:E(t)}}function T(e){const t={offsetX:h(e,"offsetX"),offsetY:h(e,"offsetY"),duration:g(e,"duration")},n=y(e,"deviceType");if(n){if("string"!=typeof(r=n)||!o.has(r))throw new Error(`'deviceType' for click steps must be one of the following: ${[...o].join(", ")}`);t.deviceType=n}var r;const i=y(e,"button");if(i){if(!function(e){return"string"==typeof e&&s.has(e)}(i))throw new Error(`'button' for click steps must be one of the following: ${[...s.keys()].join(", ")}`);t.button=i}return t}function $(e,n){if(!l(e))throw new Error(n?`Step ${n} is not an object`:"Step is not an object");if(!p(e,"type"))throw new Error(n?`Step ${n} does not have a type`:"Step does not have a type");if(!c(e.type))throw new Error(n?`Type of the step ${n} is not a string`:"Type of the step is not a string");switch(e.type){case t.Click:return function(e){return{...C(t.Click,e),...T(e),type:t.Click}}(e);case t.DoubleClick:return function(e){return{...C(t.DoubleClick,e),...T(e),type:t.DoubleClick}}(e);case t.Hover:return function(e){return{...C(t.Hover,e),type:t.Hover}}(e);case t.Change:return function(e){return{...C(t.Change,e),type:t.Change,value:S(e,"value")}}(e);case t.KeyDown:return function(e){return{...A(t.KeyDown,e),type:t.KeyDown,key:S(e,"key")}}(e);case t.KeyUp:return function(e){return{...A(t.KeyUp,e),type:t.KeyUp,key:S(e,"key")}}(e);case t.EmulateNetworkConditions:return function(e){return{...A(t.EmulateNetworkConditions,e),type:t.EmulateNetworkConditions,download:h(e,"download"),upload:h(e,"upload"),latency:h(e,"latency")}}(e);case t.Close:return function(e){return{...A(t.Close,e),type:t.Close}}(e);case t.SetViewport:return function(e){return{...A(t.SetViewport,e),type:t.SetViewport,width:h(e,"width"),height:h(e,"height"),deviceScaleFactor:h(e,"deviceScaleFactor"),isMobile:m(e,"isMobile"),hasTouch:m(e,"hasTouch"),isLandscape:m(e,"isLandscape")}}(e);case t.Scroll:return function(e){return{...x(t.Scroll,e),type:t.Scroll,x:g(e,"x"),y:g(e,"y"),selectors:v(e)}}(e);case t.Navigate:return function(e){return{...A(t.Navigate,e),type:t.Navigate,target:d(e),url:S(e,"url")}}(e);case t.CustomStep:return function(e){if(!p(e,"name"))throw new Error("customStep is missing name");if(!c(e.name))throw new Error("customStep's name is not a string");return{...x(t.CustomStep,e),type:t.CustomStep,name:e.name,parameters:p(e,"parameters")?e.parameters:void 0}}(e);case t.WaitForElement:return function(e){const n=y(e,"operator");if(n&&">="!==n&&"=="!==n&&"<="!==n)throw new Error("WaitForElement step's operator is not one of '>=','==','<='");if(p(e,"attributes")&&(!l(e.attributes)||Object.values(e.attributes).some((e=>"string"!=typeof e))))throw new Error("WaitForElement step's attribute is not a dictionary of strings");if(p(e,"properties")&&!l(e.properties))throw new Error("WaitForElement step's attribute is not an object");return{...C(t.WaitForElement,e),type:t.WaitForElement,operator:n,count:g(e,"count"),visible:b(e,"visible"),attributes:p(e,"attributes")?e.attributes:void 0,properties:p(e,"properties")?e.properties:void 0}}(e);case t.WaitForExpression:return function(e){if(!p(e,"expression"))throw new Error("waitForExpression step is missing `expression`");return{...x(t.WaitForExpression,e),type:t.WaitForExpression,expression:S(e,"expression")}}(e);default:throw new Error(`Step type ${e.type} is not supported`)}}function N(e){const t=[];if(!f(e))throw new Error("Recording `steps` is not an array");for(const[n,r]of e.entries())t.push($(r,n));return t}const P=1,I=3e4,R=`Timeout is not between ${P} and ${I} milliseconds`;function j(e){return e>=P&&e<=I}function W(e){if(!l(e))throw new Error("Recording is not an object");if(!p(e,"title"))throw new Error("Recording is missing `title`");if(!c(e.title))throw new Error("Recording `title` is not a string");if(p(e,"timeout")&&!u(e.timeout))throw new Error("Recording `timeout` is not a number");if(!p(e,"steps"))throw new Error("Recording is missing `steps`");if(p(e,"timeout")&&u(e.timeout)&&!j(e.timeout))throw new Error(R);return t={title:e.title,timeout:p(e,"timeout")&&u(e.timeout)?e.timeout:void 0,selectorAttribute:p(e,"selectorAttribute")&&c(e.selectorAttribute)?e.selectorAttribute:void 0,steps:N(e.steps)},JSON.parse(JSON.stringify(t));var t}function B(t){for(const n of Object.values(e))if(t.startsWith(`${n}/`))return n;return e.CSS}function O(t){function n(e){return e.replace(/['"()]/g,"\\$&")}Array.isArray(t)||(t=[t]);return t.map((t=>{switch(B(t)){case e.ARIA:return`::-p-aria(${n(t.substring(e.ARIA.length+1))})`;case e.CSS:return t;case e.XPath:return`::-p-xpath(${n(t.substring(e.XPath.length+1))})`;case e.Pierce:return`:scope >>> ${t.substring(e.Pierce.length+1)}`;case e.Text:return`::-p-text(${n(t.substring(e.Text.length+1))})`}})).join(" >>>> ")}class D{async beforeAllSteps(e,t){}async afterAllSteps(e,t){}async beforeEachStep(e,t,n){}async stringifyStep(e,t,n){}async afterEachStep(e,t,n){}}class H extends D{async beforeAllSteps(e,t){const n={...t,steps:void 0},r=JSON.stringify(n,null,e.getIndent()).split("\n");r.pop(),r[r.length-1]+=",",r.push(e.getIndent()+'"steps": ['),e.appendLine(r.join("\n")).startBlock().startBlock()}async afterAllSteps(e){e.endBlock().endBlock().appendLine(e.getIndent()+"]").appendLine("}")}async stringifyStep(e,t,n){const r=JSON.stringify(t,null,e.getIndent());if(!n)return void e.appendLine(r);const i=n.steps.lastIndexOf(t)===n.steps.length-1?"":",";e.appendLine(r+i)}}class K{#e;#t=0;#n=[];constructor(e){this.#e=e}appendLine(e){const t=e.split("\n").map((e=>e?this.#e.repeat(this.#t)+e.trimEnd():""));return this.#n.push(...t),this}startBlock(){return this.#t++,this}endBlock(){if(this.#t--,this.#t<0)throw new Error("Extra endBlock");return this}toString(){return this.#n.join("\n")+"\n"}getIndent(){return this.#e}getSize(){return this.#n.length}}function V(e,t){const n=[];return U(e,n,1,t),n.join("")}function U(e,t=[],n=1,r="  "){switch(typeof e){case"bigint":case"symbol":case"function":case"undefined":throw new Error("Invalid JSON");case"number":case"boolean":t.push(String(e));break;case"string":t.push(X(e));break;case"object":if(null===e)t.push("null");else if(Array.isArray(e)){t.push("[\n");for(let i=0;i<e.length;i++)t.push(r.repeat(n)),U(e[i],t,n+1,r),i!==e.length-1&&t.push(","),t.push("\n");t.push(r.repeat(n-1)+"]")}else{t.push("{\n");const i=Object.keys(e);for(let a=0;a<i.length;a++){const o=i[a],s=e[o];void 0!==s&&(t.push(r.repeat(n)),t.push(o),t.push(": "),U(s,t,n+1,r),a!==i.length-1&&t.push(","),t.push("\n"))}t.push(r.repeat(n-1)+"}")}break;default:throw new Error("Unknown object type")}return t}const q=(e,t)=>e.toString(16).toUpperCase().padStart(t,"0"),M=new Map([["\b","\\b"],["\f","\\f"],["\n","\\n"],["\r","\\r"],["\t","\\t"],["\v","\\v"],["'","\\'"],["\\","\\\\"],["\x3c!--","\\x3C!--"],["<script","\\x3Cscript"],["</script","\\x3C/script"]]),X=e=>{const t=/(\\|<(?:!--|\/?script))|(\p{Control})|(\p{Surrogate})/gu,n=/(\\|'|<(?:!--|\/?script))|(\p{Control})|(\p{Surrogate})/gu,r=(e,t,n,r)=>{if(n){if(M.has(n))return M.get(n);return"\\x"+q(n.charCodeAt(0),2)}if(r){return"\\u"+q(r.charCodeAt(0),4)}return t?M.get(t)||"":e};let i="",a="";return e.includes("'")?e.includes('"')?e.includes("`")||e.includes("${")?(a="'",i=e.replace(n,r)):(a="`",i=e.replace(t,r)):(a='"',i=e.replace(t,r)):(a="'",i=e.replace(t,r)),`${a}${i}${a}`};class J extends D{#r=!1;async beforeAllSteps(e,t){e.appendLine("const puppeteer = require('puppeteer'); // v22.0.0 or later"),e.appendLine(""),e.appendLine("(async () => {").startBlock(),e.appendLine("const browser = await puppeteer.launch();"),e.appendLine("const page = await browser.newPage();"),e.appendLine(`const timeout = ${t.timeout||Y};`),e.appendLine("page.setDefaultTimeout(timeout);"),e.appendLine(""),this.#r=!1}async afterAllSteps(e,t){if(e.appendLine(""),e.appendLine("await browser.close();"),e.appendLine(""),this.#r)for(const t of _.split("\n"))e.appendLine(t);e.endBlock().appendLine("})().catch(err => {").startBlock(),e.appendLine("console.error(err);"),e.appendLine("process.exit(1);"),e.endBlock().appendLine("});")}async stringifyStep(e,t,r){if(e.appendLine("{").startBlock(),void 0!==t.timeout&&e.appendLine(`const timeout = ${t.timeout};`),this.#i(e,t),t.assertedEvents){e.appendLine("const promises = [];"),e.appendLine("const startWaitingForEvents = () => {").startBlock();for(const r of t.assertedEvents){if(r.type!==n.Navigation)throw new Error(`Event type ${r.type} is not supported`);e.appendLine(`promises.push(${"frame"in t&&t.frame?"frame":"targetPage"}.waitForNavigation());`)}e.endBlock().appendLine("}")}this.#a(e,t),t.assertedEvents&&e.appendLine("await Promise.all(promises);"),e.endBlock().appendLine("}")}#o(e,t){"main"===t?e.appendLine("const targetPage = page;"):(e.appendLine(`const target = await browser.waitForTarget(t => t.url() === ${V(t,e.getIndent())}, { timeout });`),e.appendLine("const targetPage = await target.page();"),e.appendLine("targetPage.setDefaultTimeout(timeout);"))}#s(e,t){e.appendLine("let frame = targetPage.mainFrame();");for(const n of t)e.appendLine(`frame = frame.childFrames()[${n}];`)}#i(e,t){this.#o(e,t.target||"main"),t.frame&&this.#s(e,t.frame)}#p(e,t,n){e.appendLine("await puppeteer.Locator.race([").startBlock(),e.appendLine(t.selectors.map((n=>`${t.frame?"frame":"targetPage"}.locator(${V(O(n),e.getIndent())})`)).join(",\n")),e.endBlock().appendLine("])"),e.startBlock().appendLine(".setTimeout(timeout)"),t.assertedEvents?.length&&e.appendLine(".on('action', () => startWaitingForEvents())"),n(),e.endBlock()}#l(e,t){this.#p(e,t,(()=>{e.appendLine(".click({"),t.duration&&e.appendLine(`  delay: ${t.duration},`),t.button&&e.appendLine(`  button: '${s.get(t.button)}',`),e.appendLine("  offset: {"),e.appendLine(`    x: ${t.offsetX},`),e.appendLine(`    y: ${t.offsetY},`),e.appendLine("  },"),e.appendLine("});")}))}#c(e,t){this.#p(e,t,(()=>{e.appendLine(".click({"),e.appendLine("  count: 2,"),t.duration&&e.appendLine(`  delay: ${t.duration},`),t.button&&e.appendLine(`  button: '${s.get(t.button)}',`),e.appendLine("  offset: {"),e.appendLine(`    x: ${t.offsetX},`),e.appendLine(`    y: ${t.offsetY},`),e.appendLine("  },"),e.appendLine("});")}))}#u(e,t){this.#p(e,t,(()=>{e.appendLine(".hover();")}))}#f(e,t){this.#p(e,t,(()=>{e.appendLine(`.fill(${V(t.value,e.getIndent())});`)}))}#d(e,t){e.appendLine("await targetPage.emulateNetworkConditions({"),e.appendLine(`  offline: ${!t.download&&!t.upload},`),e.appendLine(`  downloadThroughput: ${t.download},`),e.appendLine(`  uploadThroughput: ${t.upload},`),e.appendLine(`  latency: ${t.latency},`),e.appendLine("});")}#w(e,t){e.appendLine(`await targetPage.keyboard.down(${V(t.key,e.getIndent())});`)}#h(e,t){e.appendLine(`await targetPage.keyboard.up(${V(t.key,e.getIndent())});`)}#m(e,t){e.appendLine("await targetPage.close()")}#g(e,t){e.appendLine(`await targetPage.setViewport(${V({width:t.width,height:t.height},e.getIndent())})`)}#y(e,t){"selectors"in t?this.#p(e,t,(()=>{e.appendLine(`.scroll({ scrollTop: ${t.y}, scrollLeft: ${t.x}});`)})):e.appendLine(`await targetPage.evaluate((x, y) => { window.scroll(x, y); }, ${t.x}, ${t.y})`)}#a(e,n){switch(n.type){case t.Click:return this.#l(e,n);case t.DoubleClick:return this.#c(e,n);case t.Hover:return this.#u(e,n);case t.Change:return this.#f(e,n);case t.EmulateNetworkConditions:return this.#d(e,n);case t.KeyDown:return this.#w(e,n);case t.KeyUp:return this.#h(e,n);case t.Close:return this.#m(e,n);case t.SetViewport:return this.#g(e,n);case t.Scroll:return this.#y(e,n);case t.Navigate:return this.#b(e,n);case t.WaitForElement:return this.#S(e,n);case t.WaitForExpression:return this.#E(e,n);case t.CustomStep:return;default:return i(n)}}#b(e,t){t.assertedEvents?.length&&e.appendLine("startWaitingForEvents();"),e.appendLine(`await targetPage.goto(${V(t.url,e.getIndent())});`)}#E(e,t){e.appendLine(`await ${t.frame?"frame":"targetPage"}.waitForFunction(${V(t.expression,e.getIndent())}, { timeout });`)}#S(e,t){this.#r=!0,e.appendLine(`await waitForElement(${V(t,e.getIndent())}, ${t.frame?"frame":"targetPage"}, timeout);`)}}const Y=5e3,_="async function waitForElement(step, frame, timeout) {\n  const {\n    count = 1,\n    operator = '>=',\n    visible = true,\n    properties,\n    attributes,\n  } = step;\n  const compFn = {\n    '==': (a, b) => a === b,\n    '>=': (a, b) => a >= b,\n    '<=': (a, b) => a <= b,\n  }[operator];\n  await waitForFunction(async () => {\n    const elements = await querySelectorsAll(step.selectors, frame);\n    let result = compFn(elements.length, count);\n    const elementsHandle = await frame.evaluateHandle((...elements) => {\n      return elements;\n    }, ...elements);\n    await Promise.all(elements.map((element) => element.dispose()));\n    if (result && (properties || attributes)) {\n      result = await elementsHandle.evaluate(\n        (elements, properties, attributes) => {\n          for (const element of elements) {\n            if (attributes) {\n              for (const [name, value] of Object.entries(attributes)) {\n                if (element.getAttribute(name) !== value) {\n                  return false;\n                }\n              }\n            }\n            if (properties) {\n              if (!isDeepMatch(properties, element)) {\n                return false;\n              }\n            }\n          }\n          return true;\n\n          function isDeepMatch(a, b) {\n            if (a === b) {\n              return true;\n            }\n            if ((a && !b) || (!a && b)) {\n              return false;\n            }\n            if (!(a instanceof Object) || !(b instanceof Object)) {\n              return false;\n            }\n            for (const [key, value] of Object.entries(a)) {\n              if (!isDeepMatch(value, b[key])) {\n                return false;\n              }\n            }\n            return true;\n          }\n        },\n        properties,\n        attributes\n      );\n    }\n    await elementsHandle.dispose();\n    return result === visible;\n  }, timeout);\n}\n\nasync function querySelectorsAll(selectors, frame) {\n  for (const selector of selectors) {\n    const result = await querySelectorAll(selector, frame);\n    if (result.length) {\n      return result;\n    }\n  }\n  return [];\n}\n\nasync function querySelectorAll(selector, frame) {\n  if (!Array.isArray(selector)) {\n    selector = [selector];\n  }\n  if (!selector.length) {\n    throw new Error('Empty selector provided to querySelectorAll');\n  }\n  let elements = [];\n  for (let i = 0; i < selector.length; i++) {\n    const part = selector[i];\n    if (i === 0) {\n      elements = await frame.$$(part);\n    } else {\n      const tmpElements = elements;\n      elements = [];\n      for (const el of tmpElements) {\n        elements.push(...(await el.$$(part)));\n      }\n    }\n    if (elements.length === 0) {\n      return [];\n    }\n    if (i < selector.length - 1) {\n      const tmpElements = [];\n      for (const el of elements) {\n        const newEl = (await el.evaluateHandle(el => el.shadowRoot ? el.shadowRoot : el)).asElement();\n        if (newEl) {\n          tmpElements.push(newEl);\n        }\n      }\n      elements = tmpElements;\n    }\n  }\n  return elements;\n}\n\nasync function waitForFunction(fn, timeout) {\n  let isActive = true;\n  const timeoutId = setTimeout(() => {\n    isActive = false;\n  }, timeout);\n  while (isActive) {\n    const result = await fn();\n    if (result) {\n      clearTimeout(timeoutId);\n      return;\n    }\n    await new Promise(resolve => setTimeout(resolve, 100));\n  }\n  throw new Error('Timed out');\n}",z="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",G=z.split("").reduce(((e,t,n)=>(e.set(t,n),e)),new Map),Q=31,Z=32,ee=2147483647;function te(e){if(e<0)throw new Error("Only postive integers and zero are supported");if(e>ee)throw new Error("Only integers between 0 and "+ee+" are supported");const t=[];do{let n=e&Q;(e>>>=5)>0&&(n|=Z),t.push(z[n])}while(0!==e);return t.join("")}function ne(e){const t=[],n=e.split("");let r=0,i=0;for(const e of n){const n=G.get(e);r|=(n&Q)<<i,i+=5;n&Z||(t.push(r),r=0,i=0)}return t}const re="//# recorderSourceMap=";async function ie(e,t){t||(t={});const n=t.extension??new J,r=t.writer??new K(t.indentation??"  ");await(n.beforeAllSteps?.(r,e));const i=[1];for(const t of e.steps){const a=r.getSize();await(n.beforeEachStep?.(r,t,e)),await n.stringifyStep(r,t,e),await(n.afterEachStep?.(r,t,e));const o=r.getSize();i.push(a,o-a)}return await(n.afterAllSteps?.(r,e)),r.appendLine(re+function(e){const t=[];for(const n of e)t.push(te(n));return t.join("")}(i)),r.toString()}async function ae(e,t){t||(t={});let n=t.extension;n||(n=new J),t.indentation||(t.indentation="  ");const r=t.writer??new K(t.indentation??"  ");return await(n.beforeEachStep?.(r,e)),await n.stringifyStep(r,e),await(n.afterEachStep?.(r,e)),r.toString()}function oe(e){return e.trim().startsWith(re)}function se(e){const t=e.split("\n");for(let e=t.length-1;e>=0;e--){const n=t[e];if(oe(n))return ne(n.trim().substring(re.length))}}function pe(e){return e.split("\n").filter((e=>!oe(e))).join("\n")}class le{async beforeAllSteps(e){}async afterAllSteps(e){}async beforeEachStep(e,t){}async runStep(e,t){}async afterEachStep(e,t){}}const ce={"==":(e,t)=>e===t,">=":(e,t)=>e>=t,"<=":(e,t)=>e<=t};function ue(e){return new Promise((t=>{setTimeout(t,e)}))}class fe extends le{browser;page;timeout;constructor(e,t,n){super(),this.browser=e,this.page=t,this.timeout=n?.timeout||5e3}async#v(e){try{await e._client().send("Emulation.setAutomationOverride",{enabled:!0})}catch{}}#L(e,t){return e.timeout||t?.timeout||this.timeout}async runStep(e,t){const n=this.#L(e,t),r=this.page,i=this.browser,a=await async function(e,t,n,r){if(!n.target||"main"===n.target)return t;const i=await e.waitForTarget((e=>e.url()===n.target),{timeout:r}),a=await i.page();if(!a)return null;return a.setDefaultTimeout(r),a}(i,r,e,n);let o=null;if(!a&&e.target){const t=r.frames();for(const n of t)if(n.isOOPFrame()&&n.url()===e.target){o=n;break}o||(o=await r.waitForFrame(e.target,{timeout:n}))}const s=o||a;if(!s)throw new Error("Target is not found for step: "+JSON.stringify(e));await this.#v(s);const p=await async function(e,t){let n="mainFrame"in e?e.mainFrame():e;if("frame"in t&&t.frame)for(const e of t.frame)n=n.childFrames()[e];return n}(s,e);await this.runStepInFrame(e,r,s,p,n)}async runStepInFrame(e,r,a,o,p){let l=null;const c=()=>{l=async function(e,t,r){const i=[];if(t.assertedEvents)for(const a of t.assertedEvents){if(a.type!==n.Navigation)throw new Error(`Event type ${a.type} is not supported`);i.push(e.waitForNavigation({timeout:r}))}await Promise.all(i)}(o,e,p)},u=this.page.locatorRace;switch(e.type){case t.DoubleClick:await u(e.selectors.map((e=>o.locator(O(e))))).setTimeout(p).on("action",(()=>c())).click({count:2,button:e.button&&s.get(e.button),delay:e.duration,offset:{x:e.offsetX,y:e.offsetY}});break;case t.Click:await u(e.selectors.map((e=>o.locator(O(e))))).setTimeout(p).on("action",(()=>c())).click({delay:e.duration,button:e.button&&s.get(e.button),offset:{x:e.offsetX,y:e.offsetY}});break;case t.Hover:await u(e.selectors.map((e=>o.locator(O(e))))).setTimeout(p).on("action",(()=>c())).hover();break;case t.EmulateNetworkConditions:c(),await r.emulateNetworkConditions(e);break;case t.KeyDown:c(),await r.keyboard.down(e.key),await ue(100);break;case t.KeyUp:c(),await r.keyboard.up(e.key),await ue(100);break;case t.Close:"close"in a&&(c(),await a.close());break;case t.Change:await u(e.selectors.map((e=>o.locator(O(e))))).on("action",(()=>c())).setTimeout(p).fill(e.value);break;case t.SetViewport:"setViewport"in a&&(c(),await a.setViewport(e));break;case t.Scroll:"selectors"in e?await u(e.selectors.map((e=>o.locator(O(e))))).on("action",(()=>c())).setTimeout(p).scroll({scrollLeft:e.x||0,scrollTop:e.y||0}):(c(),await o.evaluate(((e,t)=>{window.scroll(e,t)}),e.x||0,e.y||0));break;case t.Navigate:c(),await o.goto(e.url);break;case t.WaitForElement:try{c(),await async function(e,t,n){const{count:r=1,operator:i=">=",visible:a=!0,properties:o,attributes:s}=e,p=ce[i];await async function(e,t){let n=!0;const r=setTimeout((()=>{n=!1}),t);for(;n;){if(await e())return void clearTimeout(r);await new Promise((e=>setTimeout(e,100)))}throw new Error("Timed out")}((async()=>{const n=await async function(e,t){for(const n of e){const e=await we(n,t);if(e.length)return e}return[]}(e.selectors,t);let i=p(n.length,r);const l=await t.evaluateHandle(((...e)=>e),...n);return await Promise.all(n.map((e=>e.dispose()))),i&&(o||s)&&(i=await l.evaluate(((e,t,n)=>{if(n)for(const t of e)for(const[e,r]of Object.entries(n))if(t.getAttribute(e)!==r)return!1;if(t)for(const n of e)if(!r(t,n))return!1;return!0;function r(e,t){if(e===t)return!0;if(e&&!t||!e&&t)return!1;if(!(e instanceof Object&&t instanceof Object))return!1;for(const[n,i]of Object.entries(e))if(!r(i,t[n]))return!1;return!0}}),o,s)),await l.dispose(),i===a}),n)}(e,o,p)}catch(e){throw"Timed out"===e.message?new Error("waitForElement timed out. The element(s) could not be found."):e}break;case t.WaitForExpression:c(),await o.waitForFunction(e.expression,{timeout:p});break;case t.CustomStep:break;default:i(e)}await l}}class de extends fe{async afterAllSteps(){await this.browser.close()}}async function we(e,t){if(Array.isArray(e)||(e=[e]),!e.length)throw new Error("Empty selector provided to querySelectorAll");let n=await t.$$(e[0]);if(!n.length)return[];for(const t of e.slice(1,e.length))if(n=(await Promise.all(n.map((async e=>{const n=await e.evaluateHandle((e=>e.shadowRoot?e.shadowRoot:e)),r=await n.$$(t);return n.dispose(),e.dispose(),r})))).flat(),!n.length)return[];return n}async function he(e,t,n){await(e.beforeEachStep?.(t,n)),await e.runStep(t,n),await(e.afterEachStep?.(t,n))}class me{#k;#F;#A=!1;constructor(e){this.#F=e}abort(){this.#A=!0}set flow(e){this.#k=e}async runBeforeAllSteps(e){await(this.#F.beforeAllSteps?.(e))}async runAfterAllSteps(e){await(this.#F.afterAllSteps?.(e))}async runStep(e){await he(this.#F,e)}async run(){if(!this.#k)throw new Error("Set the flow on the runner instance before calling `run`.");const e=this.#k;if(this.#A=!1,await(this.#F.beforeAllSteps?.(e)),this.#A)return!1;for(const t of e.steps){if(this.#A)return await(this.#F.afterAllSteps?.(e)),!1;await he(this.#F,t,e)}return await(this.#F.afterAllSteps?.(e)),!0}}async function ge(e,t){const n=e instanceof le?void 0:e,r=new me((e instanceof le?e:t)??await async function(){const{default:e}=await import("puppeteer"),t=await e.launch(),n=await t.newPage();return new de(t,n)}());return n&&(r.flow=n),r}class ye extends D{async beforeAllSteps(e){e.appendLine("import url from 'url';"),e.appendLine("import { createRunner } from '@puppeteer/replay';"),e.appendLine(""),e.appendLine("export async function run(extension) {").startBlock(),e.appendLine("const runner = await createRunner(extension);"),e.appendLine(""),e.appendLine("await runner.runBeforeAllSteps();"),e.appendLine("")}async afterAllSteps(e){e.appendLine(""),e.appendLine("await runner.runAfterAllSteps();").endBlock().appendLine("}"),e.appendLine(""),e.appendLine("if (process && import.meta.url === url.pathToFileURL(process.argv[1]).href) {").startBlock().appendLine("run()").endBlock().appendLine("}")}async stringifyStep(e,t){e.appendLine(`await runner.runStep(${V(t,e.getIndent())});`)}}function be(e){return Boolean(e.type===t.Navigate||e.assertedEvents?.some((e=>e.type===n.Navigation)))}function Se(e){for(const n of e.steps)if(n.type===t.SetViewport)return n.isMobile;return!1}class Ee extends J{#x=!1;async beforeAllSteps(e,t){e.appendLine("const fs = require('fs');"),await super.beforeAllSteps(e,t),e.appendLine("const lhApi = await import('lighthouse'); // v10.0.0 or later");e.appendLine(`const flags = ${V({screenEmulation:{disabled:!0}},e.getIndent())}`),Se(t)?e.appendLine("const config = undefined;"):e.appendLine("const config = lhApi.desktopConfig;"),e.appendLine(`const lhFlow = await lhApi.startFlow(page, {name: ${V(t.title,e.getIndent())}, config, flags});`)}async stringifyStep(e,n,r){if(n.type===t.SetViewport)return void await super.stringifyStep(e,n,r);const i=be(n);i?(this.#x&&(e.appendLine("await lhFlow.endTimespan();"),this.#x=!1),e.appendLine("await lhFlow.startNavigation();")):this.#x||(e.appendLine("await lhFlow.startTimespan();"),this.#x=!0),await super.stringifyStep(e,n,r),i&&e.appendLine("await lhFlow.endNavigation();")}async afterAllSteps(e,t){this.#x&&e.appendLine("await lhFlow.endTimespan();"),e.appendLine("const lhFlowReport = await lhFlow.generateReport();"),e.appendLine("fs.writeFileSync(__dirname + '/flow.report.html', lhFlowReport)"),await super.afterAllSteps(e,t)}}class ve extends fe{#C=!1;#T=!1;#$;async createFlowResult(){if(!this.#$)throw new Error("Cannot get flow result before running the flow");return this.#$.createFlowResult()}async beforeAllSteps(e){await(super.beforeAllSteps?.(e));const{startFlow:t,desktopConfig:n}=await import("lighthouse");let r;Se(e)||(r=n),this.#$=await t(this.page,{config:r,flags:{screenEmulation:{disabled:!0}},name:e.title})}async beforeEachStep(e,n){await(super.beforeEachStep?.(e,n)),e.type!==t.SetViewport&&(be(e)?(this.#C&&(await this.#$.endTimespan(),this.#C=!1),await this.#$.startNavigation(),this.#T=!0):this.#C||(await this.#$.startTimespan(),this.#C=!0))}async afterEachStep(e,t){this.#T&&(await this.#$.endNavigation(),this.#T=!1),await(super.afterEachStep?.(e,t))}async afterAllSteps(e){this.#C&&await this.#$.endTimespan(),await(super.afterAllSteps?.(e))}}export{n as AssertedEventType,H as JSONStringifyExtension,ve as LighthouseRunnerExtension,Ee as LighthouseStringifyExtension,ye as PuppeteerReplayStringifyExtension,fe as PuppeteerRunnerExtension,de as PuppeteerRunnerOwningBrowserExtension,J as PuppeteerStringifyExtension,me as Runner,le as RunnerExtension,r as Schema,e as SelectorType,t as StepType,D as StringifyExtension,i as assertAllStepTypesAreHandled,ge as createRunner,X as formatAsJSLiteral,V as formatJSONAsJS,B as getSelectorType,I as maxTimeout,P as minTimeout,s as mouseButtonMap,W as parse,se as parseSourceMap,$ as parseStep,o as pointerDeviceTypes,O as selectorToPElementSelector,ie as stringify,ae as stringifyStep,pe as stripSourceMap,a as typeableInputTypes,j as validTimeout};
