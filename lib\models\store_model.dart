class Store {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String sellerId;
  final List<String> productIds;
  final DateTime createdAt;
  
  Store({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.sellerId,
    this.productIds = const [],
    required this.createdAt,
  });
  
  factory Store.fromJson(Map<String, dynamic> json) {
    return Store(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      sellerId: json['sellerId'],
      productIds: List<String>.from(json['productIds'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'sellerId': sellerId,
      'productIds': productIds,
      'createdAt': createdAt.toIso8601String(),
    };
  }
  
  Store copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? sellerId,
    List<String>? productIds,
    DateTime? createdAt,
  }) {
    return Store(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      sellerId: sellerId ?? this.sellerId,
      productIds: productIds ?? this.productIds,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
