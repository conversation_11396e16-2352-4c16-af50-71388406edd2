import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/livestream_model.dart';
import '../../services/auth_service.dart';
import '../../services/livestream_service.dart';
import '../../services/product_service.dart';
import '../../services/store_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

class SellerLivePage extends StatelessWidget {
  const SellerLivePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Live Streaming'),
        centerTitle: true,
        backgroundColor: AppColors.secondary,
      ),
      body: Consumer4<
        AuthService,
        StoreService,
        LivestreamService,
        ProductService
      >(
        builder: (
          context,
          authService,
          storeService,
          livestreamService,
          productService,
          _,
        ) {
          if (authService.currentUser == null) {
            return const Center(
              child: Text('Please log in to access live streaming'),
            );
          }

          final sellerId = authService.currentUser!.id;
          final storeId = authService.currentUser!.storeId;

          if (storeId == null) {
            return const Center(child: Text('Please create a store first'));
          }

          if (livestreamService.isLoading) {
            return const Center(
              child: CircularProgressIndicator(color: AppColors.secondary),
            );
          }

          final sellerLivestreams = livestreamService.getLivestreamsBySellerId(
            sellerId,
          );
          final activeLivestream =
              sellerLivestreams
                  .where((stream) => stream.status == LiveStreamStatus.live)
                  .toList();

          final hasActiveLivestream = activeLivestream.isNotEmpty;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Live Status Card
                Card(
                  color:
                      hasActiveLivestream
                          ? AppColors.liveIndicator.withAlpha(25) // 0.1 opacity
                          : Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(
                      color:
                          hasActiveLivestream
                              ? AppColors.liveIndicator
                              : Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color:
                                    hasActiveLivestream
                                        ? AppColors.liveIndicator.withAlpha(
                                          51,
                                        ) // 0.2 opacity
                                        : Colors.grey.shade200,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                hasActiveLivestream
                                    ? Icons.videocam
                                    : Icons.videocam_off,
                                color:
                                    hasActiveLivestream
                                        ? AppColors.liveIndicator
                                        : Colors.grey,
                                size: 30,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    hasActiveLivestream
                                        ? 'You are live!'
                                        : 'You are not streaming',
                                    style: AppTextStyles.heading3.copyWith(
                                      color:
                                          hasActiveLivestream
                                              ? AppColors.liveIndicator
                                              : AppColors.textPrimary,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    hasActiveLivestream
                                        ? '${activeLivestream.first.viewerCount} viewers watching'
                                        : 'Start a live stream to connect with customers',
                                    style: AppTextStyles.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        SizedBox(
                          width: double.infinity,
                          child:
                              hasActiveLivestream
                                  ? ElevatedButton(
                                    onPressed:
                                        () => _endLivestream(
                                          context,
                                          activeLivestream.first,
                                          livestreamService,
                                        ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.error,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 16,
                                      ),
                                    ),
                                    child: const Text('End Stream'),
                                  )
                                  : ElevatedButton(
                                    onPressed:
                                        () => _startNewLivestream(
                                          context,
                                          sellerId,
                                          storeId,
                                          storeService,
                                          livestreamService,
                                          productService,
                                        ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.secondary,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 16,
                                      ),
                                    ),
                                    child: const Text('Start New Stream'),
                                  ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 32),

                // Past and Scheduled Livestreams
                Text('Your Livestreams', style: AppTextStyles.heading2),

                const SizedBox(height: 16),

                if (sellerLivestreams.isEmpty) ...[
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 32),
                      child: Column(
                        children: [
                          const Icon(
                            Icons.live_tv,
                            size: 64,
                            color: AppColors.textSecondary,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No livestreams yet',
                            style: AppTextStyles.bodyLarge.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Start your first livestream to connect with customers',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ] else ...[
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: sellerLivestreams.length,
                    itemBuilder: (context, index) {
                      final livestream = sellerLivestreams[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Thumbnail
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.network(
                                  livestream.thumbnailUrl,
                                  width: 100,
                                  height: 60,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              const SizedBox(width: 16),

                              // Details
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      livestream.title,
                                      style: AppTextStyles.bodyLarge.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        _buildStatusBadge(livestream.status),
                                        const SizedBox(width: 8),
                                        Text(
                                          _formatDate(livestream.startTime),
                                          style: AppTextStyles.bodySmall,
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    if (livestream.status ==
                                            LiveStreamStatus.live ||
                                        livestream.status ==
                                            LiveStreamStatus.ended) ...[
                                      Text(
                                        'Viewers: ${livestream.viewerCount}',
                                        style: AppTextStyles.bodySmall,
                                      ),
                                    ],
                                  ],
                                ),
                              ),

                              // Actions
                              if (livestream.status ==
                                  LiveStreamStatus.scheduled)
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  color: AppColors.secondary,
                                  onPressed: () {
                                    // TODO: Edit scheduled livestream
                                  },
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],

                const SizedBox(height: 32),

                // Livestreaming Tips
                Card(
                  color: Colors.grey.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Tips for Successful Livestreaming',
                          style: AppTextStyles.heading3,
                        ),
                        const SizedBox(height: 16),
                        _buildTipItem(
                          icon: Icons.lightbulb,
                          title: 'Good Lighting',
                          description:
                              'Ensure your products are well-lit and clearly visible.',
                        ),
                        const SizedBox(height: 12),
                        _buildTipItem(
                          icon: Icons.mic,
                          title: 'Clear Audio',
                          description:
                              'Use a good microphone and speak clearly.',
                        ),
                        const SizedBox(height: 12),
                        _buildTipItem(
                          icon: Icons.people,
                          title: 'Engage with Viewers',
                          description:
                              'Respond to comments and questions in real-time.',
                        ),
                        const SizedBox(height: 12),
                        _buildTipItem(
                          icon: Icons.shopping_bag,
                          title: 'Showcase Products',
                          description:
                              'Highlight key features and demonstrate how to use them.',
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusBadge(LiveStreamStatus status) {
    Color color;
    String text;

    switch (status) {
      case LiveStreamStatus.scheduled:
        color = AppColors.info;
        text = 'Scheduled';
        break;
      case LiveStreamStatus.live:
        color = AppColors.liveIndicator;
        text = 'Live';
        break;
      case LiveStreamStatus.ended:
        color = AppColors.textSecondary;
        text = 'Ended';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withAlpha(25), // 0.1 opacity
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildTipItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.secondary.withAlpha(25), // 0.1 opacity
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: AppColors.secondary, size: 16),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 2),
              Text(description, style: AppTextStyles.bodySmall),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _startNewLivestream(
    BuildContext context,
    String sellerId,
    String storeId,
    StoreService storeService,
    LivestreamService livestreamService,
    ProductService productService,
  ) async {
    final store = storeService.getStoreById(storeId);
    if (store == null) return;

    final sellerProducts = productService.getProductsBySellerId(sellerId);

    // Show dialog to set up livestream
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Start New Livestream'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Livestream Title',
                      hintText: 'Enter a title for your livestream',
                    ),
                    maxLength: 50,
                    controller: TextEditingController(
                      text: 'Live from ${store.name}',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      hintText: 'Describe what you\'ll be showcasing',
                    ),
                    maxLines: 3,
                    controller: TextEditingController(
                      text:
                          'Join us for a live showcase of our latest products!',
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Featured Products:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  if (sellerProducts.isEmpty)
                    const Text('You don\'t have any products to showcase yet.')
                  else
                    const Text(
                      'In a real app, you would select products to feature here.',
                    ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context, {
                    'title': 'Live from ${store.name}',
                    'description':
                        'Join us for a live showcase of our latest products!',
                    'featuredProductIds':
                        sellerProducts.isNotEmpty
                            ? [sellerProducts.first.id]
                            : <String>[],
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondary,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Start Streaming'),
              ),
            ],
          ),
    );

    if (result != null && context.mounted) {
      // Create new livestream
      final newLivestream = await livestreamService.createLivestream(
        title: result['title'],
        description: result['description'],
        thumbnailUrl:
            'https://images.unsplash.com/photo-1577968897966-3d4325b36b61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        sellerId: sellerId,
        storeId: storeId,
        storeName: store.name,
        storeImageUrl: store.imageUrl,
        startTime: DateTime.now(),
        featuredProductIds: result['featuredProductIds'],
      );

      // Start the livestream
      await livestreamService.startLivestream(newLivestream.id);

      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Your livestream has started!'),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  Future<void> _endLivestream(
    BuildContext context,
    LiveStream livestream,
    LivestreamService livestreamService,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('End Livestream'),
            content: const Text(
              'Are you sure you want to end your current livestream?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                ),
                child: const Text('End Stream'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      if (!context.mounted) return;

      await livestreamService.endLivestream(livestream.id);

      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Your livestream has ended'),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }
}
