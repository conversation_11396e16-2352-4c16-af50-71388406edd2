import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/auth_service.dart';
import '../../services/store_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/verification_badge.dart';
import '../../widgets/wallet_card.dart';
import '../../widgets/welcome_notification.dart';
import '../auth/user_type_selection.dart';

class SellerAccountPage extends StatefulWidget {
  const SellerAccountPage({super.key});

  @override
  State<SellerAccountPage> createState() => _SellerAccountPageState();
}

class _SellerAccountPageState extends State<SellerAccountPage> {
  bool _showWelcomeNotification = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Seller Account'),
        centerTitle: true,
        backgroundColor: AppColors.secondary,
        actions: [
          // Notification icon with badge
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () {
                  // Handle notifications
                },
              ),
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: const Text(
                    '3', // Number of new orders
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer2<AuthService, StoreService>(
        builder: (context, authService, storeService, _) {
          if (authService.currentUser == null) {
            return const Center(
              child: Text('Please log in to view your account'),
            );
          }

          final user = authService.currentUser!;
          final store =
              user.storeId != null
                  ? storeService.getStoreById(user.storeId!)
                  : null;

          return Stack(
            children: [
              SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Header
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 40,
                          backgroundImage: NetworkImage(user.profileImage),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(user.name, style: AppTextStyles.heading2),
                              const SizedBox(height: 4),
                              Text(
                                user.email,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.secondary.withAlpha(
                                    25,
                                  ), // 0.1 opacity
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'Seller',
                                  style: TextStyle(
                                    color: AppColors.secondary,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () {},
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Store Information
                    if (store != null) ...[
                      _buildSectionCard(
                        title: 'Store Information',
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    store.imageUrl,
                                    width: 60,
                                    height: 60,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              store.name,
                                              style: AppTextStyles.bodyLarge
                                                  .copyWith(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          const VerificationBadge(),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Created on: ${_formatDate(store.createdAt)}',
                                        style: AppTextStyles.bodySmall,
                                      ),
                                    ],
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  color: AppColors.secondary,
                                  onPressed: () {},
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Description:',
                              style: AppTextStyles.bodyMedium.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              store.description,
                              style: AppTextStyles.bodyMedium,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                _buildStatCard(
                                  title: 'Products',
                                  value: store.productIds.length.toString(),
                                  icon: Icons.inventory,
                                ),
                                _buildStatCard(
                                  title: 'Sales',
                                  value:
                                      '0', // This would come from a real backend
                                  icon: Icons.attach_money,
                                ),
                                _buildStatCard(
                                  title: 'Rating',
                                  value:
                                      '0.0', // This would come from a real backend
                                  icon: Icons.star,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),
                    ],

                    // Wallet
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: WalletCard(
                        balance: user.walletBalance,
                        primaryColor: AppColors.secondary,
                        isCustomer: false,
                        onWithdraw: () {},
                        onTransactions: () {},
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Account Settings
                    _buildSectionCard(
                      title: 'Account Settings',
                      child: Column(
                        children: [
                          _buildSettingsItem(
                            icon: Icons.person,
                            title: 'Personal Information',
                            onTap: () {},
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.payment,
                            title: 'Payment Methods',
                            onTap: () {},
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.notifications,
                            title: 'Notifications',
                            onTap: () {
                              // TODO: Navigate to notifications
                            },
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.settings,
                            title: 'Store Settings',
                            onTap: () {
                              // TODO: Navigate to store settings
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Support
                    _buildSectionCard(
                      title: 'Support',
                      child: Column(
                        children: [
                          _buildSettingsItem(
                            icon: Icons.help,
                            title: 'Seller Help Center',
                            onTap: () {
                              // TODO: Navigate to help center
                            },
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.chat,
                            title: 'Contact Support',
                            onTap: () {
                              // TODO: Navigate to contact support
                            },
                          ),
                          const Divider(),
                          _buildSettingsItem(
                            icon: Icons.info,
                            title: 'About HadiHia',
                            onTap: () {
                              // TODO: Navigate to about
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Logout Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _handleLogout(context, authService),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppColors.error,
                          side: const BorderSide(color: AppColors.error),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text('Logout'),
                      ),
                    ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),

              // Welcome notification
              if (_showWelcomeNotification)
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: WelcomeNotification(
                    message: 'Welcome back to HadiHia, Seller!',
                    backgroundColor: AppColors.secondary,
                    onDismiss: () {
                      setState(() {
                        _showWelcomeNotification = false;
                      });
                    },
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionCard({required String title, required Widget child}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: AppTextStyles.heading3),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Container(
      width: 90,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.secondary.withAlpha(25), // 0.1 opacity
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppColors.secondary, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: AppTextStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, color: AppColors.secondary, size: 20),
            const SizedBox(width: 16),
            Expanded(child: Text(title, style: AppTextStyles.bodyLarge)),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textSecondary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _handleLogout(
    BuildContext context,
    AuthService authService,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Logout'),
            content: const Text('Are you sure you want to logout?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Logout'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      if (!context.mounted) return;

      await authService.logout();

      if (!context.mounted) return;

      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const UserTypeSelectionPage()),
        (route) => false,
      );
    }
  }
}
