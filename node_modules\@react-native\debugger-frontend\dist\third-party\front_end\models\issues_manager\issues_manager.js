import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as s from"../../core/i18n/i18n.js";import*as i from"../../core/sdk/sdk.js";import*as r from"../../third_party/marked/marked.js";import*as o from"../../core/root/root.js";import*as n from"../bindings/bindings.js";import*as a from"../workspace/workspace.js";const u={improvements:"Improvements",pageErrors:"Page Errors",breakingChanges:"Breaking Changes",pageErrorIssue:"A page error issue: the page is not working correctly",breakingChangeIssue:"A breaking change issue: the page may stop working in an upcoming version of Chrome",improvementIssue:"An improvement issue: there is an opportunity to improve the page"},d=s.i18n.registerUIStrings("models/issues_manager/Issue.ts",u),l=s.i18n.getLocalizedString.bind(void 0,d);class c{#e;#t;issueId=void 0;#s;constructor(e,s=null,i){this.#e="object"==typeof e?e.code:e,this.#t=s,this.issueId=i,t.userMetrics.issueCreated("string"==typeof e?e:e.umaCode),this.#s=!1}code(){return this.#e}getBlockedByResponseDetails(){return[]}cookies(){return[]}rawCookieLines(){return[]}elements(){return[]}requests(){return[]}sources(){return[]}trackingSites(){return[]}isAssociatedWithRequestId(e){for(const t of this.requests())if(t.requestId===e)return!0;return!1}model(){return this.#t}isCausedByThirdParty(){return!1}getIssueId(){return this.issueId}isHidden(){return this.#s}setHidden(e){this.#s=e}maybeCreateConsoleMessage(){}}function g(e){if(e)return{url:e.url,scriptId:e.scriptId,lineNumber:e.lineNumber,columnNumber:0===e.columnNumber?void 0:e.columnNumber-1}}var h=Object.freeze({__proto__:null,getIssueKindName:function(e){switch(e){case"BreakingChange":return l(u.breakingChanges);case"Improvement":return l(u.improvements);case"PageError":return l(u.pageErrors)}},getIssueKindDescription:function(e){switch(e){case"PageError":return l(u.pageErrorIssue);case"BreakingChange":return l(u.breakingChangeIssue);case"Improvement":return l(u.improvementIssue)}},unionIssueKind:function(e,t){return"PageError"===e||"PageError"===t?"PageError":"BreakingChange"===e||"BreakingChange"===t?"BreakingChange":"Improvement"},getShowThirdPartyIssuesSetting:function(){return e.Settings.Settings.instance().createSetting("show-third-party-issues",!0)},Issue:c,toZeroBasedLocation:g});const m={link:"https://tools.ietf.org/id/draft-ietf-httpbis-header-structure-15.html#rfc.section.4.2.2",linkTitle:"Structured Headers RFC"};class p extends c{issueDetails;constructor(e,t){super(function(e){switch(e.violationType){case"PermissionPolicyDisabled":return"AttributionReportingIssue::PermissionPolicyDisabled";case"UntrustworthyReportingOrigin":return"AttributionReportingIssue::UntrustworthyReportingOrigin";case"InsecureContext":return"AttributionReportingIssue::InsecureContext";case"InvalidHeader":return"AttributionReportingIssue::InvalidRegisterSourceHeader";case"InvalidRegisterTriggerHeader":return"AttributionReportingIssue::InvalidRegisterTriggerHeader";case"SourceAndTriggerHeaders":return"AttributionReportingIssue::SourceAndTriggerHeaders";case"SourceIgnored":return"AttributionReportingIssue::SourceIgnored";case"TriggerIgnored":return"AttributionReportingIssue::TriggerIgnored";case"OsSourceIgnored":return"AttributionReportingIssue::OsSourceIgnored";case"OsTriggerIgnored":return"AttributionReportingIssue::OsTriggerIgnored";case"InvalidRegisterOsSourceHeader":return"AttributionReportingIssue::InvalidRegisterOsSourceHeader";case"InvalidRegisterOsTriggerHeader":return"AttributionReportingIssue::InvalidRegisterOsTriggerHeader";case"WebAndOsHeaders":return"AttributionReportingIssue::WebAndOsHeaders";case"NavigationRegistrationWithoutTransientUserActivation":return"AttributionReportingIssue::NavigationRegistrationWithoutTransientUserActivation";default:return"AttributionReportingIssue::Unknown"}}(e),t),this.issueDetails=e}getCategory(){return"AttributionReporting"}getHeaderValidatorLink(e){const t=new URL("https://wicg.github.io/attribution-reporting-api/validate-headers");return t.searchParams.set("header",e),this.issueDetails.invalidParameter&&t.searchParams.set("json",this.issueDetails.invalidParameter),{link:t.toString(),linkTitle:"Header Validator"}}getDescription(){switch(this.code()){case"AttributionReportingIssue::PermissionPolicyDisabled":return{file:"arPermissionPolicyDisabled.md",links:[]};case"AttributionReportingIssue::UntrustworthyReportingOrigin":return{file:"arUntrustworthyReportingOrigin.md",links:[]};case"AttributionReportingIssue::InsecureContext":return{file:"arInsecureContext.md",links:[]};case"AttributionReportingIssue::InvalidRegisterSourceHeader":return{file:"arInvalidRegisterSourceHeader.md",links:[this.getHeaderValidatorLink("source")]};case"AttributionReportingIssue::InvalidRegisterTriggerHeader":return{file:"arInvalidRegisterTriggerHeader.md",links:[this.getHeaderValidatorLink("trigger")]};case"AttributionReportingIssue::InvalidRegisterOsSourceHeader":return{file:"arInvalidRegisterOsSourceHeader.md",links:[this.getHeaderValidatorLink("os-source")]};case"AttributionReportingIssue::InvalidRegisterOsTriggerHeader":return{file:"arInvalidRegisterOsTriggerHeader.md",links:[this.getHeaderValidatorLink("os-trigger")]};case"AttributionReportingIssue::SourceAndTriggerHeaders":return{file:"arSourceAndTriggerHeaders.md",links:[]};case"AttributionReportingIssue::WebAndOsHeaders":return{file:"arWebAndOsHeaders.md",links:[]};case"AttributionReportingIssue::SourceIgnored":return{file:"arSourceIgnored.md",links:[m]};case"AttributionReportingIssue::TriggerIgnored":return{file:"arTriggerIgnored.md",links:[m]};case"AttributionReportingIssue::OsSourceIgnored":return{file:"arOsSourceIgnored.md",links:[m]};case"AttributionReportingIssue::OsTriggerIgnored":return{file:"arOsTriggerIgnored.md",links:[m]};case"AttributionReportingIssue::NavigationRegistrationWithoutTransientUserActivation":return{file:"arNavigationRegistrationWithoutTransientUserActivation.md",links:[]};case"AttributionReportingIssue::Unknown":return null}}primaryKey(){return JSON.stringify(this.issueDetails)}getKind(){switch(this.code()){case"AttributionReportingIssue::PermissionPolicyDisabled":case"AttributionReportingIssue::UntrustworthyReportingOrigin":case"AttributionReportingIssue::InsecureContext":case"AttributionReportingIssue::InvalidRegisterSourceHeader":case"AttributionReportingIssue::InvalidRegisterTriggerHeader":case"AttributionReportingIssue::InvalidRegisterOsSourceHeader":case"AttributionReportingIssue::InvalidRegisterOsTriggerHeader":case"AttributionReportingIssue::SourceAndTriggerHeaders":case"AttributionReportingIssue::WebAndOsHeaders":case"AttributionReportingIssue::SourceIgnored":case"AttributionReportingIssue::TriggerIgnored":case"AttributionReportingIssue::OsSourceIgnored":case"AttributionReportingIssue::OsTriggerIgnored":case"AttributionReportingIssue::NavigationRegistrationWithoutTransientUserActivation":case"AttributionReportingIssue::Unknown":return"PageError"}}static fromInspectorIssue(e,t){const{attributionReportingIssueDetails:s}=t.details;return s?[new p(s,e)]:(console.warn("Attribution Reporting issue without details received."),[])}}var I=Object.freeze({__proto__:null,AttributionReportingIssue:p});let f=null;class k{constructor(){i.TargetManager.TargetManager.instance().addModelListener(i.ResourceTreeModel.ResourceTreeModel,i.ResourceTreeModel.Events.Load,this.#i,this,{scoped:!0});for(const e of i.TargetManager.TargetManager.instance().models(i.ResourceTreeModel.ResourceTreeModel))e.target().outermostTarget()===e.target()&&this.#r(e)}static instance({forceNew:e}={forceNew:!1}){return f&&!e||(f=new k),f}#r(e){e.target().auditsAgent().invoke_checkFormsIssues()}#i(e){const{resourceTreeModel:t}=e.data;this.#r(t)}}var C=Object.freeze({__proto__:null,CheckFormsIssuesTrigger:k});function S(e){const t=new Map;e.substitutions?.forEach(((e,s)=>{t.set(s,e())}));return{file:e.file,links:e.links.map((function(e){return{link:e.link,linkTitle:e.linkTitle()}})),substitutions:t}}async function y(e){try{return(await fetch(e.toString())).text()}catch(t){throw new Error(`Markdown file ${e.toString()} not found. Make sure it is correctly listed in the relevant BUILD.gn files.`)}}async function v(e){return y(new URL(`descriptions/${e}`,import.meta.url))}function b(e,t){const s=r.Marked.lexer(e),i=R(s);if(!i)throw new Error("Markdown issue descriptions must start with a heading");return{title:i,markdown:s.slice(1),links:t.links}}const w=/\{(PLACEHOLDER_[a-zA-Z][a-zA-Z0-9]*)\}/g,D=/PLACEHOLDER_[a-zA-Z][a-zA-Z0-9]*/;function T(e,t){const s=new Set(t?t.keys():[]);!function(e){const t=[...e].filter((e=>!D.test(e)));if(t.length>0)throw new Error(`Invalid placeholders provided in the substitutions map: ${t}`)}(s);const i=e.replace(w,((e,i)=>{const r=t?t.get(i):void 0;if(void 0===r)throw new Error(`No replacement provided for placeholder '${i}'.`);return s.delete(i),r}));if(s.size>0)throw new Error(`Unused replacements provided: ${[...s]}`);return i}function R(e){return 0===e.length||"heading"!==e[0].type||1!==e[0].depth?null:e[0].text}async function A(e){const t=await v(e.file);return R(r.Marked.lexer(t))}var E=Object.freeze({__proto__:null,resolveLazyDescription:S,getFileContent:y,getMarkdownFileContent:v,createIssueDescriptionFromMarkdown:async function(e){return b(T(await v(e.file),e.substitutions),e)},createIssueDescriptionFromRawMarkdown:b,substitutePlaceholders:T,findTitleFromMarkdownAst:R,getIssueTitleFromMarkdownDescription:A});const P={clientHintsInfrastructure:"Client Hints Infrastructure"},M=s.i18n.registerUIStrings("models/issues_manager/ClientHintIssue.ts",P),F=s.i18n.getLazilyComputedLocalizedString.bind(void 0,M);class N extends c{issueDetails;constructor(e,t){super({code:"ClientHintIssue",umaCode:["ClientHintIssue",e.clientHintIssueReason].join("::")},t),this.issueDetails=e}getCategory(){return"Other"}details(){return this.issueDetails}getDescription(){const e=x.get(this.issueDetails.clientHintIssueReason);return e?S(e):null}sources(){return[this.issueDetails.sourceCodeLocation]}primaryKey(){return JSON.stringify(this.issueDetails)}getKind(){return"BreakingChange"}static fromInspectorIssue(e,t){const s=t.details.clientHintIssueDetails;return s?[new N(s,e)]:(console.warn("Client Hint issue without details received."),[])}}const x=new Map([["MetaTagAllowListInvalidOrigin",{file:"clientHintMetaTagAllowListInvalidOrigin.md",links:[{link:"https://wicg.github.io/client-hints-infrastructure/",linkTitle:F(P.clientHintsInfrastructure)}]}],["MetaTagModifiedHTML",{file:"clientHintMetaTagModifiedHTML.md",links:[{link:"https://wicg.github.io/client-hints-infrastructure/",linkTitle:F(P.clientHintsInfrastructure)}]}]]);var L=Object.freeze({__proto__:null,ClientHintIssue:N});const O={contentSecurityPolicySource:"Content Security Policy - Source Allowlists",contentSecurityPolicyInlineCode:"Content Security Policy - Inline Code",contentSecurityPolicyEval:"Content Security Policy - Eval",trustedTypesFixViolations:"Trusted Types - Fix violations",trustedTypesPolicyViolation:"Trusted Types - Policy violation"},W=s.i18n.registerUIStrings("models/issues_manager/ContentSecurityPolicyIssue.ts",O),H=s.i18n.getLazilyComputedLocalizedString.bind(void 0,W);class U extends c{#o;constructor(e,t,s){super(["ContentSecurityPolicyIssue",e.contentSecurityPolicyViolationType].join("::"),t,s),this.#o=e}getCategory(){return"ContentSecurityPolicy"}primaryKey(){return JSON.stringify(this.#o,["blockedURL","contentSecurityPolicyViolationType","violatedDirective","isReportOnly","sourceCodeLocation","url","lineNumber","columnNumber","violatingNodeId"])}getDescription(){const e=Q.get(this.#o.contentSecurityPolicyViolationType);return e?S(e):null}details(){return this.#o}getKind(){return this.#o.isReportOnly?"Improvement":"PageError"}static fromInspectorIssue(e,t){const s=t.details.contentSecurityPolicyIssueDetails;return s?[new U(s,e,t.issueId)]:(console.warn("Content security policy issue without details received."),[])}}const q={file:"cspURLViolation.md",links:[{link:"https://developers.google.com/web/fundamentals/security/csp#source_allowlists",linkTitle:H(O.contentSecurityPolicySource)}]},_={file:"cspInlineViolation.md",links:[{link:"https://developers.google.com/web/fundamentals/security/csp#inline_code_is_considered_harmful",linkTitle:H(O.contentSecurityPolicyInlineCode)}]},B={file:"cspEvalViolation.md",links:[{link:"https://developers.google.com/web/fundamentals/security/csp#eval_too",linkTitle:H(O.contentSecurityPolicyEval)}]},z={file:"cspTrustedTypesSinkViolation.md",links:[{link:"https://web.dev/trusted-types/#fix-the-violations",linkTitle:H(O.trustedTypesFixViolations)}]},V={file:"cspTrustedTypesPolicyViolation.md",links:[{link:"https://web.dev/trusted-types/",linkTitle:H(O.trustedTypesPolicyViolation)}]},j=["ContentSecurityPolicyIssue","kURLViolation"].join("::"),K=["ContentSecurityPolicyIssue","kInlineViolation"].join("::"),$=["ContentSecurityPolicyIssue","kEvalViolation"].join("::"),G=["ContentSecurityPolicyIssue","kTrustedTypesSinkViolation"].join("::"),J=["ContentSecurityPolicyIssue","kTrustedTypesPolicyViolation"].join("::"),Q=new Map([["kURLViolation",q],["kInlineViolation",_],["kEvalViolation",B],["kTrustedTypesSinkViolation",z],["kTrustedTypesPolicyViolation",V]]);var Y=Object.freeze({__proto__:null,ContentSecurityPolicyIssue:U,urlViolationCode:j,inlineViolationCode:K,evalViolationCode:$,trustedTypesSinkViolationCode:G,trustedTypesPolicyViolationCode:J});let X=null;class Z{#n=new WeakMap;#a=new WeakMap;constructor(){i.TargetManager.TargetManager.instance().observeModels(i.ResourceTreeModel.ResourceTreeModel,this)}static instance({forceNew:e}={forceNew:!1}){return X&&!e||(X=new Z),X}async modelAdded(e){this.#n.set(e,e.addEventListener(i.ResourceTreeModel.Events.Load,this.#i,this)),this.#a.set(e,e.addEventListener(i.ResourceTreeModel.Events.FrameAdded,this.#u,this))}modelRemoved(t){const s=this.#n.get(t);s&&e.EventTarget.removeEventListeners([s]);const i=this.#a.get(t);i&&e.EventTarget.removeEventListeners([i])}#d(e){o.Runtime.experiments.isEnabled("contrast-issues")&&e.target().auditsAgent().invoke_checkContrast({})}#i(e){const{resourceTreeModel:t}=e.data;this.#d(t)}async#u(e){if(!o.Runtime.experiments.isEnabled("contrast-issues"))return;const t=e.data;if(!t.isMainFrame())return;const s=await t.resourceTreeModel().target().runtimeAgent().invoke_evaluate({expression:"document.readyState",returnByValue:!0});s.result&&"complete"===s.result.value&&this.#d(t.resourceTreeModel())}}var ee=Object.freeze({__proto__:null,ContrastCheckTrigger:Z});const te={thirdPartyPhaseoutExplained:"Prepare for phasing out third-party cookies"},se=s.i18n.registerUIStrings("models/issues_manager/CookieDeprecationMetadataIssue.ts",te),ie=s.i18n.getLocalizedString.bind(void 0,se);class re extends c{#o;constructor(e,t){super("CookieDeprecationMetadataIssue_"+e.operation,t),this.#o=e}getCategory(){return"Other"}getDescription(){const e="SetCookie"===this.#o.operation?"cookieWarnMetadataGrantSet.md":"cookieWarnMetadataGrantRead.md";let t="";return this.#o.isOptOutTopLevel&&(t="\n\n (Top level site opt-out: "+this.#o.optOutPercentage+"% - [learn more](gracePeriodStagedControlExplainer))"),{file:e,substitutions:new Map([["PLACEHOLDER_topleveloptout",t]]),links:[{link:"https://developer.chrome.com/docs/privacy-sandbox/third-party-cookie-phase-out/",linkTitle:ie(te.thirdPartyPhaseoutExplained)}]}}details(){return this.#o}getKind(){return"BreakingChange"}primaryKey(){return JSON.stringify(this.#o)}static fromInspectorIssue(e,t){const s=t.details.cookieDeprecationMetadataIssueDetails;return s?[new re(s,e)]:(console.warn("Cookie deprecation metadata issue without details received."),[])}}var oe=Object.freeze({__proto__:null,CookieDeprecationMetadataIssue:re});const ne={samesiteCookiesExplained:"SameSite cookies explained",howSchemefulSamesiteWorks:"How Schemeful Same-Site Works",aSecure:"a secure",anInsecure:"an insecure",firstPartySetsExplained:"`First-Party Sets` and the `SameParty` attribute",thirdPartyPhaseoutExplained:"Prepare for phasing out third-party cookies",fileCrosSiteRedirectBug:"File a bug",consoleTpcdWarningMessage:"Third-party cookie will be blocked in future Chrome versions as part of Privacy Sandbox.",consoleTpcdErrorMessage:"Third-party cookie is blocked in Chrome as part of Privacy Sandbox."},ae=s.i18n.registerUIStrings("models/issues_manager/CookieIssue.ts",ne),ue=s.i18n.getLazilyComputedLocalizedString.bind(void 0,ae);class de extends c{#o;constructor(e,t,s,i){super(e,s,i),this.#o=t}#l(){if(this.#o.cookie){const{domain:e,path:t,name:s}=this.#o.cookie;return`${e};${t};${s}`}return this.#o.rawCookieLine??"no-cookie-info"}primaryKey(){const e=this.#o.request?this.#o.request.requestId:"no-request";return`${this.code()}-(${this.#l()})-(${e})`}static createIssuesFromCookieIssueDetails(e,t,s){const i=[];if(e.cookieExclusionReasons&&e.cookieExclusionReasons.length>0){for(const r of e.cookieExclusionReasons){const o=de.codeForCookieIssueDetails(r,e.cookieWarningReasons,e.operation,e.cookieUrl);o&&i.push(new de(o,e,t,s))}return i}if(e.cookieWarningReasons)for(const r of e.cookieWarningReasons){const o=de.codeForCookieIssueDetails(r,[],e.operation,e.cookieUrl);o&&i.push(new de(o,e,t,s))}return i}static codeForCookieIssueDetails(t,s,i,r){const o=r&&(e.ParsedURL.schemeIs(r,"https:")||e.ParsedURL.schemeIs(r,"wss:"))?"Secure":"Insecure";if("ExcludeSameSiteStrict"===t||"ExcludeSameSiteLax"===t||"ExcludeSameSiteUnspecifiedTreatedAsLax"===t){if(s&&s.length>0){if(s.includes("WarnSameSiteStrictLaxDowngradeStrict"))return["CookieIssue","ExcludeNavigationContextDowngrade",o].join("::");if(s.includes("WarnSameSiteStrictCrossDowngradeStrict")||s.includes("WarnSameSiteStrictCrossDowngradeLax")||s.includes("WarnSameSiteLaxCrossDowngradeStrict")||s.includes("WarnSameSiteLaxCrossDowngradeLax"))return["CookieIssue","ExcludeContextDowngrade",i,o].join("::")}return s.includes("WarnCrossSiteRedirectDowngradeChangesInclusion")?["CookieIssue","CrossSiteRedirectDowngradeChangesInclusion"].join("::"):"ExcludeSameSiteUnspecifiedTreatedAsLax"===t?["CookieIssue",t,i].join("::"):null}return"WarnSameSiteStrictLaxDowngradeStrict"===t?["CookieIssue",t,o].join("::"):"WarnSameSiteStrictCrossDowngradeStrict"===t||"WarnSameSiteStrictCrossDowngradeLax"===t||"WarnSameSiteLaxCrossDowngradeLax"===t||"WarnSameSiteLaxCrossDowngradeStrict"===t?["CookieIssue","WarnCrossDowngrade",i,o].join("::"):["CookieIssue",t,i].join("::")}cookies(){return this.#o.cookie?[this.#o.cookie]:[]}rawCookieLines(){return this.#o.rawCookieLine?[this.#o.rawCookieLine]:[]}requests(){return this.#o.request?[this.#o.request]:[]}getCategory(){return"Cookie"}getDescription(){const e=Oe.get(this.code());return e?S(e):null}isCausedByThirdParty(){return le(i.FrameManager.FrameManager.instance().getOutermostFrame(),this.#o.cookieUrl,this.#o.siteForCookies)}getKind(){return this.#o.cookieExclusionReasons?.length>0?"PageError":"BreakingChange"}static fromInspectorIssue(e,t){const s=t.details.cookieIssueDetails;return s?de.createIssuesFromCookieIssueDetails(s,e,t.issueId):(console.warn("Cookie issue without details received."),[])}static getSubCategory(e){return e.includes("SameSite")||e.includes("Downgrade")?"SameSiteCookie":e.includes("ThirdPartyPhaseout")?"ThirdPartyPhaseoutCookie":"GenericCookie"}maybeCreateConsoleMessage(){const t=this.model();if(t&&"ThirdPartyPhaseoutCookie"===de.getSubCategory(this.code()))return new i.ConsoleModel.ConsoleMessage(t.target().model(i.RuntimeModel.RuntimeModel),e.Console.FrontendMessageSource.IssuePanel,"warning","PageError"===this.getKind()?ne.consoleTpcdErrorMessage:ne.consoleTpcdWarningMessage,{url:this.#o.request?.url,affectedResources:{requestId:this.#o.request?.requestId,issueId:this.issueId}})}}function le(t,s,i){if(!t)return!0;if(!i)return!0;if(!s||""===t.domainAndRegistry())return!1;const r=e.ParsedURL.ParsedURL.fromString(s);return!!r&&!function(e,t){if(e.length<=t.length)return e===t;if(!e.endsWith(t))return!1;return e.substr(0,e.length-t.length).endsWith(".")}(r.domain(),t.domainAndRegistry())}const ce={file:"SameSiteUnspecifiedLaxAllowUnsafeRead.md",links:[{link:"https://web.dev/samesite-cookies-explained/",linkTitle:ue(ne.samesiteCookiesExplained)}]},ge={file:"SameSiteUnspecifiedLaxAllowUnsafeSet.md",links:[{link:"https://web.dev/samesite-cookies-explained/",linkTitle:ue(ne.samesiteCookiesExplained)}]},he={file:"SameSiteNoneInsecureErrorRead.md",links:[{link:"https://web.dev/samesite-cookies-explained/",linkTitle:ue(ne.samesiteCookiesExplained)}]},me={file:"SameSiteNoneInsecureErrorSet.md",links:[{link:"https://web.dev/samesite-cookies-explained/",linkTitle:ue(ne.samesiteCookiesExplained)}]},pe={file:"SameSiteNoneInsecureWarnRead.md",links:[{link:"https://web.dev/samesite-cookies-explained/",linkTitle:ue(ne.samesiteCookiesExplained)}]},Ie={file:"SameSiteNoneInsecureWarnSet.md",links:[{link:"https://web.dev/samesite-cookies-explained/",linkTitle:ue(ne.samesiteCookiesExplained)}]},fe=[{link:"https://web.dev/schemeful-samesite/",linkTitle:ue(ne.howSchemefulSamesiteWorks)}];function ke({isDestinationSecure:e,isOriginSecure:t}){return new Map([["PLACEHOLDER_destination",()=>e?"a secure":"an insecure"],["PLACEHOLDER_origin",()=>t?"a secure":"an insecure"]])}function Ce(e){return{file:"SameSiteWarnStrictLaxDowngradeStrict.md",substitutions:ke({isDestinationSecure:e,isOriginSecure:!e}),links:fe}}function Se(e){return{file:"SameSiteExcludeNavigationContextDowngrade.md",substitutions:ke({isDestinationSecure:e,isOriginSecure:!e}),links:fe}}function ye(e){return{file:"SameSiteWarnCrossDowngradeRead.md",substitutions:ke({isDestinationSecure:e,isOriginSecure:!e}),links:fe}}function ve(e){return{file:"SameSiteExcludeContextDowngradeRead.md",substitutions:ke({isDestinationSecure:e,isOriginSecure:!e}),links:fe}}function be(e){return{file:"SameSiteWarnCrossDowngradeSet.md",substitutions:ke({isDestinationSecure:!e,isOriginSecure:e}),links:fe}}function we(e){return{file:"SameSiteExcludeContextDowngradeSet.md",substitutions:ke({isDestinationSecure:e,isOriginSecure:!e}),links:fe}}const De={file:"SameSiteInvalidSameParty.md",links:[{link:"https://developer.chrome.com/blog/first-party-sets-sameparty/",linkTitle:ue(ne.firstPartySetsExplained)}]},Te={file:"SameSiteSamePartyCrossPartyContextSet.md",links:[{link:"https://developer.chrome.com/blog/first-party-sets-sameparty/",linkTitle:ue(ne.firstPartySetsExplained)}]},Re={file:"CookieAttributeValueExceedsMaxSize.md",links:[]},Ae={file:"cookieWarnDomainNonAscii.md",links:[]},Ee={file:"cookieExcludeDomainNonAscii.md",links:[]},Pe={file:"cookieExcludeBlockedWithinRelatedWebsiteSet.md",links:[]},Me={file:"cookieWarnThirdPartyPhaseoutSet.md",links:[{link:"https://goo.gle/3pcd-dev-issue",linkTitle:ue(ne.thirdPartyPhaseoutExplained)}]},Fe={file:"cookieWarnThirdPartyPhaseoutRead.md",links:[{link:"https://goo.gle/3pcd-dev-issue",linkTitle:ue(ne.thirdPartyPhaseoutExplained)}]},Ne={file:"cookieExcludeThirdPartyPhaseoutSet.md",links:[{link:"https://goo.gle/3pcd-dev-issue",linkTitle:ue(ne.thirdPartyPhaseoutExplained)}]},xe={file:"cookieExcludeThirdPartyPhaseoutRead.md",links:[{link:"https://goo.gle/3pcd-dev-issue",linkTitle:ue(ne.thirdPartyPhaseoutExplained)}]},Le={file:"cookieCrossSiteRedirectDowngrade.md",links:[{link:"https://bugs.chromium.org/p/chromium/issues/entry?template=Defect%20report%20from%20user&summary=[Cross-Site Redirect Chain] <INSERT BUG SUMMARY HERE>&comment=Chrome Version: (copy from chrome://version)%0AChannel: (e.g. Canary, Dev, Beta, Stable)%0A%0AAffected URLs:%0A%0AWhat is the expected result?%0A%0AWhat happens instead?%0A%0AWhat is the purpose of the cross-site redirect?:%0A%0AWhat steps will reproduce the problem?:%0A(1)%0A(2)%0A(3)%0A%0APlease provide any additional information below.&components=Internals%3ENetwork%3ECookies",linkTitle:ue(ne.fileCrosSiteRedirectBug)}]},Oe=new Map([["CookieIssue::WarnSameSiteUnspecifiedLaxAllowUnsafe::ReadCookie",ce],["CookieIssue::WarnSameSiteUnspecifiedLaxAllowUnsafe::SetCookie",ge],["CookieIssue::WarnSameSiteUnspecifiedCrossSiteContext::ReadCookie",ce],["CookieIssue::WarnSameSiteUnspecifiedCrossSiteContext::SetCookie",ge],["CookieIssue::ExcludeSameSiteNoneInsecure::ReadCookie",he],["CookieIssue::ExcludeSameSiteNoneInsecure::SetCookie",me],["CookieIssue::WarnSameSiteNoneInsecure::ReadCookie",pe],["CookieIssue::WarnSameSiteNoneInsecure::SetCookie",Ie],["CookieIssue::WarnSameSiteStrictLaxDowngradeStrict::Secure",Ce(!0)],["CookieIssue::WarnSameSiteStrictLaxDowngradeStrict::Insecure",Ce(!1)],["CookieIssue::WarnCrossDowngrade::ReadCookie::Secure",ye(!0)],["CookieIssue::WarnCrossDowngrade::ReadCookie::Insecure",ye(!1)],["CookieIssue::WarnCrossDowngrade::SetCookie::Secure",be(!0)],["CookieIssue::WarnCrossDowngrade::SetCookie::Insecure",be(!1)],["CookieIssue::ExcludeNavigationContextDowngrade::Secure",Se(!0)],["CookieIssue::ExcludeNavigationContextDowngrade::Insecure",Se(!1)],["CookieIssue::ExcludeContextDowngrade::ReadCookie::Secure",ve(!0)],["CookieIssue::ExcludeContextDowngrade::ReadCookie::Insecure",ve(!1)],["CookieIssue::ExcludeContextDowngrade::SetCookie::Secure",we(!0)],["CookieIssue::ExcludeContextDowngrade::SetCookie::Insecure",we(!1)],["CookieIssue::ExcludeInvalidSameParty::SetCookie",De],["CookieIssue::ExcludeSamePartyCrossPartyContext::SetCookie",Te],["CookieIssue::WarnAttributeValueExceedsMaxSize::ReadCookie",Re],["CookieIssue::WarnAttributeValueExceedsMaxSize::SetCookie",Re],["CookieIssue::WarnDomainNonASCII::ReadCookie",Ae],["CookieIssue::WarnDomainNonASCII::SetCookie",Ae],["CookieIssue::ExcludeDomainNonASCII::ReadCookie",Ee],["CookieIssue::ExcludeDomainNonASCII::SetCookie",Ee],["CookieIssue::ExcludeThirdPartyCookieBlockedInRelatedWebsiteSet::ReadCookie",Pe],["CookieIssue::ExcludeThirdPartyCookieBlockedInRelatedWebsiteSet::SetCookie",Pe],["CookieIssue::WarnThirdPartyPhaseout::ReadCookie",Fe],["CookieIssue::WarnThirdPartyPhaseout::SetCookie",Me],["CookieIssue::ExcludeThirdPartyPhaseout::ReadCookie",xe],["CookieIssue::ExcludeThirdPartyPhaseout::SetCookie",Ne],["CookieIssue::CrossSiteRedirectDowngradeChangesInclusion",Le]]);var We=Object.freeze({__proto__:null,CookieIssue:de,isCausedByThirdParty:le});const He={corsPrivateNetworkAccess:"Private Network Access",CORS:"Cross-Origin Resource Sharing (`CORS`)"},Ue=s.i18n.registerUIStrings("models/issues_manager/CorsIssue.ts",He),qe=s.i18n.getLocalizedString.bind(void 0,Ue);function _e(e){switch(e.corsErrorStatus.corsError){case"InvalidAllowMethodsPreflightResponse":case"InvalidAllowHeadersPreflightResponse":case"PreflightMissingAllowOriginHeader":case"PreflightMultipleAllowOriginValues":case"PreflightInvalidAllowOriginValue":case"MissingAllowOriginHeader":case"MultipleAllowOriginValues":case"InvalidAllowOriginValue":return"CorsIssue::InvalidHeaders";case"PreflightWildcardOriginNotAllowed":case"WildcardOriginNotAllowed":return"CorsIssue::WildcardOriginWithCredentials";case"PreflightInvalidStatus":case"PreflightDisallowedRedirect":case"InvalidResponse":return"CorsIssue::PreflightResponseInvalid";case"AllowOriginMismatch":case"PreflightAllowOriginMismatch":return"CorsIssue::OriginMismatch";case"InvalidAllowCredentials":case"PreflightInvalidAllowCredentials":return"CorsIssue::AllowCredentialsRequired";case"MethodDisallowedByPreflightResponse":return"CorsIssue::MethodDisallowedByPreflightResponse";case"HeaderDisallowedByPreflightResponse":return"CorsIssue::HeaderDisallowedByPreflightResponse";case"RedirectContainsCredentials":return"CorsIssue::RedirectContainsCredentials";case"DisallowedByMode":return"CorsIssue::DisallowedByMode";case"CorsDisabledScheme":return"CorsIssue::CorsDisabledScheme";case"PreflightMissingAllowExternal":return"CorsIssue::PreflightMissingAllowExternal";case"PreflightInvalidAllowExternal":return"CorsIssue::PreflightInvalidAllowExternal";case"InsecurePrivateNetwork":return"CorsIssue::InsecurePrivateNetwork";case"NoCorsRedirectModeNotFollow":return"CorsIssue::NoCorsRedirectModeNotFollow";case"InvalidPrivateNetworkAccess":return"CorsIssue::InvalidPrivateNetworkAccess";case"UnexpectedPrivateNetworkAccess":return"CorsIssue::UnexpectedPrivateNetworkAccess";case"PreflightMissingAllowPrivateNetwork":case"PreflightInvalidAllowPrivateNetwork":return"CorsIssue::PreflightAllowPrivateNetworkError";case"PreflightMissingPrivateNetworkAccessId":return"CorsIssue::PreflightMissingPrivateNetworkAccessId";case"PreflightMissingPrivateNetworkAccessName":return"CorsIssue::PreflightMissingPrivateNetworkAccessName";case"PrivateNetworkAccessPermissionUnavailable":return"CorsIssue::PrivateNetworkAccessPermissionUnavailable";case"PrivateNetworkAccessPermissionDenied":return"CorsIssue::PrivateNetworkAccessPermissionDenied"}}class Be extends c{#o;constructor(e,t,s){super(_e(e),t,s),this.#o=e}getCategory(){return"Cors"}details(){return this.#o}getDescription(){switch(_e(this.#o)){case"CorsIssue::InsecurePrivateNetwork":return{file:"corsInsecurePrivateNetwork.md",links:[{link:"https://developer.chrome.com/blog/private-network-access-update",linkTitle:qe(He.corsPrivateNetworkAccess)}]};case"CorsIssue::PreflightAllowPrivateNetworkError":return{file:"corsPreflightAllowPrivateNetworkError.md",links:[{link:"https://developer.chrome.com/blog/private-network-access-update",linkTitle:qe(He.corsPrivateNetworkAccess)}]};case"CorsIssue::InvalidHeaders":return{file:"corsInvalidHeaderValues.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::WildcardOriginWithCredentials":return{file:"corsWildcardOriginNotAllowed.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::PreflightResponseInvalid":return{file:"corsPreflightResponseInvalid.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::OriginMismatch":return{file:"corsOriginMismatch.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::AllowCredentialsRequired":return{file:"corsAllowCredentialsRequired.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::MethodDisallowedByPreflightResponse":return{file:"corsMethodDisallowedByPreflightResponse.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::HeaderDisallowedByPreflightResponse":return{file:"corsHeaderDisallowedByPreflightResponse.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::RedirectContainsCredentials":return{file:"corsRedirectContainsCredentials.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::DisallowedByMode":return{file:"corsDisallowedByMode.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::CorsDisabledScheme":return{file:"corsDisabledScheme.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::NoCorsRedirectModeNotFollow":return{file:"corsNoCorsRedirectModeNotFollow.md",links:[{link:"https://web.dev/cross-origin-resource-sharing",linkTitle:qe(He.CORS)}]};case"CorsIssue::PreflightMissingPrivateNetworkAccessId":case"CorsIssue::PreflightMissingPrivateNetworkAccessName":return{file:"corsPrivateNetworkPermissionDenied.md",links:[{link:"https://developer.chrome.com/blog/private-network-access-update",linkTitle:qe(He.corsPrivateNetworkAccess)}]};case"CorsIssue::PreflightMissingAllowExternal":case"CorsIssue::PreflightInvalidAllowExternal":case"CorsIssue::InvalidPrivateNetworkAccess":case"CorsIssue::UnexpectedPrivateNetworkAccess":case"CorsIssue::PrivateNetworkAccessPermissionUnavailable":case"CorsIssue::PrivateNetworkAccessPermissionDenied":return null}}primaryKey(){return JSON.stringify(this.#o)}getKind(){return!this.#o.isWarning||"InsecurePrivateNetwork"!==this.#o.corsErrorStatus.corsError&&"PreflightMissingAllowPrivateNetwork"!==this.#o.corsErrorStatus.corsError&&"PreflightInvalidAllowPrivateNetwork"!==this.#o.corsErrorStatus.corsError?"PageError":"BreakingChange"}static fromInspectorIssue(e,t){const s=t.details.corsIssueDetails;return s?[new Be(s,e,t.issueId)]:(console.warn("Cors issue without details received."),[])}}var ze=Object.freeze({__proto__:null,CorsIssue:Be});const Ve={coopAndCoep:"COOP and COEP",samesiteAndSameorigin:"Same-Site and Same-Origin"},je=s.i18n.registerUIStrings("models/issues_manager/CrossOriginEmbedderPolicyIssue.ts",Ve),Ke=s.i18n.getLazilyComputedLocalizedString.bind(void 0,je);function $e(e){switch(e){case"CoepFrameResourceNeedsCoepHeader":case"CoopSandboxedIFrameCannotNavigateToCoopPage":case"CorpNotSameOrigin":case"CorpNotSameOriginAfterDefaultedToSameOriginByCoep":case"CorpNotSameSite":return!0}return!1}class Ge extends c{#o;constructor(e,t){super(`CrossOriginEmbedderPolicyIssue::${e.reason}`,t),this.#o=e}primaryKey(){return`${this.code()}-(${this.#o.request.requestId})`}getBlockedByResponseDetails(){return[this.#o]}requests(){return[this.#o.request]}getCategory(){return"CrossOriginEmbedderPolicy"}getDescription(){const e=Je.get(this.code());return e?S(e):null}getKind(){return"PageError"}}const Je=new Map([["CrossOriginEmbedderPolicyIssue::CorpNotSameOriginAfterDefaultedToSameOriginByCoep",{file:"CoepCorpNotSameOriginAfterDefaultedToSameOriginByCoep.md",links:[{link:"https://web.dev/coop-coep/",linkTitle:Ke(Ve.coopAndCoep)},{link:"https://web.dev/same-site-same-origin/",linkTitle:Ke(Ve.samesiteAndSameorigin)}]}],["CrossOriginEmbedderPolicyIssue::CoepFrameResourceNeedsCoepHeader",{file:"CoepFrameResourceNeedsCoepHeader.md",links:[{link:"https://web.dev/coop-coep/",linkTitle:Ke(Ve.coopAndCoep)}]}],["CrossOriginEmbedderPolicyIssue::CoopSandboxedIframeCannotNavigateToCoopPage",{file:"CoepCoopSandboxedIframeCannotNavigateToCoopPage.md",links:[{link:"https://web.dev/coop-coep/",linkTitle:Ke(Ve.coopAndCoep)}]}],["CrossOriginEmbedderPolicyIssue::CorpNotSameSite",{file:"CoepCorpNotSameSite.md",links:[{link:"https://web.dev/coop-coep/",linkTitle:Ke(Ve.coopAndCoep)},{link:"https://web.dev/same-site-same-origin/",linkTitle:Ke(Ve.samesiteAndSameorigin)}]}],["CrossOriginEmbedderPolicyIssue::CorpNotSameOrigin",{file:"CoepCorpNotSameOrigin.md",links:[{link:"https://web.dev/coop-coep/",linkTitle:Ke(Ve.coopAndCoep)},{link:"https://web.dev/same-site-same-origin/",linkTitle:Ke(Ve.samesiteAndSameorigin)}]}]]);var Qe=Object.freeze({__proto__:null,isCrossOriginEmbedderPolicyIssue:$e,CrossOriginEmbedderPolicyIssue:Ge});const Ye={AuthorizationCoveredByWildcard:"Authorization will not be covered by the wildcard symbol (*) in CORS `Access-Control-Allow-Headers` handling.",CanRequestURLHTTPContainingNewline:"Resource requests whose URLs contained both removed whitespace `\\(n|r|t)` characters and less-than characters (`<`) are blocked. Please remove newlines and encode less-than characters from places like element attribute values in order to load these resources.",ChromeLoadTimesConnectionInfo:"`chrome.loadTimes()` is deprecated, instead use standardized API: Navigation Timing 2.",ChromeLoadTimesFirstPaintAfterLoadTime:"`chrome.loadTimes()` is deprecated, instead use standardized API: Paint Timing.",ChromeLoadTimesWasAlternateProtocolAvailable:"`chrome.loadTimes()` is deprecated, instead use standardized API: `nextHopProtocol` in Navigation Timing 2.",CookieWithTruncatingChar:"Cookies containing a `\\(0|r|n)` character will be rejected instead of truncated.",CrossOriginAccessBasedOnDocumentDomain:"Relaxing the same-origin policy by setting `document.domain` is deprecated, and will be disabled by default. This deprecation warning is for a cross-origin access that was enabled by setting `document.domain`.",CrossOriginWindowAlert:"Triggering window.alert from cross origin iframes has been deprecated and will be removed in the future.",CrossOriginWindowConfirm:"Triggering window.confirm from cross origin iframes has been deprecated and will be removed in the future.",CSSCustomStateDeprecatedSyntax:"`:--customstatename` is deprecated. Please use the `:state(customstatename)` syntax instead.",CSSSelectorInternalMediaControlsOverlayCastButton:"The `disableRemotePlayback` attribute should be used in order to disable the default Cast integration instead of using `-internal-media-controls-overlay-cast-button` selector.",CSSValueAppearanceNonStandard:"CSS appearance values  `inner-spin-button`, `media-slider`, `media-sliderthumb`, `media-volume-slider`, `media-volume-sliderthumb`, `push-button`, `searchfield-cancel-button`, `slider-horizontal`, `sliderthumb-horizontal`, `sliderthumb-vertical`, `square-button` are not standardized and will be removed.",CSSValueAppearanceSliderVertical:"CSS appearance value `slider-vertical` is not standardized and will be removed.",DataUrlInSvgUse:"Support for data: URLs in SVGUseElement is deprecated and it will be removed in the future.",DelegatedInkExpectedImprovement:"`DelegatedInkTrailPresenter.expectedImprovement` is deprecated due to potential fingerprinting concerns.",DocumentDomainSettingWithoutOriginAgentClusterHeader:"Relaxing the same-origin policy by setting `document.domain` is deprecated, and will be disabled by default. To continue using this feature, please opt-out of origin-keyed agent clusters by sending an `Origin-Agent-Cluster: ?0` header along with the HTTP response for the document and frames. See https://developer.chrome.com/blog/immutable-document-domain/ for more details.",DOMMutationEvents:"DOM Mutation Events, including `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument`, and `DOMCharacterDataModified` are deprecated (https://w3c.github.io/uievents/#legacy-event-types) and will be removed. Please use `MutationObserver` instead.",GeolocationInsecureOrigin:"`getCurrentPosition()` and `watchPosition()` no longer work on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details.",GeolocationInsecureOriginDeprecatedNotRemoved:"`getCurrentPosition()` and `watchPosition()` are deprecated on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details.",GetInnerHTML:"The getInnerHTML() function is deprecated, and will be removed from this browser very soon. Please use getHTML() instead.",GetUserMediaInsecureOrigin:"`getUserMedia()` no longer works on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details.",HostCandidateAttributeGetter:"`RTCPeerConnectionIceErrorEvent.hostCandidate` is deprecated. Please use `RTCPeerConnectionIceErrorEvent.address` or `RTCPeerConnectionIceErrorEvent.port` instead.",IdentityInCanMakePaymentEvent:"The merchant origin and arbitrary data from the `canmakepayment` service worker event are deprecated and will be removed: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`.",InsecurePrivateNetworkSubresourceRequest:"The website requested a subresource from a network that it could only access because of its users' privileged network position. These requests expose non-public devices and servers to the internet, increasing the risk of a cross-site request forgery (CSRF) attack, and/or information leakage. To mitigate these risks, Chrome deprecates requests to non-public subresources when initiated from non-secure contexts, and will start blocking them.",InterestGroupDailyUpdateUrl:"The `dailyUpdateUrl` field of `InterestGroups` passed to `joinAdInterestGroup()` has been renamed to `updateUrl`, to more accurately reflect its behavior.",LocalCSSFileExtensionRejected:"CSS cannot be loaded from `file:` URLs unless they end in a `.css` file extension.",MediaSourceAbortRemove:"Using `SourceBuffer.abort()` to abort `remove()`'s asynchronous range removal is deprecated due to specification change. Support will be removed in the future. You should listen to the `updateend` event instead. `abort()` is intended to only abort an asynchronous media append or reset parser state.",MediaSourceDurationTruncatingBuffered:"Setting `MediaSource.duration` below the highest presentation timestamp of any buffered coded frames is deprecated due to specification change. Support for implicit removal of truncated buffered media will be removed in the future. You should instead perform explicit `remove(newDuration, oldDuration)` on all `sourceBuffers`, where `newDuration < oldDuration`.",NoSysexWebMIDIWithoutPermission:"Web MIDI will ask a permission to use even if the sysex is not specified in the `MIDIOptions`.",NotificationInsecureOrigin:"The Notification API may no longer be used from insecure origins. You should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details.",NotificationPermissionRequestedIframe:"Permission for the Notification API may no longer be requested from a cross-origin iframe. You should consider requesting permission from a top-level frame or opening a new window instead.",ObsoleteCreateImageBitmapImageOrientationNone:"Option `imageOrientation: 'none'` in createImageBitmap is deprecated. Please use createImageBitmap with option {imageOrientation: 'from-image'} instead.",ObsoleteWebRtcCipherSuite:"Your partner is negotiating an obsolete (D)TLS version. Please check with your partner to have this fixed.",OverflowVisibleOnReplacedElement:"Specifying `overflow: visible` on img, video and canvas tags may cause them to produce visual content outside of the element bounds. See https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md.",PaymentInstruments:"`paymentManager.instruments` is deprecated. Please use just-in-time install for payment handlers instead.",PaymentRequestCSPViolation:"Your `PaymentRequest` call bypassed Content-Security-Policy (CSP) `connect-src` directive. This bypass is deprecated. Please add the payment method identifier from the `PaymentRequest` API (in `supportedMethods` field) to your CSP `connect-src` directive.",PersistentQuotaType:"`StorageType.persistent` is deprecated. Please use standardized `navigator.storage` instead.",PictureSourceSrc:"`<source src>` with a `<picture>` parent is invalid and therefore ignored. Please use `<source srcset>` instead.",PrefixedCancelAnimationFrame:"webkitCancelAnimationFrame is vendor-specific. Please use the standard cancelAnimationFrame instead.",PrefixedRequestAnimationFrame:"webkitRequestAnimationFrame is vendor-specific. Please use the standard requestAnimationFrame instead.",PrefixedVideoDisplayingFullscreen:"HTMLVideoElement.webkitDisplayingFullscreen is deprecated. Please use Document.fullscreenElement instead.",PrefixedVideoEnterFullScreen:"HTMLVideoElement.webkitEnterFullScreen() is deprecated. Please use Element.requestFullscreen() instead.",PrefixedVideoEnterFullscreen:"HTMLVideoElement.webkitEnterFullscreen() is deprecated. Please use Element.requestFullscreen() instead.",PrefixedVideoExitFullScreen:"HTMLVideoElement.webkitExitFullScreen() is deprecated. Please use Document.exitFullscreen() instead.",PrefixedVideoExitFullscreen:"HTMLVideoElement.webkitExitFullscreen() is deprecated. Please use Document.exitFullscreen() instead.",PrefixedVideoSupportsFullscreen:"HTMLVideoElement.webkitSupportsFullscreen is deprecated. Please use Document.fullscreenEnabled instead.",PrivacySandboxExtensionsAPI:"We're deprecating the API `chrome.privacy.websites.privacySandboxEnabled`, though it will remain active for backward compatibility until release M113. Instead, please use `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` and `chrome.privacy.websites.adMeasurementEnabled`. See https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled.",RangeExpand:"Range.expand() is deprecated. Please use Selection.modify() instead.",RequestedSubresourceWithEmbeddedCredentials:"Subresource requests whose URLs contain embedded credentials (e.g. `**********************/`) are blocked.",RTCConstraintEnableDtlsSrtpFalse:"The constraint `DtlsSrtpKeyAgreement` is removed. You have specified a `false` value for this constraint, which is interpreted as an attempt to use the removed `SDES key negotiation` method. This functionality is removed; use a service that supports `DTLS key negotiation` instead.",RTCConstraintEnableDtlsSrtpTrue:"The constraint `DtlsSrtpKeyAgreement` is removed. You have specified a `true` value for this constraint, which had no effect, but you can remove this constraint for tidiness.",RTCPeerConnectionGetStatsLegacyNonCompliant:"The callback-based getStats() is deprecated and will be removed. Use the spec-compliant getStats() instead.",RtcpMuxPolicyNegotiate:"The `rtcpMuxPolicy` option is deprecated and will be removed.",SharedArrayBufferConstructedWithoutIsolation:"`SharedArrayBuffer` will require cross-origin isolation. See https://developer.chrome.com/blog/enabling-shared-array-buffer/ for more details.",TextToSpeech_DisallowedByAutoplay:"`speechSynthesis.speak()` without user activation is deprecated and will be removed.",UnloadHandler:"Unload event listeners are deprecated and will be removed.",V8SharedArrayBufferConstructedInExtensionWithoutIsolation:"Extensions should opt into cross-origin isolation to continue using `SharedArrayBuffer`. See https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/.",WebSQL:"Web SQL is deprecated. Please use SQLite WebAssembly or Indexed Database",XHRJSONEncodingDetection:"UTF-16 is not supported by response json in `XMLHttpRequest`",XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload:"Synchronous `XMLHttpRequest` on the main thread is deprecated because of its detrimental effects to the end user's experience. For more help, check https://xhr.spec.whatwg.org/.",XRSupportsSession:"`supportsSession()` is deprecated. Please use `isSessionSupported()` and check the resolved boolean value instead."},Xe={AuthorizationCoveredByWildcard:{milestone:97},CSSCustomStateDeprecatedSyntax:{chromeStatusFeature:****************,milestone:122},CSSSelectorInternalMediaControlsOverlayCastButton:{chromeStatusFeature:5714245488476160},CSSValueAppearanceNonStandard:{chromeStatusFeature:5066630972833792},CSSValueAppearanceSliderVertical:{chromeStatusFeature:6001359429566464},CanRequestURLHTTPContainingNewline:{chromeStatusFeature:5735596811091968},ChromeLoadTimesConnectionInfo:{chromeStatusFeature:5637885046816768},ChromeLoadTimesFirstPaintAfterLoadTime:{chromeStatusFeature:5637885046816768},ChromeLoadTimesWasAlternateProtocolAvailable:{chromeStatusFeature:5637885046816768},CookieWithTruncatingChar:{milestone:103},CrossOriginAccessBasedOnDocumentDomain:{milestone:115},DOMMutationEvents:{chromeStatusFeature:5083947249172480,milestone:127},DataUrlInSvgUse:{chromeStatusFeature:****************,milestone:119},DocumentDomainSettingWithoutOriginAgentClusterHeader:{milestone:115},GetInnerHTML:{chromeStatusFeature:5081733588582400},IdentityInCanMakePaymentEvent:{chromeStatusFeature:5190978431352832},InsecurePrivateNetworkSubresourceRequest:{chromeStatusFeature:5436853517811712,milestone:92},LocalCSSFileExtensionRejected:{milestone:64},MediaSourceAbortRemove:{chromeStatusFeature:6107495151960064},MediaSourceDurationTruncatingBuffered:{chromeStatusFeature:6107495151960064},NoSysexWebMIDIWithoutPermission:{chromeStatusFeature:5138066234671104,milestone:82},NotificationPermissionRequestedIframe:{chromeStatusFeature:6451284559265792},ObsoleteCreateImageBitmapImageOrientationNone:{milestone:111},ObsoleteWebRtcCipherSuite:{milestone:81},OverflowVisibleOnReplacedElement:{chromeStatusFeature:5137515594383360,milestone:108},PaymentInstruments:{chromeStatusFeature:5099285054488576},PaymentRequestCSPViolation:{chromeStatusFeature:6286595631087616},PersistentQuotaType:{chromeStatusFeature:5176235376246784,milestone:106},RTCConstraintEnableDtlsSrtpFalse:{milestone:97},RTCConstraintEnableDtlsSrtpTrue:{milestone:97},RTCPeerConnectionGetStatsLegacyNonCompliant:{chromeStatusFeature:4631626228695040,milestone:117},RequestedSubresourceWithEmbeddedCredentials:{chromeStatusFeature:5669008342777856},RtcpMuxPolicyNegotiate:{chromeStatusFeature:5654810086866944,milestone:62},SharedArrayBufferConstructedWithoutIsolation:{milestone:106},TextToSpeech_DisallowedByAutoplay:{chromeStatusFeature:5687444770914304,milestone:71},UnloadHandler:{chromeStatusFeature:5579556305502208},V8SharedArrayBufferConstructedInExtensionWithoutIsolation:{milestone:96},WebSQL:{chromeStatusFeature:5134293578285056,milestone:115},XHRJSONEncodingDetection:{milestone:93},XRSupportsSession:{milestone:80}},Ze={feature:"Check the feature status page for more details.",milestone:"This change will go into effect with milestone {milestone}.",title:"Deprecated feature used"},et=s.i18n.registerUIStrings("models/issues_manager/DeprecationIssue.ts",Ze),tt=s.i18n.getLazilyComputedLocalizedString.bind(void 0,et),st=s.i18n.registerUIStrings("generated/Deprecation.ts",Ye),it=s.i18n.getLazilyComputedLocalizedString.bind(void 0,st);class rt extends c{#o;constructor(e,t){super({code:["DeprecationIssue",e.type].join("::"),umaCode:"DeprecationIssue"},t),this.#o=e}getCategory(){return"Other"}details(){return this.#o}getDescription(){let e=()=>"";const t=Ye[this.#o.type];t&&(e=it(t));const s=[],i=Xe[this.#o.type],r=i?.chromeStatusFeature??0;0!==r&&s.push({link:`https://chromestatus.com/feature/${r}`,linkTitle:tt(Ze.feature)});const o=i?.milestone??0;return 0!==o&&s.push({link:"https://chromiumdash.appspot.com/schedule",linkTitle:tt(Ze.milestone,{milestone:o})}),S({file:"deprecation.md",substitutions:new Map([["PLACEHOLDER_title",tt(Ze.title)],["PLACEHOLDER_message",e]]),links:s})}sources(){return this.#o.sourceCodeLocation?[this.#o.sourceCodeLocation]:[]}primaryKey(){return JSON.stringify(this.#o)}getKind(){return"BreakingChange"}static fromInspectorIssue(e,t){const s=t.details.deprecationIssueDetails;return s?[new rt(s,e)]:(console.warn("Deprecation issue without details received."),[])}}var ot=Object.freeze({__proto__:null,DeprecationIssue:rt});const nt={fedCmUserInfo:"Federated Credential Management User Info API"},at=s.i18n.registerUIStrings("models/issues_manager/FederatedAuthUserInfoRequestIssue.ts",nt),ut=s.i18n.getLazilyComputedLocalizedString.bind(void 0,at);class dt extends c{#o;constructor(e,t){super({code:"FederatedAuthUserInfoRequestIssue",umaCode:["FederatedAuthUserInfoRequestIssue",e.federatedAuthUserInfoRequestIssueReason].join("::")},t),this.#o=e}getCategory(){return"Other"}details(){return this.#o}getDescription(){const e=lt.get(this.#o.federatedAuthUserInfoRequestIssueReason);return e?S(e):null}primaryKey(){return JSON.stringify(this.#o)}getKind(){return"PageError"}static fromInspectorIssue(e,t){const s=t.details.federatedAuthUserInfoRequestIssueDetails;return s?[new dt(s,e)]:(console.warn("Federated auth user info request issue without details received."),[])}}const lt=new Map([["NotSameOrigin",{file:"federatedAuthUserInfoRequestNotSameOrigin.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:ut(nt.fedCmUserInfo)}]}],["NotIframe",{file:"federatedAuthUserInfoRequestNotIframe.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:ut(nt.fedCmUserInfo)}]}],["NotPotentiallyTrustworthy",{file:"federatedAuthUserInfoRequestNotPotentiallyTrustworthy.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:ut(nt.fedCmUserInfo)}]}],["NoApiPermission",{file:"federatedAuthUserInfoRequestNoApiPermission.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:ut(nt.fedCmUserInfo)}]}],["NotSignedInWithIdp",{file:"federatedAuthUserInfoRequestNotSignedInWithIdp.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:ut(nt.fedCmUserInfo)}]}],["NoAccountSharingPermission",{file:"federatedAuthUserInfoRequestNoAccountSharingPermission.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:ut(nt.fedCmUserInfo)}]}],["InvalidConfigOrWellKnown",{file:"federatedAuthUserInfoRequestInvalidConfigOrWellKnown.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:ut(nt.fedCmUserInfo)}]}],["InvalidAccountsResponse",{file:"federatedAuthUserInfoRequestInvalidAccountsResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:ut(nt.fedCmUserInfo)}]}],["NoReturningUserFromFetchedAccounts",{file:"federatedAuthUserInfoRequestNoReturningUserFromFetchedAccounts.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:ut(nt.fedCmUserInfo)}]}]]);var ct=Object.freeze({__proto__:null,FederatedAuthUserInfoRequestIssue:dt});const gt={howDoesAutofillWorkPageTitle:"How does autofill work?",labelFormlementsPageTitle:"The label elements",inputFormElementPageTitle:"The form input element",autocompleteAttributePageTitle:"HTML attribute: autocomplete",corbExplainerPageTitle:"CORB explainer"},ht=s.i18n.registerUIStrings("models/issues_manager/GenericIssue.ts",gt),mt=s.i18n.getLazilyComputedLocalizedString.bind(void 0,ht);class pt extends c{#o;constructor(e,t,s){super(["GenericIssue",e.errorType].join("::"),t,s),this.#o=e}requests(){return this.#o.request?[this.#o.request]:[]}getCategory(){return"Generic"}primaryKey(){const e=this.#o.request?this.#o.request.requestId:"no-request";return`${this.code()}-(${this.#o.frameId})-(${this.#o.violatingNodeId})-(${this.#o.violatingNodeAttribute})-(${e})`}getDescription(){const e=Rt.get(this.#o.errorType);return e?S(e):null}details(){return this.#o}getKind(){return At.get(this.#o.errorType)||"Improvement"}static fromInspectorIssue(e,t){const s=t.details.genericIssueDetails;return s?[new pt(s,e,t.issueId)]:(console.warn("Generic issue without details received."),[])}}const It={file:"genericFormLabelForNameError.md",links:[{link:"https://html.spec.whatwg.org/multipage/forms.html#attr-label-for",linkTitle:s.i18n.lockedLazyString("HTML Standard")}]},ft={file:"genericFormInputWithNoLabelError.md",links:[]},kt={file:"genericFormAutocompleteAttributeEmptyError.md",links:[]},Ct={file:"genericFormDuplicateIdForInputError.md",links:[{link:"https://web.dev/learn/forms/autofill/#how-does-autofill-work",linkTitle:mt(gt.howDoesAutofillWorkPageTitle)}]},St={file:"genericFormAriaLabelledByToNonExistingId.md",links:[{link:"https://developer.mozilla.org/en-US/docs/Web/HTML/Element/label",linkTitle:mt(gt.labelFormlementsPageTitle)}]},yt={file:"genericFormEmptyIdAndNameAttributesForInputError.md",links:[{link:"https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input",linkTitle:mt(gt.inputFormElementPageTitle)}]},vt={file:"genericFormInputAssignedAutocompleteValueToIdOrNameAttributeError.md",links:[{link:"https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/autocomplete#values",linkTitle:mt(gt.autocompleteAttributePageTitle)}]},bt={file:"genericFormInputHasWrongButWellIntendedAutocompleteValueError.md",links:[{link:"https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/autocomplete#values",linkTitle:mt(gt.autocompleteAttributePageTitle)}]},wt={file:"genericFormLabelForMatchesNonExistingIdError.md",links:[{link:"https://developer.mozilla.org/en-US/docs/Web/HTML/Element/label",linkTitle:mt(gt.labelFormlementsPageTitle)}]},Dt={file:"genericFormLabelHasNeitherForNorNestedInput.md",links:[{link:"https://developer.mozilla.org/en-US/docs/Web/HTML/Element/label",linkTitle:mt(gt.labelFormlementsPageTitle)}]},Tt={file:"genericResponseWasBlockedByORB.md",links:[{link:"https://www.chromium.org/Home/chromium-security/corb-for-developers/",linkTitle:mt(gt.corbExplainerPageTitle)}]},Rt=new Map([["FormLabelForNameError",It],["FormInputWithNoLabelError",ft],["FormAutocompleteAttributeEmptyError",kt],["FormDuplicateIdForInputError",Ct],["FormAriaLabelledByToNonExistingId",St],["FormEmptyIdAndNameAttributesForInputError",yt],["FormInputAssignedAutocompleteValueToIdOrNameAttributeError",vt],["FormLabelForMatchesNonExistingIdError",wt],["FormLabelHasNeitherForNorNestedInput",Dt],["FormInputHasWrongButWellIntendedAutocompleteValueError",bt],["ResponseWasBlockedByORB",Tt]]),At=new Map([["FormLabelForNameError","PageError"],["FormInputWithNoLabelError","Improvement"],["FormAutocompleteAttributeEmptyError","PageError"],["FormDuplicateIdForInputError","PageError"],["FormAriaLabelledByToNonExistingId","Improvement"],["FormEmptyIdAndNameAttributesForInputError","Improvement"],["FormInputAssignedAutocompleteValueToIdOrNameAttributeError","Improvement"],["FormLabelForMatchesNonExistingIdError","PageError"],["FormLabelHasNeitherForNorNestedInput","Improvement"],["FormInputHasWrongButWellIntendedAutocompleteValueError","Improvement"]]);var Et=Object.freeze({__proto__:null,GenericIssue:pt,genericFormLabelForNameError:It,genericFormInputWithNoLabelError:ft,genericFormAutocompleteAttributeEmptyError:kt,genericFormDuplicateIdForInputError:Ct,genericFormAriaLabelledByToNonExistingId:St,genericFormEmptyIdAndNameAttributesForInputError:yt,genericFormInputAssignedAutocompleteValueToIdOrNameAttributeError:vt,genericFormInputHasWrongButWellIntendedAutocompleteValue:bt,genericFormLabelForMatchesNonExistingIdError:wt,genericFormLabelHasNeitherForNorNestedInput:Dt,genericResponseWasBlockedbyORB:Tt});const Pt={handlingHeavyAdInterventions:"Handling Heavy Ad Interventions"},Mt=s.i18n.registerUIStrings("models/issues_manager/HeavyAdIssue.ts",Pt),Ft=s.i18n.getLocalizedString.bind(void 0,Mt);class Nt extends c{#o;constructor(e,t){super({code:"HeavyAdIssue",umaCode:["HeavyAdIssue",e.reason].join("::")},t),this.#o=e}details(){return this.#o}primaryKey(){return`HeavyAdIssue-${JSON.stringify(this.#o)}`}getDescription(){return{file:"heavyAd.md",links:[{link:"https://developers.google.com/web/updates/2020/05/heavy-ad-interventions",linkTitle:Ft(Pt.handlingHeavyAdInterventions)}]}}getCategory(){return"HeavyAd"}getKind(){switch(this.#o.resolution){case"HeavyAdBlocked":return"PageError";case"HeavyAdWarning":return"BreakingChange"}}static fromInspectorIssue(e,t){const s=t.details.heavyAdIssueDetails;return s?[new Nt(s,e)]:(console.warn("Heavy Ad issue without details received."),[])}}var xt=Object.freeze({__proto__:null,HeavyAdIssue:Nt});const Lt={bounceTrackingMitigations:"Bounce tracking mitigations"},Ot=s.i18n.registerUIStrings("models/issues_manager/BounceTrackingIssue.ts",Lt),Wt=s.i18n.getLocalizedString.bind(void 0,Ot);class Ht extends c{#o;constructor(e,t){super("BounceTrackingIssue",t),this.#o=e}getCategory(){return"Other"}getDescription(){return{file:"bounceTrackingMitigations.md",links:[{link:"https://privacycg.github.io/nav-tracking-mitigations/#bounce-tracking-mitigations",linkTitle:Wt(Lt.bounceTrackingMitigations)}]}}details(){return this.#o}getKind(){return"BreakingChange"}primaryKey(){return JSON.stringify(this.#o)}trackingSites(){return this.#o.trackingSites?this.#o.trackingSites:[]}static fromInspectorIssue(e,t){const s=t.details.bounceTrackingIssueDetails;return s?[new Ht(s,e)]:(console.warn("Bounce tracking issue without details received."),[])}}const Ut={fedCm:"Federated Credential Management API"},qt=s.i18n.registerUIStrings("models/issues_manager/FederatedAuthRequestIssue.ts",Ut),_t=s.i18n.getLazilyComputedLocalizedString.bind(void 0,qt);class Bt extends c{#o;constructor(e,t){super({code:"FederatedAuthRequestIssue",umaCode:["FederatedAuthRequestIssue",e.federatedAuthRequestIssueReason].join("::")},t),this.#o=e}getCategory(){return"Other"}details(){return this.#o}getDescription(){const e=zt.get(this.#o.federatedAuthRequestIssueReason);return e?S(e):null}primaryKey(){return JSON.stringify(this.#o)}getKind(){return"PageError"}static fromInspectorIssue(e,t){const s=t.details.federatedAuthRequestIssueDetails;return s?[new Bt(s,e)]:(console.warn("Federated auth request issue without details received."),[])}}const zt=new Map([["TooManyRequests",{file:"federatedAuthRequestTooManyRequests.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["ConfigHttpNotFound",{file:"federatedAuthRequestManifestHttpNotFound.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["ConfigNoResponse",{file:"federatedAuthRequestManifestNoResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["ConfigInvalidResponse",{file:"federatedAuthRequestManifestInvalidResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["ClientMetadataHttpNotFound",{file:"federatedAuthRequestClientMetadataHttpNotFound.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["ClientMetadataNoResponse",{file:"federatedAuthRequestClientMetadataNoResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["ClientMetadataInvalidResponse",{file:"federatedAuthRequestClientMetadataInvalidResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["ErrorFetchingSignin",{file:"federatedAuthRequestErrorFetchingSignin.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["InvalidSigninResponse",{file:"federatedAuthRequestInvalidSigninResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["AccountsHttpNotFound",{file:"federatedAuthRequestAccountsHttpNotFound.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["AccountsNoResponse",{file:"federatedAuthRequestAccountsNoResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["AccountsInvalidResponse",{file:"federatedAuthRequestAccountsInvalidResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["IdTokenHttpNotFound",{file:"federatedAuthRequestIdTokenHttpNotFound.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["IdTokenNoResponse",{file:"federatedAuthRequestIdTokenNoResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["IdTokenInvalidResponse",{file:"federatedAuthRequestIdTokenInvalidResponse.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["IdTokenInvalidRequest",{file:"federatedAuthRequestIdTokenInvalidRequest.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["ErrorIdToken",{file:"federatedAuthRequestErrorIdToken.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}],["Canceled",{file:"federatedAuthRequestCanceled.md",links:[{link:"https://fedidcg.github.io/FedCM/",linkTitle:_t(Ut.fedCm)}]}]]),Vt={colorAndContrastAccessibility:"Color and contrast accessibility"},jt=s.i18n.registerUIStrings("models/issues_manager/LowTextContrastIssue.ts",Vt),Kt=s.i18n.getLocalizedString.bind(void 0,jt);class $t extends c{#o;constructor(e,t){super("LowTextContrastIssue",t),this.#o=e}primaryKey(){return`${this.code()}-(${this.#o.violatingNodeId})`}getCategory(){return"LowTextContrast"}details(){return this.#o}getDescription(){return{file:"LowTextContrast.md",links:[{link:"https://web.dev/color-and-contrast-accessibility/",linkTitle:Kt(Vt.colorAndContrastAccessibility)}]}}getKind(){return"Improvement"}static fromInspectorIssue(e,t){const s=t.details.lowTextContrastIssueDetails;return s?[new $t(s,e)]:(console.warn("LowTextContrast issue without details received."),[])}}var Gt=Object.freeze({__proto__:null,LowTextContrastIssue:$t});const Jt={preventingMixedContent:"Preventing mixed content"},Qt=s.i18n.registerUIStrings("models/issues_manager/MixedContentIssue.ts",Jt),Yt=s.i18n.getLocalizedString.bind(void 0,Qt);class Xt extends c{#o;constructor(e,t){super("MixedContentIssue",t),this.#o=e}requests(){return this.#o.request?[this.#o.request]:[]}getDetails(){return this.#o}getCategory(){return"MixedContent"}getDescription(){return{file:"mixedContent.md",links:[{link:"https://web.dev/what-is-mixed-content/",linkTitle:Yt(Jt.preventingMixedContent)}]}}primaryKey(){return JSON.stringify(this.#o)}getKind(){switch(this.#o.resolutionStatus){case"MixedContentAutomaticallyUpgraded":case"MixedContentWarning":return"Improvement";case"MixedContentBlocked":return"PageError"}}static fromInspectorIssue(e,t){const s=t.details.mixedContentIssueDetails;return s?[new Xt(s,e)]:(console.warn("Mixed content issue without details received."),[])}}var Zt=Object.freeze({__proto__:null,MixedContentIssue:Xt});class es extends c{#o;#c;constructor(e,t){const s=JSON.stringify(e);super(s,t),this.#c=s,this.#o=e}sources(){return[this.#o.sourceCodeLocation]}details(){return this.#o}primaryKey(){return this.#c}getPropertyName(){switch(this.#o.propertyRuleIssueReason){case"InvalidInherits":return"inherits";case"InvalidInitialValue":return"initial-value";case"InvalidSyntax":return"syntax"}return""}getDescription(){if("InvalidName"===this.#o.propertyRuleIssueReason)return{file:"propertyRuleInvalidNameIssue.md",links:[]};const e=this.#o.propertyValue?`: ${this.#o.propertyValue}`:"",t=`${this.getPropertyName()}${e}`;return{file:"propertyRuleIssue.md",substitutions:new Map([["PLACEHOLDER_property",t]]),links:[]}}getCategory(){return"Other"}getKind(){return"PageError"}static fromInspectorIssue(e,t){const s=t.details.propertyRuleIssueDetails;return s?[new es(s,e)]:(console.warn("Property rule issue without details received"),[])}}var ts=Object.freeze({__proto__:null,PropertyRuleIssue:es});const ss={documentCompatibilityMode:"Document compatibility mode"},is=s.i18n.registerUIStrings("models/issues_manager/QuirksModeIssue.ts",ss),rs=s.i18n.getLocalizedString.bind(void 0,is);class os extends c{#o;constructor(e,t){super({code:"QuirksModeIssue",umaCode:["QuirksModeIssue",e.isLimitedQuirksMode?"LimitedQuirksMode":"QuirksMode"].join("::")},t),this.#o=e}primaryKey(){return`${this.code()}-(${this.#o.documentNodeId})-(${this.#o.url})`}getCategory(){return"QuirksMode"}details(){return this.#o}getDescription(){return{file:"CompatibilityModeQuirks.md",links:[{link:"https://web.dev/doctype/",linkTitle:rs(ss.documentCompatibilityMode)}]}}getKind(){return"Improvement"}static fromInspectorIssue(e,t){const s=t.details.quirksModeIssueDetails;return s?[new os(s,e)]:(console.warn("Quirks Mode issue without details received."),[])}}var ns=Object.freeze({__proto__:null,QuirksModeIssue:os});const as={enablingSharedArrayBuffer:"Enabling `SharedArrayBuffer`"},us=s.i18n.registerUIStrings("models/issues_manager/SharedArrayBufferIssue.ts",as),ds=s.i18n.getLocalizedString.bind(void 0,us);class ls extends c{#o;constructor(e,t){super({code:"SharedArrayBufferIssue",umaCode:["SharedArrayBufferIssue",e.type].join("::")},t),this.#o=e}getCategory(){return"Other"}details(){return this.#o}getDescription(){return{file:"sharedArrayBuffer.md",links:[{link:"https://developer.chrome.com/blog/enabling-shared-array-buffer/",linkTitle:ds(as.enablingSharedArrayBuffer)}]}}primaryKey(){return JSON.stringify(this.#o)}getKind(){return this.#o.isWarning?"BreakingChange":"PageError"}static fromInspectorIssue(e,t){const s=t.details.sharedArrayBufferIssueDetails;return s?[new ls(s,e)]:(console.warn("SAB transfer issue without details received."),[])}}var cs=Object.freeze({__proto__:null,SharedArrayBufferIssue:ls});const gs={compressionDictionaryTransport:"Compression Dictionary Transport"},hs=s.i18n.registerUIStrings("models/issues_manager/SharedDictionaryIssue.ts",gs),ms=s.i18n.getLazilyComputedLocalizedString.bind(void 0,hs);function ps(e){switch(e.sharedDictionaryError){case"UseErrorCrossOriginNoCorsRequest":return"SharedDictionaryIssue::UseErrorCrossOriginNoCorsRequest";case"UseErrorDictionaryLoadFailure":return"SharedDictionaryIssue::UseErrorDictionaryLoadFailure";case"UseErrorMatchingDictionaryNotUsed":return"SharedDictionaryIssue::UseErrorMatchingDictionaryNotUsed";case"UseErrorUnexpectedContentDictionaryHeader":return"SharedDictionaryIssue::UseErrorUnexpectedContentDictionaryHeader";case"WriteErrorCossOriginNoCorsRequest":return"SharedDictionaryIssue::WriteErrorCossOriginNoCorsRequest";case"WriteErrorDisallowedBySettings":return"SharedDictionaryIssue::WriteErrorDisallowedBySettings";case"WriteErrorExpiredResponse":return"SharedDictionaryIssue::WriteErrorExpiredResponse";case"WriteErrorFeatureDisabled":return"SharedDictionaryIssue::WriteErrorFeatureDisabled";case"WriteErrorInsufficientResources":return"SharedDictionaryIssue::WriteErrorInsufficientResources";case"WriteErrorInvalidMatchField":return"SharedDictionaryIssue::WriteErrorInvalidMatchField";case"WriteErrorInvalidStructuredHeader":return"SharedDictionaryIssue::WriteErrorInvalidStructuredHeader";case"WriteErrorNavigationRequest":return"SharedDictionaryIssue::WriteErrorNavigationRequest";case"WriteErrorNoMatchField":return"SharedDictionaryIssue::WriteErrorNoMatchField";case"WriteErrorNonListMatchDestField":return"SharedDictionaryIssue::WriteErrorNonListMatchDestField";case"WriteErrorNonSecureContext":return"SharedDictionaryIssue::WriteErrorNonSecureContext";case"WriteErrorNonStringIdField":return"SharedDictionaryIssue::WriteErrorNonStringIdField";case"WriteErrorNonStringInMatchDestList":return"SharedDictionaryIssue::WriteErrorNonStringInMatchDestList";case"WriteErrorNonStringMatchField":return"SharedDictionaryIssue::WriteErrorNonStringMatchField";case"WriteErrorNonTokenTypeField":return"SharedDictionaryIssue::WriteErrorNonTokenTypeField";case"WriteErrorRequestAborted":return"SharedDictionaryIssue::WriteErrorRequestAborted";case"WriteErrorShuttingDown":return"SharedDictionaryIssue::WriteErrorShuttingDown";case"WriteErrorTooLongIdField":return"SharedDictionaryIssue::WriteErrorTooLongIdField";case"WriteErrorUnsupportedType":return"SharedDictionaryIssue::WriteErrorUnsupportedType";default:return"SharedDictionaryIssue::WriteErrorUnknown"}}class Is extends c{#o;constructor(e,t){super({code:ps(e),umaCode:["SharedDictionaryIssue",e.sharedDictionaryError].join("::")},t),this.#o=e}requests(){return this.#o.request?[this.#o.request]:[]}getCategory(){return"Other"}details(){return this.#o}getDescription(){const e=ks.get(this.#o.sharedDictionaryError);return e?S(e):null}primaryKey(){return JSON.stringify(this.#o)}getKind(){return"PageError"}static fromInspectorIssue(e,t){const s=t.details.sharedDictionaryIssueDetails;return s?[new Is(s,e)]:(console.warn("Shared Dictionary issue without details received."),[])}}const fs=[{link:"https://datatracker.ietf.org/doc/draft-ietf-httpbis-compression-dictionary/",linkTitle:ms(gs.compressionDictionaryTransport)}],ks=new Map([["UseErrorCrossOriginNoCorsRequest",{file:"sharedDictionaryUseErrorCrossOriginNoCorsRequest.md",links:fs}],["UseErrorDictionaryLoadFailure",{file:"sharedDictionaryUseErrorDictionaryLoadFailure.md",links:fs}],["UseErrorMatchingDictionaryNotUsed",{file:"sharedDictionaryUseErrorMatchingDictionaryNotUsed.md",links:fs}],["UseErrorUnexpectedContentDictionaryHeader",{file:"sharedDictionaryUseErrorUnexpectedContentDictionaryHeader.md",links:fs}],["WriteErrorCossOriginNoCorsRequest",{file:"sharedDictionaryWriteErrorCossOriginNoCorsRequest.md",links:fs}],["WriteErrorDisallowedBySettings",{file:"sharedDictionaryWriteErrorDisallowedBySettings.md",links:fs}],["WriteErrorExpiredResponse",{file:"sharedDictionaryWriteErrorExpiredResponse.md",links:fs}],["WriteErrorFeatureDisabled",{file:"sharedDictionaryWriteErrorFeatureDisabled.md",links:fs}],["WriteErrorInsufficientResources",{file:"sharedDictionaryWriteErrorInsufficientResources.md",links:fs}],["WriteErrorInvalidMatchField",{file:"sharedDictionaryWriteErrorInvalidMatchField.md",links:fs}],["WriteErrorInvalidStructuredHeader",{file:"sharedDictionaryWriteErrorInvalidStructuredHeader.md",links:fs}],["WriteErrorNavigationRequest",{file:"sharedDictionaryWriteErrorNavigationRequest.md",links:fs}],["WriteErrorNoMatchField",{file:"sharedDictionaryWriteErrorNoMatchField.md",links:fs}],["WriteErrorNonListMatchDestField",{file:"sharedDictionaryWriteErrorNonListMatchDestField.md",links:fs}],["WriteErrorNonSecureContext",{file:"sharedDictionaryWriteErrorNonSecureContext.md",links:fs}],["WriteErrorNonStringIdField",{file:"sharedDictionaryWriteErrorNonStringIdField.md",links:fs}],["WriteErrorNonStringInMatchDestList",{file:"sharedDictionaryWriteErrorNonStringInMatchDestList.md",links:fs}],["WriteErrorNonStringMatchField",{file:"sharedDictionaryWriteErrorNonStringMatchField.md",links:fs}],["WriteErrorNonTokenTypeField",{file:"sharedDictionaryWriteErrorNonTokenTypeField.md",links:fs}],["WriteErrorRequestAborted",{file:"sharedDictionaryWriteErrorRequestAborted.md",links:fs}],["WriteErrorShuttingDown",{file:"sharedDictionaryWriteErrorShuttingDown.md",links:fs}],["WriteErrorTooLongIdField",{file:"sharedDictionaryWriteErrorTooLongIdField.md",links:fs}],["WriteErrorUnsupportedType",{file:"sharedDictionaryWriteErrorUnsupportedType.md",links:fs}]]);var Cs=Object.freeze({__proto__:null,SharedDictionaryIssue:Is});const Ss=["StylesheetLoadingIssue","LateImportRule"].join("::");class ys extends c{#o;constructor(e,t){super(`StylesheetLoadingIssue::${e.styleSheetLoadingIssueReason}`,t),this.#o=e}sources(){return[this.#o.sourceCodeLocation]}requests(){if(!this.#o.failedRequestInfo)return[];const{url:e,requestId:t}=this.#o.failedRequestInfo;return t?[{url:e,requestId:t}]:[]}details(){return this.#o}primaryKey(){return JSON.stringify(this.#o)}getDescription(){switch(this.#o.styleSheetLoadingIssueReason){case"LateImportRule":return{file:"stylesheetLateImport.md",links:[]};case"RequestFailed":return{file:"stylesheetRequestFailed.md",links:[]}}}getCategory(){return"Other"}getKind(){return"PageError"}static fromInspectorIssue(e,t){const s=t.details.stylesheetLoadingIssueDetails;return s?[new ys(s,e)]:(console.warn("Stylesheet loading issue without details received"),[])}}var vs=Object.freeze({__proto__:null,lateImportStylesheetLoadingCode:Ss,StylesheetLoadingIssue:ys});class bs{issuesManager;#g=new n.PresentationConsoleMessageHelper.PresentationSourceFrameMessageManager;constructor(e){this.issuesManager=e,this.issuesManager.addEventListener("IssueAdded",this.#h,this),this.issuesManager.addEventListener("FullUpdateRequired",this.#m,this)}#h(e){const{issue:t}=e.data;this.#p(t)}async#p(t){if(!this.#I(t)&&!this.#f(t)&&!this.#k(t))return;const s=t.model();if(!s)return;const i=g(t.details().sourceCodeLocation),r=t.getDescription();if(!r||!i)return;const o=await A(r);if(!o)return;this.#g.addMessage(new ws(o,t.getKind(),(()=>{e.Revealer.reveal(t)})),{line:i.lineNumber,column:i.columnNumber??-1,url:i.url,scriptId:i.scriptId},s.target())}#m(){this.#C();const e=this.issuesManager.issues();for(const t of e)this.#p(t)}#I(e){return e instanceof U&&e.code()===G||e.code()===J}#k(e){return e instanceof es}#f(e){return e.code()===Ss}#C(){this.#g.clear()}}class ws extends a.UISourceCode.Message{#S;constructor(e,t,s){super("Issue",e,s),this.#S=t}getIssueKind(){return this.#S}}var Ds=Object.freeze({__proto__:null,SourceFrameIssuesManager:bs,IssueMessage:ws});let Ts=null;const Rs=new Map([["CookieIssue",de.fromInspectorIssue],["MixedContentIssue",Xt.fromInspectorIssue],["HeavyAdIssue",Nt.fromInspectorIssue],["ContentSecurityPolicyIssue",U.fromInspectorIssue],["BlockedByResponseIssue",function(e,t){const s=t.details.blockedByResponseIssueDetails;return s?$e(s.reason)?[new Ge(s,e)]:[]:(console.warn("BlockedByResponse issue without details received."),[])}],["SharedArrayBufferIssue",ls.fromInspectorIssue],["SharedDictionaryIssue",Is.fromInspectorIssue],["LowTextContrastIssue",$t.fromInspectorIssue],["CorsIssue",Be.fromInspectorIssue],["QuirksModeIssue",os.fromInspectorIssue],["AttributionReportingIssue",p.fromInspectorIssue],["GenericIssue",pt.fromInspectorIssue],["DeprecationIssue",rt.fromInspectorIssue],["ClientHintIssue",N.fromInspectorIssue],["FederatedAuthRequestIssue",Bt.fromInspectorIssue],["BounceTrackingIssue",Ht.fromInspectorIssue],["StylesheetLoadingIssue",ys.fromInspectorIssue],["PropertyRuleIssue",es.fromInspectorIssue],["CookieDeprecationMetadataIssue",re.fromInspectorIssue]]);function As(){return{}}class Es extends e.ObjectWrapper.ObjectWrapper{showThirdPartyIssuesSetting;hideIssueSetting;#y=new WeakMap;#v=new Map;#b=new Map;#w=new Map;#D=new Map;#T=!1;#R=new Map;#A=new Map;constructor(e,t){super(),this.showThirdPartyIssuesSetting=e,this.hideIssueSetting=t,new bs(this),i.TargetManager.TargetManager.instance().observeModels(i.IssuesModel.IssuesModel,this),i.TargetManager.TargetManager.instance().addModelListener(i.ResourceTreeModel.ResourceTreeModel,i.ResourceTreeModel.Events.PrimaryPageChanged,this.#E,this),i.FrameManager.FrameManager.instance().addEventListener("FrameAddedToTarget",this.#P,this),this.showThirdPartyIssuesSetting?.addChangeListener((()=>this.#M())),this.hideIssueSetting?.addChangeListener((()=>this.#M())),i.TargetManager.TargetManager.instance().observeTargets({targetAdded:e=>{e.outermostTarget()===e&&this.#M()},targetRemoved:e=>{}},{scoped:!0})}static instance(e={forceNew:!1,ensureFirst:!1}){if(Ts&&e.ensureFirst)throw new Error('IssuesManager was already created. Either set "ensureFirst" to false or make sure that this invocation is really the first one.');return Ts&&!e.forceNew||(Ts=new Es(e.showThirdPartyIssuesSetting,e.hideIssueSetting)),Ts}static removeInstance(){Ts=null}reloadForAccurateInformationRequired(){return!this.#T}#E(e){const{frame:t,type:s}=e.data,r=new Map;for(const[e,o]of this.#v.entries())if(o.isAssociatedWithRequestId(t.loaderId))r.set(e,o);else if("Activation"===s&&t.resourceTreeModel().target()===o.model()?.target())r.set(e,o);else if("BounceTrackingIssue"===o.code()||"CookieIssue"===o.code()){const s=t.resourceTreeModel().target().model(i.NetworkManager.NetworkManager);!1===s?.requestForLoaderId(t.loaderId)?.hasUserGesture()&&r.set(e,o)}this.#v=r,this.#T=!0,this.#M()}#P(e){const{frame:t}=e.data;t.isOutermostFrame()&&i.TargetManager.TargetManager.instance().isInScope(t.resourceTreeModel())&&this.#M()}modelAdded(e){const t=e.addEventListener("IssueAdded",this.#F,this);this.#y.set(e,t)}modelRemoved(t){const s=this.#y.get(t);s&&e.EventTarget.removeEventListeners([s])}#F(e){const{issuesModel:t,inspectorIssue:s}=e.data,r=function(e,t){const s=Rs.get(t.code);return s?s(e,t):(console.warn(`No handler registered for issue code ${t.code}`),[])}(t,s);for(const e of r){this.addIssue(t,e);const s=e.maybeCreateConsoleMessage();s&&t.target().model(i.ConsoleModel.ConsoleModel)?.addMessage(s)}}addIssue(e,t){if(!t.getDescription())return;const s=t.primaryKey();if(this.#v.has(s))return;this.#v.set(s,t);const i=e.target().outermostTarget();if(i){let e=this.#A.get(i);e||(e=new Set,this.#A.set(i,e)),e.add(t)}if(this.#N(t)){this.#b.set(s,t),this.#w.set(t.getKind(),1+(this.#w.get(t.getKind())||0));const i=t.getIssueId();i&&this.#R.set(i,t);const r=this.hideIssueSetting?.get();this.#x(t,r),t.isHidden()&&this.#D.set(t.getKind(),1+(this.#D.get(t.getKind())||0)),this.dispatchEventToListeners("IssueAdded",{issuesModel:e,issue:t})}this.dispatchEventToListeners("IssuesCountUpdated")}issues(){return this.#b.values()}numberOfIssues(e){return e?(this.#w.get(e)??0)-this.numberOfHiddenIssues(e):this.#b.size-this.numberOfHiddenIssues()}numberOfHiddenIssues(e){if(e)return this.#D.get(e)??0;let t=0;for(const e of this.#D.values())t+=e;return t}numberOfAllStoredIssues(){return this.#v.size}#N(e){const t=i.TargetManager.TargetManager.instance().scopeTarget();return!!t&&(!!this.#A.get(t)?.has(e)&&(this.showThirdPartyIssuesSetting?.get()||!e.isCausedByThirdParty()))}#x(e,t){const s=e.code();if(t&&t[s])return"Hidden"===t[s]?void e.setHidden(!0):void e.setHidden(!1)}#M(){this.#b.clear(),this.#w.clear(),this.#R.clear(),this.#D.clear();const e=this.hideIssueSetting?.get();for(const[t,s]of this.#v)if(this.#N(s)){this.#x(s,e),this.#b.set(t,s),this.#w.set(s.getKind(),1+(this.#w.get(s.getKind())??0)),s.isHidden()&&this.#D.set(s.getKind(),1+(this.#D.get(s.getKind())||0));const i=s.getIssueId();i&&this.#R.set(i,s)}this.dispatchEventToListeners("FullUpdateRequired"),this.dispatchEventToListeners("IssuesCountUpdated")}unhideAllIssues(){for(const e of this.#v.values())e.setHidden(!1);this.hideIssueSetting?.set({})}getIssueById(e){return this.#R.get(e)}}globalThis.addIssueForTest=e=>{const t=i.TargetManager.TargetManager.instance().primaryPageTarget(),s=t?.model(i.IssuesModel.IssuesModel);s?.issueAdded({issue:e})};var Ps=Object.freeze({__proto__:null,defaultHideIssueByCodeSetting:As,getHideIssueByCodeSetting:function(){return e.Settings.Settings.instance().createSetting("hide-issue-by-code-setting-experiment-2021",{})},IssuesManager:Es});class Ms extends e.ResolverBase.ResolverBase{#L=null;#O;constructor(e=Es.instance()){super(),this.#O=e}getForId(e){return this.#O.getIssueById(e)||null}#h(e){const{issue:t}=e.data,s=t.getIssueId();s&&this.onResolve(s,t)}startListening(){this.#L||(this.#L=this.#O.addEventListener("IssueAdded",this.#h,this))}stopListening(){this.#L&&(e.EventTarget.removeEventListeners([this.#L]),this.#L=null)}}var Fs=Object.freeze({__proto__:null,IssueResolver:Ms});function Ns(e,t){if(t instanceof i.NetworkRequest.NetworkRequest)return function(e,t){return e.filter((e=>{for(const s of e.requests())if(s.requestId===t.requestId())return!0;return!1}))}(e,t);if(t instanceof i.Cookie.Cookie)return function(e,t,s,i){return e.filter((e=>{for(const r of e.cookies())if(r.domain===t&&r.name===s&&r.path===i)return!0;return!1}))}(e,t.domain(),t.name(),t.path());throw new Error(`issues can not be associated with ${JSON.stringify(t)}`)}var xs=Object.freeze({__proto__:null,issuesAssociatedWith:Ns,hasIssues:function(e){return Ns(Array.from(Es.instance().issues()),e).length>0},hasIssueOfCategory:function(e,t){return Ns(Array.from(Es.instance().issues()),e).some((e=>e.getCategory()===t))},hasThirdPartyPhaseoutCookieIssue:function(e){return Ns(Array.from(Es.instance().issues()),e).some((e=>"ThirdPartyPhaseoutCookie"===de.getSubCategory(e.code())))},hasThirdPartyPhaseoutCookieIssueForDomain:function(e){return Array.from(Es.instance().issues()).filter((t=>Array.from(t.cookies()).some((t=>t.domain===e)))).some((e=>"ThirdPartyPhaseoutCookie"===de.getSubCategory(e.code())))},reveal:async function(t,s){if("string"==typeof t){const s=Es.instance().getIssueById(t);if(s)return e.Revealer.reveal(s)}const i=Ns(Array.from(Es.instance().issues()),t).filter((e=>!s||e.getCategory()===s));if(i.length>0)return e.Revealer.reveal(i[0])}});export{I as AttributionReportingIssue,C as CheckFormsIssuesTrigger,L as ClientHintIssue,Y as ContentSecurityPolicyIssue,ee as ContrastCheckTrigger,oe as CookieDeprecationMetadataIssue,We as CookieIssue,ze as CorsIssue,Qe as CrossOriginEmbedderPolicyIssue,ot as DeprecationIssue,ct as FederatedAuthUserInfoRequestIssue,Et as GenericIssue,xt as HeavyAdIssue,h as Issue,Fs as IssueResolver,Ps as IssuesManager,Gt as LowTextContrastIssue,E as MarkdownIssueDescription,Zt as MixedContentIssue,ts as PropertyRuleIssue,ns as QuirksModeIssue,xs as RelatedIssue,cs as SharedArrayBufferIssue,Cs as SharedDictionaryIssue,Ds as SourceFrameIssuesManager,vs as StylesheetLoadingIssue};
