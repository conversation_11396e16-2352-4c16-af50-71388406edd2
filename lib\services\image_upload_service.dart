import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:mime/mime.dart';

class ImageUploadService extends ChangeNotifier {
  final _supabase = Supabase.instance.client;
  final _imagePicker = ImagePicker();

  bool _isUploading = false;
  double _uploadProgress = 0.0;
  String? _error;

  bool get isUploading => _isUploading;
  double get uploadProgress => _uploadProgress;
  String? get error => _error;

  // Storage bucket name
  static const String bucketName = 'productimages';

  /// Pick image from device (camera or gallery)
  Future<XFile?> pickImageFromDevice({
    ImageSource source = ImageSource.gallery,
  }) async {
    try {
      _error = null;
      notifyListeners();

      if (kIsWeb) {
        // For web, use file picker
        return await _pickImageWeb();
      } else {
        // For mobile, use image picker
        final XFile? image = await _imagePicker.pickImage(
          source: source,
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );
        return image;
      }
    } catch (e) {
      _error = 'Failed to pick image: $e';
      notifyListeners();
      debugPrint('Error picking image: $e');
      return null;
    }
  }

  /// Pick image for web platform
  Future<XFile?> _pickImageWeb() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          // Create XFile from bytes for web
          return XFile.fromData(
            file.bytes!,
            name: file.name,
            mimeType: lookupMimeType(file.name),
          );
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error picking image on web: $e');
      rethrow;
    }
  }

  /// Upload image to Supabase Storage
  Future<String?> uploadImage(
    XFile imageFile, {
    String? customFileName,
    String? customBucket,
  }) async {
    try {
      _isUploading = true;
      _uploadProgress = 0.0;
      _error = null;
      notifyListeners();

      // Use custom bucket or default bucket
      final String targetBucket = customBucket ?? bucketName;

      // Ensure bucket exists and is public before upload
      try {
        // First try to create bucket if it doesn't exist
        await createBucketIfNotExists(customBucket: customBucket);

        // Then ensure it's public
        await _supabase.storage.updateBucket(
          targetBucket,
          BucketOptions(
            public: true,
            allowedMimeTypes: [
              'image/jpeg',
              'image/png',
              'image/gif',
              'image/webp',
            ],
          ),
        );
      } catch (e) {
        debugPrint('Bucket setup warning (may already be configured): $e');
      }

      // Generate unique filename
      final String fileName =
          customFileName ?? _generateFileName(imageFile.name);

      // Validate file
      if (!_isValidImageFile(imageFile.name)) {
        throw Exception(
          'Invalid file type. Please select a valid image file (JPG, PNG, GIF, WebP).',
        );
      }

      // Get file data
      Uint8List fileBytes;
      if (kIsWeb) {
        fileBytes = await imageFile.readAsBytes();
      } else {
        final File file = File(imageFile.path);
        fileBytes = await file.readAsBytes();
      }

      // Check file size (max 5MB)
      if (fileBytes.length > 5 * 1024 * 1024) {
        throw Exception(
          'File size too large. Please select an image smaller than 5MB.',
        );
      }

      _uploadProgress = 0.1;
      notifyListeners();

      // Upload to Supabase Storage
      await _supabase.storage
          .from(targetBucket)
          .uploadBinary(
            fileName,
            fileBytes,
            fileOptions: FileOptions(
              contentType: lookupMimeType(imageFile.name) ?? 'image/jpeg',
              upsert: false,
            ),
          );

      _uploadProgress = 0.8;
      notifyListeners();

      // Get public URL
      final String publicUrl = _supabase.storage
          .from(targetBucket)
          .getPublicUrl(fileName);

      _uploadProgress = 1.0;
      _isUploading = false;
      notifyListeners();

      debugPrint('Image uploaded successfully: $publicUrl');
      return publicUrl;
    } catch (e) {
      _error = 'Upload failed: $e';
      _isUploading = false;
      _uploadProgress = 0.0;
      notifyListeners();
      debugPrint('Error uploading image: $e');
      return null;
    }
  }

  /// Generate unique filename
  String _generateFileName(String originalName) {
    final String extension = path.extension(originalName).toLowerCase();
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final String randomSuffix = DateTime.now().microsecond.toString();
    return 'product_${timestamp}_$randomSuffix$extension';
  }

  /// Validate if file is a valid image
  bool _isValidImageFile(String fileName) {
    final String extension = path.extension(fileName).toLowerCase();
    const List<String> validExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
    ];
    return validExtensions.contains(extension);
  }

  /// Delete image from storage
  Future<bool> deleteImage(String imageUrl) async {
    try {
      // Extract filename from URL
      final Uri uri = Uri.parse(imageUrl);
      final String fileName = path.basename(uri.path);

      await _supabase.storage.from(bucketName).remove([fileName]);

      debugPrint('Image deleted successfully: $fileName');
      return true;
    } catch (e) {
      debugPrint('Error deleting image: $e');
      return false;
    }
  }

  /// Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// Reset upload progress
  void resetProgress() {
    _uploadProgress = 0.0;
    _isUploading = false;
    notifyListeners();
  }

  /// Show image picker options (mobile only)
  Future<XFile?> showImagePickerOptions(BuildContext context) async {
    if (kIsWeb) {
      // For web, directly pick from files
      return await pickImageFromDevice();
    }

    // For mobile, show options
    return await showModalBottomSheet<XFile?>(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () async {
                  Navigator.pop(context);
                  final image = await pickImageFromDevice(
                    source: ImageSource.gallery,
                  );
                  Navigator.pop(context, image);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Take a Photo'),
                onTap: () async {
                  Navigator.pop(context);
                  final image = await pickImageFromDevice(
                    source: ImageSource.camera,
                  );
                  Navigator.pop(context, image);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text('Cancel'),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Get storage bucket info
  Future<bool> bucketExists({String? customBucket}) async {
    final String targetBucket = customBucket ?? bucketName;

    try {
      await _supabase.storage.getBucket(targetBucket);
      return true;
    } catch (e) {
      debugPrint('Bucket $targetBucket does not exist: $e');
      return false;
    }
  }

  /// Create storage bucket if it doesn't exist
  Future<bool> createBucketIfNotExists({String? customBucket}) async {
    final String targetBucket = customBucket ?? bucketName;

    try {
      // Try to check if bucket exists first
      final exists = await bucketExists(customBucket: customBucket);
      if (exists) {
        debugPrint('Bucket $targetBucket already exists');
        return true;
      }

      // Create bucket if it doesn't exist
      await _supabase.storage.createBucket(
        targetBucket,
        BucketOptions(
          public: true,
          allowedMimeTypes: [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
          ],
        ),
      );

      debugPrint('Bucket $targetBucket created successfully');
      return true;
    } catch (e) {
      debugPrint('Error creating bucket: $e');
      return false;
    }
  }
}
