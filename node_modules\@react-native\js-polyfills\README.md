# @react-native/js-polyfills

[![Version][version-badge]][package]

## Installation

```
yarn add @react-native/js-polyfills
```

*Note: We're using `yarn` to install deps. Feel free to change commands to use `npm` 3+ and `npx` if you like*

[version-badge]: https://img.shields.io/npm/v/@react-native/js-polyfills?style=flat-square
[package]: https://www.npmjs.com/package/@react-native/js-polyfills

## Testing

To run the tests in this package, run the following commands from the React Native root folder:

1. `yarn` to install the dependencies. You just need to run this once
2. `yarn jest packages/polyfills`.
