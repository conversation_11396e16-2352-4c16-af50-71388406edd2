import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/order_model.dart';
import '../../services/auth_service.dart';
import '../../services/order_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

class SellerOrdersPage extends StatelessWidget {
  const SellerOrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Orders'),
        centerTitle: true,
        backgroundColor: AppColors.secondary,
      ),
      body: Consumer2<AuthService, OrderService>(
        builder: (context, authService, orderService, _) {
          if (authService.currentUser == null) {
            return const Center(child: Text('Please log in to view orders'));
          }

          if (orderService.isLoading) {
            return const Center(
              child: CircularProgressIndicator(color: AppColors.secondary),
            );
          }

          final sellerId = authService.currentUser!.id;
          final sellerOrders = orderService.getOrdersBySellerId(sellerId);

          if (sellerOrders.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.receipt_long,
                    size: 64,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No orders yet',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'When customers place orders, they will appear here',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          // Group orders by status
          final pendingOrders =
              sellerOrders
                  .where((order) => order.status == OrderStatus.pending)
                  .toList();
          final processingOrders =
              sellerOrders
                  .where((order) => order.status == OrderStatus.processing)
                  .toList();
          final shippedOrders =
              sellerOrders
                  .where((order) => order.status == OrderStatus.shipped)
                  .toList();
          final deliveredOrders =
              sellerOrders
                  .where((order) => order.status == OrderStatus.delivered)
                  .toList();
          final cancelledOrders =
              sellerOrders
                  .where((order) => order.status == OrderStatus.cancelled)
                  .toList();

          return DefaultTabController(
            length: 5,
            child: Column(
              children: [
                TabBar(
                  isScrollable: true,
                  labelColor: AppColors.secondary,
                  unselectedLabelColor: AppColors.textSecondary,
                  indicatorColor: AppColors.secondary,
                  tabs: [
                    Tab(text: 'Pending (${pendingOrders.length})'),
                    Tab(text: 'Processing (${processingOrders.length})'),
                    Tab(text: 'Shipped (${shippedOrders.length})'),
                    Tab(text: 'Delivered (${deliveredOrders.length})'),
                    Tab(text: 'Cancelled (${cancelledOrders.length})'),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildOrdersList(context, pendingOrders, orderService),
                      _buildOrdersList(context, processingOrders, orderService),
                      _buildOrdersList(context, shippedOrders, orderService),
                      _buildOrdersList(context, deliveredOrders, orderService),
                      _buildOrdersList(context, cancelledOrders, orderService),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildOrdersList(
    BuildContext context,
    List<Order> orders,
    OrderService orderService,
  ) {
    if (orders.isEmpty) {
      return Center(
        child: Text(
          'No orders in this category',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Order header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Order #${order.id.substring(0, 8)}',
                      style: AppTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      _formatDate(order.orderDate),
                      style: AppTextStyles.bodySmall,
                    ),
                  ],
                ),

                const Divider(height: 24),

                // Customer info
                Row(
                  children: [
                    const Icon(
                      Icons.person,
                      size: 16,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Customer: ${order.customerName}',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Shipping address
                if (order.shippingAddress != null) ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Address: ${order.shippingAddress}',
                          style: AppTextStyles.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],

                // Order items
                ...order.items.map(
                  (item) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: DecorationImage(
                              image: NetworkImage(item.productImage),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item.productName,
                                style: AppTextStyles.bodyMedium,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                '${item.quantity} x ${item.price.toStringAsFixed(2)} MAD',
                                style: AppTextStyles.bodySmall,
                              ),
                            ],
                          ),
                        ),
                        Text(
                          '${item.total.toStringAsFixed(2)} MAD',
                          style: AppTextStyles.priceText.copyWith(
                            color: AppColors.secondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const Divider(height: 24),

                // Total
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Total', style: AppTextStyles.bodyLarge),
                    Text(
                      '${order.totalAmount.toStringAsFixed(2)} MAD',
                      style: AppTextStyles.heading3.copyWith(
                        color: AppColors.secondary,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Actions
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (order.status == OrderStatus.pending) ...[
                      OutlinedButton(
                        onPressed:
                            () => _updateOrderStatus(
                              context,
                              order,
                              OrderStatus.cancelled,
                              orderService,
                            ),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.error,
                          side: const BorderSide(color: AppColors.error),
                        ),
                        child: const Text('Cancel'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed:
                            () => _updateOrderStatus(
                              context,
                              order,
                              OrderStatus.processing,
                              orderService,
                            ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.secondary,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Accept'),
                      ),
                    ] else if (order.status == OrderStatus.processing) ...[
                      ElevatedButton(
                        onPressed:
                            () => _updateOrderStatus(
                              context,
                              order,
                              OrderStatus.shipped,
                              orderService,
                            ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.secondary,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Mark as Shipped'),
                      ),
                    ] else if (order.status == OrderStatus.shipped) ...[
                      ElevatedButton(
                        onPressed:
                            () => _updateOrderStatus(
                              context,
                              order,
                              OrderStatus.delivered,
                              orderService,
                            ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.secondary,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Mark as Delivered'),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _updateOrderStatus(
    BuildContext context,
    Order order,
    OrderStatus newStatus,
    OrderService orderService,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Update Order Status'),
            content: Text(
              'Are you sure you want to change the status of Order #${order.id.substring(0, 8)} to ${newStatus.toString().split('.').last}?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondary,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Update'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      if (!context.mounted) return;

      await orderService.updateOrderStatus(order.id, newStatus);

      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Order status updated to ${newStatus.toString().split('.').last}',
          ),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
