let e=-1;const t=()=>e,n=t=>{addEventListener("pageshow",(n=>{n.persisted&&(e=n.timeStamp,t(n))}),!0)},r=()=>{const e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},a=()=>{const e=r();return e&&e.activationStart||0},i=(e,n)=>{const i=r();let s="navigate";t()>=0?s="back-forward-cache":i&&(document.prerendering||a()>0?s="prerender":document.wasDiscarded?s="restore":i.type&&(s=i.type.replace(/_/g,"-")));return{name:e,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:s}},s=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const r=new PerformanceObserver((e=>{Promise.resolve().then((()=>{t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},o=(e,t,n,r)=>{let a,i;return s=>{t.value>=0&&(s||r)&&(i=t.value-(a||0),(i||void 0===a)&&(a=t.value,t.delta=i,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,n),e(t)))}},c=e=>{requestAnimationFrame((()=>requestAnimationFrame((()=>e()))))},l=e=>{document.addEventListener("visibilitychange",(()=>{"hidden"===document.visibilityState&&e()}))},d=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let m=-1;const u=()=>"hidden"!==document.visibilityState||document.prerendering?1/0:0,p=e=>{"hidden"===document.visibilityState&&m>-1&&(m="visibilitychange"===e.type?e.timeStamp:0,g())},h=()=>{addEventListener("visibilitychange",p,!0),addEventListener("prerenderingchange",p,!0)},g=()=>{removeEventListener("visibilitychange",p,!0),removeEventListener("prerenderingchange",p,!0)},f=()=>(m<0&&(m=u(),h(),n((()=>{setTimeout((()=>{m=u(),h()}),0)}))),{get firstHiddenTime(){return m}}),T=e=>{document.prerendering?addEventListener("prerenderingchange",(()=>e()),!0):e()},v=[1800,3e3],y=(e,t)=>{t=t||{},T((()=>{const r=f();let l,d=i("FCP");const m=s("paint",(e=>{e.forEach((e=>{"first-contentful-paint"===e.name&&(m.disconnect(),e.startTime<r.firstHiddenTime&&(d.value=Math.max(e.startTime-a(),0),d.entries.push(e),l(!0)))}))}));m&&(l=o(e,d,v,t.reportAllChanges),n((n=>{d=i("FCP"),l=o(e,d,v,t.reportAllChanges),c((()=>{d.value=performance.now()-n.timeStamp,l(!0)}))})))}))},E=[.1,.25],S=(e,t)=>{t=t||{},y(d((()=>{let r,a=i("CLS",0),d=0,m=[];const u=e=>{e.forEach((e=>{if(!e.hadRecentInput){const t=m[0],n=m[m.length-1];d&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(d+=e.value,m.push(e)):(d=e.value,m=[e])}})),d>a.value&&(a.value=d,a.entries=m,r())},p=s("layout-shift",u);p&&(r=o(e,a,E,t.reportAllChanges),l((()=>{u(p.takeRecords()),r(!0)})),n((()=>{d=0,a=i("CLS",0),r=o(e,a,E,t.reportAllChanges),c((()=>r()))})),setTimeout(r,0))})))};let b=0,C=1/0,L=0;const D=e=>{e.forEach((e=>{e.interactionId&&(C=Math.min(C,e.interactionId),L=Math.max(L,e.interactionId),b=L?(L-C)/7+1:0)}))};let M;const w=()=>{"interactionCount"in performance||M||(M=s("event",D,{type:"event",buffered:!0,durationThreshold:0}))},I=[],F=new Map;let P=0;const x=()=>(M?b:performance.interactionCount||0)-P,k=[],A=e=>{if(k.forEach((t=>t(e))),!e.interactionId&&"first-input"!==e.entryType)return;const t=I[I.length-1],n=F.get(e.interactionId);if(n||I.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{const t={id:e.interactionId,latency:e.duration,entries:[e]};F.set(t.id,t),I.push(t)}I.sort(((e,t)=>t.latency-e.latency)),I.length>10&&I.splice(10).forEach((e=>F.delete(e.id)))}},B=e=>{const t=self.requestIdleCallback||self.setTimeout;let n=-1;return e=d(e),"hidden"===document.visibilityState?e():(n=t(e),l(e)),n},O=[200,500],j=(e,t)=>{"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},T((()=>{w();let r,a=i("INP");const c=e=>{B((()=>{e.forEach(A);const t=(()=>{const e=Math.min(I.length-1,Math.floor(x()/50));return I[e]})();t&&t.latency!==a.value&&(a.value=t.latency,a.entries=t.entries,r())}))},d=s("event",c,{durationThreshold:t.durationThreshold??40});r=o(e,a,O,t.reportAllChanges),d&&(d.observe({type:"first-input",buffered:!0}),l((()=>{c(d.takeRecords()),r(!0)})),n((()=>{P=0,I.length=0,F.clear(),a=i("INP"),r=o(e,a,O,t.reportAllChanges)})))})))},R=[2500,4e3],N={},q=(e,t)=>{t=t||{},T((()=>{const r=f();let m,u=i("LCP");const p=e=>{t.reportAllChanges||(e=e.slice(-1)),e.forEach((e=>{e.startTime<r.firstHiddenTime&&(u.value=Math.max(e.startTime-a(),0),u.entries=[e],m())}))},h=s("largest-contentful-paint",p);if(h){m=o(e,u,R,t.reportAllChanges);const r=d((()=>{N[u.id]||(p(h.takeRecords()),h.disconnect(),N[u.id]=!0,m(!0))}));["keydown","click"].forEach((e=>{addEventListener(e,(()=>B(r)),!0)})),l(r),n((n=>{u=i("LCP"),m=o(e,u,R,t.reportAllChanges),c((()=>{u.value=performance.now()-n.timeStamp,N[u.id]=!0,m(!0)}))}))}}))},_=[800,1800],H=e=>{document.prerendering?T((()=>H(e))):"complete"!==document.readyState?addEventListener("load",(()=>H(e)),!0):setTimeout(e,0)},z=(e,t)=>{t=t||{};let s=i("TTFB"),c=o(e,s,_,t.reportAllChanges);H((()=>{const l=r();l&&(s.value=Math.max(l.responseStart-a(),0),s.entries=[l],c(!0),n((()=>{s=i("TTFB",0),c=o(e,s,_,t.reportAllChanges),c(!0)})))}))};let W,$,U,V;const G={passive:!0,capture:!0},J=new Date,K=(e,t)=>{W||(W=t,$=e,U=new Date,Y(removeEventListener),Q())},Q=()=>{if($>=0&&$<U-J){const e={entryType:"first-input",name:W.type,target:W.target,cancelable:W.cancelable,startTime:W.timeStamp,processingStart:W.timeStamp+$};V.forEach((function(t){t(e)})),V=[]}},X=e=>{if(e.cancelable){const t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?((e,t)=>{const n=()=>{K(e,t),a()},r=()=>{a()},a=()=>{removeEventListener("pointerup",n,G),removeEventListener("pointercancel",r,G)};addEventListener("pointerup",n,G),addEventListener("pointercancel",r,G)})(t,e):K(t,e)}},Y=e=>{["mousedown","keydown","touchstart","pointerdown"].forEach((t=>e(t,X,G)))},Z=[100,300],ee=(e,t)=>{t=t||{},T((()=>{const r=f();let a,c=i("FID");const m=e=>{e.startTime<r.firstHiddenTime&&(c.value=e.processingStart-e.startTime,c.entries.push(e),a(!0))},u=e=>{e.forEach(m)},p=s("first-input",u);a=o(e,c,Z,t.reportAllChanges),p&&(l(d((()=>{u(p.takeRecords()),p.disconnect()}))),n((()=>{var n;c=i("FID"),a=o(e,c,Z,t.reportAllChanges),V=[],$=-1,W=null,Y(addEventListener),n=m,V.push(n),Q()})))}))},te=e=>{if("loading"===document.readyState)return"loading";{const t=r();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}}return"complete"},ne=e=>{const t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},re=(e,t)=>{let n="";try{for(;e&&9!==e.nodeType;){const r=e,a=r.id?"#"+r.id:ne(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?"."+r.classList.value.trim().replace(/\s+/g,"."):"");if(n.length+a.length>(t||100)-1)return n||a;if(n=n?a+">"+n:a,r.id)break;e=r.parentNode}}catch(e){}return n};let ae,ie,se=[],oe=[];const ce=new WeakMap,le=new Map;let de=-1;const me=e=>{se=se.concat(e),ue()},ue=()=>{de<0&&(de=B(pe))},pe=()=>{le.size>10&&le.forEach(((e,t)=>{F.has(t)||le.delete(t)}));const e=I.map((e=>ce.get(e.entries[0]))),t=oe.length-50;oe=oe.filter(((n,r)=>r>=t||e.includes(n)));const n=new Set;for(let e=0;e<oe.length;e++){const t=oe[e];he(t.startTime,t.processingEnd).forEach((e=>{n.add(e)}))}for(let e=0;e<50;e++){const t=se[se.length-1-e];if(!t||t.startTime<ie)break;n.add(t)}se=Array.from(n),de=-1};k.push((e=>{e.interactionId&&e.target&&!le.has(e.interactionId)&&le.set(e.interactionId,e.target)}),(e=>{const t=e.startTime+e.duration;let n;ie=Math.max(ie,e.processingEnd);for(let r=oe.length-1;r>=0;r--){const a=oe[r];if(Math.abs(t-a.renderTime)<=8){n=a,n.startTime=Math.min(e.startTime,n.startTime),n.processingStart=Math.min(e.processingStart,n.processingStart),n.processingEnd=Math.max(e.processingEnd,n.processingEnd),n.entries.push(e);break}}n||(n={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:t,entries:[e]},oe.push(n)),(e.interactionId||"first-input"===e.entryType)&&ce.set(e,n),ue()}));const he=(e,t)=>{const n=[];for(let r,a=0;r=se[a];a++)if(!(r.startTime+r.duration<e)){if(r.startTime>t)break;n.push(r)}return n};var ge=Object.freeze({__proto__:null,onCLS:(e,t)=>{S((t=>{const n=(e=>{let t={};if(e.entries.length){const r=e.entries.reduce(((e,t)=>e&&e.value>t.value?e:t));if(r&&r.sources&&r.sources.length){const e=(n=r.sources).find((e=>e.node&&1===e.node.nodeType))||n[0];e&&(t={largestShiftTarget:re(e.node),largestShiftTime:r.startTime,largestShiftValue:r.value,largestShiftSource:e,largestShiftEntry:r,loadState:te(r.startTime)})}}var n;return Object.assign(e,{attribution:t})})(t);e(n)}),t)},onFCP:(e,n)=>{y((n=>{const a=(e=>{let n={timeToFirstByte:0,firstByteToFCP:e.value,loadState:te(t())};if(e.entries.length){const t=r(),a=e.entries[e.entries.length-1];if(t){const r=t.activationStart||0,i=Math.max(0,t.responseStart-r);n={timeToFirstByte:i,firstByteToFCP:e.value-i,loadState:te(e.entries[0].startTime),navigationEntry:t,fcpEntry:a}}}return Object.assign(e,{attribution:n})})(n);e(a)}),n)},onINP:(e,t)=>{ae||(ae=s("long-animation-frame",me)),j((t=>{const n=(e=>{const t=e.entries[0],n=ce.get(t),r=t.processingStart,a=n.processingEnd,i=n.entries.sort(((e,t)=>e.processingStart-t.processingStart)),s=he(t.startTime,a),o=e.entries.find((e=>e.target)),c=o&&o.target||le.get(t.interactionId),l=[t.startTime+t.duration,a].concat(s.map((e=>e.startTime+e.duration))),d=Math.max.apply(Math,l),m={interactionTarget:re(c),interactionTargetElement:c,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:d,processedEventEntries:i,longAnimationFrameEntries:s,inputDelay:r-t.startTime,processingDuration:a-r,presentationDelay:Math.max(d-a,0),loadState:te(t.startTime)};return Object.assign(e,{attribution:m})})(t);e(n)}),t)},onLCP:(e,t)=>{q((t=>{const n=(e=>{let t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){const n=r();if(n){const r=n.activationStart||0,a=e.entries[e.entries.length-1],i=a.url&&performance.getEntriesByType("resource").filter((e=>e.name===a.url))[0],s=Math.max(0,n.responseStart-r),o=Math.max(s,i?(i.requestStart||i.startTime)-r:0),c=Math.max(o,i?i.responseEnd-r:0),l=Math.max(c,a.startTime-r);t={element:re(a.element),timeToFirstByte:s,resourceLoadDelay:o-s,resourceLoadDuration:c-o,elementRenderDelay:l-c,navigationEntry:n,lcpEntry:a},a.url&&(t.url=a.url),i&&(t.lcpResourceEntry=i)}}return Object.assign(e,{attribution:t})})(t);e(n)}),t)},onTTFB:(e,t)=>{z((t=>{const n=(e=>{let t={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(e.entries.length){const n=e.entries[0],r=n.activationStart||0,a=Math.max((n.workerStart||n.fetchStart)-r,0),i=Math.max(n.domainLookupStart-r,0),s=Math.max(n.connectStart-r,0),o=Math.max(n.connectEnd-r,0);t={waitingDuration:a,cacheDuration:i-a,dnsDuration:s-i,connectionDuration:o-s,requestDuration:e.value-o,navigationEntry:n}}return Object.assign(e,{attribution:t})})(t);e(n)}),t)},CLSThresholds:E,FCPThresholds:v,INPThresholds:O,LCPThresholds:R,TTFBThresholds:_,onFID:(e,t)=>{ee((t=>{const n=(e=>{const t=e.entries[0],n={eventTarget:re(t.target),eventType:t.name,eventTime:t.startTime,eventEntry:t,loadState:te(t.startTime)};return Object.assign(e,{attribution:n})})(t);e(n)}),t)},FIDThresholds:Z});export{ge as Attribution,E as CLSThresholds,v as FCPThresholds,Z as FIDThresholds,O as INPThresholds,R as LCPThresholds,_ as TTFBThresholds,n as onBFCacheRestore,S as onCLS,y as onFCP,ee as onFID,j as onINP,q as onLCP,z as onTTFB};
