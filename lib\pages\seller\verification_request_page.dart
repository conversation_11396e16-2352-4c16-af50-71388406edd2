import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/verification_badge.dart';

class VerificationRequestPage extends StatefulWidget {
  const VerificationRequestPage({Key? key}) : super(key: key);

  @override
  State<VerificationRequestPage> createState() => _VerificationRequestPageState();
}

class _VerificationRequestPageState extends State<VerificationRequestPage> {
  final _formKey = GlobalKey<FormState>();
  final _businessNameController = TextEditingController();
  final _businessAddressController = TextEditingController();
  final _businessPhoneController = TextEditingController();
  final _businessWebsiteController = TextEditingController();
  final _businessDescriptionController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _businessNameController.dispose();
    _businessAddressController.dispose();
    _businessPhoneController.dispose();
    _businessWebsiteController.dispose();
    _businessDescriptionController.dispose();
    super.dispose();
  }

  void _submitVerificationRequest() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSubmitting = true;
      });

      // Simulate API call
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _isSubmitting = false;
          });

          // Show success dialog
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Verification Request Submitted'),
              content: const Text(
                'Your verification request has been submitted successfully. '
                'We will review your request and get back to you within 3-5 business days.',
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context); // Close dialog
                    Navigator.pop(context); // Go back to previous screen
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Get Verified'),
        backgroundColor: AppColors.secondary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Verification Badge Info
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const VerificationBadge(
                      size: 40,
                      isAnimated: true,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Seller Verification',
                            style: AppTextStyles.heading3,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Get a blue verification badge to show customers that your store is authentic and trusted.',
                            style: AppTextStyles.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Benefits Section
            Text(
              'Benefits of Verification',
              style: AppTextStyles.heading2,
            ),
            const SizedBox(height: 16),
            _buildBenefitItem(
              icon: Icons.verified_user,
              title: 'Increased Trust',
              description: 'Customers are more likely to purchase from verified sellers.',
            ),
            _buildBenefitItem(
              icon: Icons.visibility,
              title: 'Better Visibility',
              description: 'Verified sellers appear higher in search results.',
            ),
            _buildBenefitItem(
              icon: Icons.star,
              title: 'Higher Conversion',
              description: 'Verification can increase your conversion rate by up to 30%.',
            ),
            _buildBenefitItem(
              icon: Icons.support_agent,
              title: 'Priority Support',
              description: 'Get access to dedicated seller support.',
            ),

            const SizedBox(height: 24),

            // Verification Form
            Text(
              'Verification Request Form',
              style: AppTextStyles.heading2,
            ),
            const SizedBox(height: 16),
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Business Name
                      TextFormField(
                        controller: _businessNameController,
                        decoration: const InputDecoration(
                          labelText: 'Business Name',
                          hintText: 'Enter your business name',
                          prefixIcon: Icon(Icons.business),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your business name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Business Address
                      TextFormField(
                        controller: _businessAddressController,
                        decoration: const InputDecoration(
                          labelText: 'Business Address',
                          hintText: 'Enter your business address',
                          prefixIcon: Icon(Icons.location_on),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your business address';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Business Phone
                      TextFormField(
                        controller: _businessPhoneController,
                        decoration: const InputDecoration(
                          labelText: 'Business Phone',
                          hintText: 'Enter your business phone',
                          prefixIcon: Icon(Icons.phone),
                        ),
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your business phone';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Business Website (Optional)
                      TextFormField(
                        controller: _businessWebsiteController,
                        decoration: const InputDecoration(
                          labelText: 'Business Website (Optional)',
                          hintText: 'Enter your business website',
                          prefixIcon: Icon(Icons.language),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Business Description
                      TextFormField(
                        controller: _businessDescriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Business Description',
                          hintText: 'Tell us about your business',
                          prefixIcon: Icon(Icons.description),
                          alignLabelWithHint: true,
                        ),
                        maxLines: 5,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your business description';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),

                      // Submit Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isSubmitting ? null : _submitVerificationRequest,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.secondary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: _isSubmitting
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text('Submit Verification Request'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Note
            Card(
              color: AppColors.info.withAlpha(26), // 0.1 opacity
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info,
                      color: AppColors.info,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Verification requests are typically processed within 3-5 business days. '
                        'You will be notified via email once your request has been reviewed.',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefitItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.secondary.withAlpha(26), // 0.1 opacity
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.secondary,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: AppTextStyles.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
