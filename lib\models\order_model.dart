enum OrderStatus { pending, processing, shipped, delivered, cancelled }

class OrderItem {
  final String productId;
  final String productName;
  final String productImage;
  final double price;
  final int quantity;
  
  OrderItem({
    required this.productId,
    required this.productName,
    required this.productImage,
    required this.price,
    required this.quantity,
  });
  
  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      productId: json['productId'],
      productName: json['productName'],
      productImage: json['productImage'],
      price: json['price'].toDouble(),
      quantity: json['quantity'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'productImage': productImage,
      'price': price,
      'quantity': quantity,
    };
  }
  
  double get total => price * quantity;
}

class Order {
  final String id;
  final String customerId;
  final String customerName;
  final String sellerId;
  final String storeId;
  final String storeName;
  final List<OrderItem> items;
  final OrderStatus status;
  final DateTime orderDate;
  final double totalAmount;
  final String? shippingAddress;
  
  Order({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.sellerId,
    required this.storeId,
    required this.storeName,
    required this.items,
    required this.status,
    required this.orderDate,
    required this.totalAmount,
    this.shippingAddress,
  });
  
  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      customerId: json['customerId'],
      customerName: json['customerName'],
      sellerId: json['sellerId'],
      storeId: json['storeId'],
      storeName: json['storeName'],
      items: (json['items'] as List).map((item) => OrderItem.fromJson(item)).toList(),
      status: _parseStatus(json['status']),
      orderDate: DateTime.parse(json['orderDate']),
      totalAmount: json['totalAmount'].toDouble(),
      shippingAddress: json['shippingAddress'],
    );
  }
  
  static OrderStatus _parseStatus(String status) {
    switch (status) {
      case 'pending':
        return OrderStatus.pending;
      case 'processing':
        return OrderStatus.processing;
      case 'shipped':
        return OrderStatus.shipped;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      default:
        return OrderStatus.pending;
    }
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customerId': customerId,
      'customerName': customerName,
      'sellerId': sellerId,
      'storeId': storeId,
      'storeName': storeName,
      'items': items.map((item) => item.toJson()).toList(),
      'status': status.toString().split('.').last,
      'orderDate': orderDate.toIso8601String(),
      'totalAmount': totalAmount,
      'shippingAddress': shippingAddress,
    };
  }
  
  Order copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? sellerId,
    String? storeId,
    String? storeName,
    List<OrderItem>? items,
    OrderStatus? status,
    DateTime? orderDate,
    double? totalAmount,
    String? shippingAddress,
  }) {
    return Order(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      sellerId: sellerId ?? this.sellerId,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      items: items ?? this.items,
      status: status ?? this.status,
      orderDate: orderDate ?? this.orderDate,
      totalAmount: totalAmount ?? this.totalAmount,
      shippingAddress: shippingAddress ?? this.shippingAddress,
    );
  }
}
