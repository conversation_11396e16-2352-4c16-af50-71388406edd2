/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { BrowserLauncher } from "../types/BrowserLauncher";
/**
 * Default `BrowserLauncher` implementation which opens URLs on the host
 * machine.
 */
declare const DefaultBrowserLauncher: BrowserLauncher;
declare const $$EXPORT_DEFAULT_DECLARATION$$: typeof DefaultBrowserLauncher;
export default $$EXPORT_DEFAULT_DECLARATION$$;
