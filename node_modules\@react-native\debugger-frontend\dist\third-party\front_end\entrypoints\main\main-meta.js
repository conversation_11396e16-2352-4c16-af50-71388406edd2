import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as o from"../../core/i18n/i18n.js";import*as a from"../../core/sdk/sdk.js";import*as i from"../../models/workspace/workspace.js";import*as n from"../../ui/legacy/components/utils/utils.js";import*as r from"../../ui/legacy/legacy.js";const s={focusDebuggee:"Focus page",toggleDrawer:"Toggle drawer",nextPanel:"Next panel",previousPanel:"Previous panel",reloadDevtools:"Reload DevTools",restoreLastDockPosition:"Restore last dock position",zoomIn:"Zoom in",zoomOut:"Zoom out",resetZoomLevel:"Reset zoom level",searchInPanel:"Search in panel",cancelSearch:"Cancel search",findNextResult:"Find next result",findPreviousResult:"Find previous result",theme:"Theme:",switchToBrowserPreferredColor:"Switch to browser's preferred color theme",browserPreference:"Browser preference",switchToLightTheme:"Switch to light theme",lightCapital:"Light",switchToDarkTheme:"Switch to dark theme",darkCapital:"Dark",darkLower:"dark",lightLower:"light",panelLayout:"Panel layout:",useHorizontalPanelLayout:"Use horizontal panel layout",horizontal:"horizontal",useVerticalPanelLayout:"Use vertical panel layout",vertical:"vertical",useAutomaticPanelLayout:"Use automatic panel layout",auto:"auto",enableCtrlShortcutToSwitchPanels:"Enable Ctrl + 1-9 shortcut to switch panels",enableShortcutToSwitchPanels:"Enable ⌘ + 1-9 shortcut to switch panels",right:"Right",dockToRight:"Dock to right",bottom:"Bottom",dockToBottom:"Dock to bottom",left:"Left",dockToLeft:"Dock to left",undocked:"Undocked",undockIntoSeparateWindow:"Undock into separate window",devtoolsDefault:"DevTools (Default)",language:"Language:",browserLanguage:"Browser UI language",enableSync:"Enable settings sync",searchAsYouTypeSetting:"Search as you type",searchAsYouTypeCommand:"Enable search as you type",searchOnEnterCommand:"Disable search as you type (press Enter to search)"},l=o.i18n.registerUIStrings("entrypoints/main/main-meta.ts",s),c=o.i18n.getLazilyComputedLocalizedString.bind(void 0,l);let u,d;async function g(){return u||(u=await import("./main.js")),u}function m(){return!t.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()}function p(e){return()=>o.i18n.getLocalizedLanguageRegion(e,o.DevToolsLocale.DevToolsLocale.instance())}r.ActionRegistration.registerActionExtension({category:"DRAWER",actionId:"inspector-main.focus-debuggee",loadActionDelegate:async()=>new((await async function(){return d||(d=await import("../inspector_main/inspector_main.js")),d}()).InspectorMain.FocusDebuggeeActionDelegate),order:100,title:c(s.focusDebuggee)}),r.ActionRegistration.registerActionExtension({category:"DRAWER",actionId:"main.toggle-drawer",loadActionDelegate:async()=>new r.InspectorView.ActionDelegate,order:101,title:c(s.toggleDrawer),bindings:[{shortcut:"Esc"}]}),r.ActionRegistration.registerActionExtension({actionId:"main.next-tab",category:"GLOBAL",title:c(s.nextPanel),loadActionDelegate:async()=>new r.InspectorView.ActionDelegate,bindings:[{platform:"windows,linux",shortcut:"Ctrl+]"},{platform:"mac",shortcut:"Meta+]"}]}),r.ActionRegistration.registerActionExtension({actionId:"main.previous-tab",category:"GLOBAL",title:c(s.previousPanel),loadActionDelegate:async()=>new r.InspectorView.ActionDelegate,bindings:[{platform:"windows,linux",shortcut:"Ctrl+["},{platform:"mac",shortcut:"Meta+["}]}),r.ActionRegistration.registerActionExtension({actionId:"main.debug-reload",category:"GLOBAL",title:c(s.reloadDevtools),loadActionDelegate:async()=>new((await g()).MainImpl.ReloadActionDelegate),bindings:[{shortcut:"Alt+R"}]}),r.ActionRegistration.registerActionExtension({category:"GLOBAL",title:c(s.restoreLastDockPosition),actionId:"main.toggle-dock",loadActionDelegate:async()=>new r.DockController.ToggleDockActionDelegate,bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+D"},{platform:"mac",shortcut:"Meta+Shift+D"}]}),r.ActionRegistration.registerActionExtension({actionId:"main.zoom-in",category:"GLOBAL",title:c(s.zoomIn),loadActionDelegate:async()=>new((await g()).MainImpl.ZoomActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Plus",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+Plus"},{platform:"windows,linux",shortcut:"Ctrl+NumpadPlus"},{platform:"windows,linux",shortcut:"Ctrl+Shift+NumpadPlus"},{platform:"mac",shortcut:"Meta+Plus",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+Plus"},{platform:"mac",shortcut:"Meta+NumpadPlus"},{platform:"mac",shortcut:"Meta+Shift+NumpadPlus"}],condition:m}),r.ActionRegistration.registerActionExtension({actionId:"main.zoom-out",category:"GLOBAL",title:c(s.zoomOut),loadActionDelegate:async()=>new((await g()).MainImpl.ZoomActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Minus",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+Minus"},{platform:"windows,linux",shortcut:"Ctrl+NumpadMinus"},{platform:"windows,linux",shortcut:"Ctrl+Shift+NumpadMinus"},{platform:"mac",shortcut:"Meta+Minus",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+Minus"},{platform:"mac",shortcut:"Meta+NumpadMinus"},{platform:"mac",shortcut:"Meta+Shift+NumpadMinus"}],condition:m}),r.ActionRegistration.registerActionExtension({actionId:"main.zoom-reset",category:"GLOBAL",title:c(s.resetZoomLevel),loadActionDelegate:async()=>new((await g()).MainImpl.ZoomActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+0"},{platform:"windows,linux",shortcut:"Ctrl+Numpad0"},{platform:"mac",shortcut:"Meta+Numpad0"},{platform:"mac",shortcut:"Meta+0"}],condition:m}),r.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.find",category:"GLOBAL",title:c(s.searchInPanel),loadActionDelegate:async()=>new((await g()).MainImpl.SearchActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"F3"}]}),r.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.cancel",category:"GLOBAL",title:c(s.cancelSearch),loadActionDelegate:async()=>new((await g()).MainImpl.SearchActionDelegate),order:10,bindings:[{shortcut:"Esc"}]}),r.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.find-next",category:"GLOBAL",title:c(s.findNextResult),loadActionDelegate:async()=>new((await g()).MainImpl.SearchActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+G",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+G"},{platform:"windows,linux",shortcut:"F3",keybindSets:["devToolsDefault","vsCode"]}]}),r.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.find-previous",category:"GLOBAL",title:c(s.findPreviousResult),loadActionDelegate:async()=>new((await g()).MainImpl.SearchActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+Shift+G",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+G"},{platform:"windows,linux",shortcut:"Shift+F3",keybindSets:["devToolsDefault","vsCode"]}]}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:c(s.theme),settingName:"ui-theme",settingType:"enum",defaultValue:"systemPreferred",reloadRequired:!1,options:[{title:c(s.switchToBrowserPreferredColor),text:c(s.browserPreference),value:"systemPreferred"},{title:c(s.switchToLightTheme),text:c(s.lightCapital),value:"default"},{title:c(s.switchToDarkTheme),text:c(s.darkCapital),value:"dark"}],tags:[c(s.darkLower),c(s.lightLower)]}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:c(s.panelLayout),settingName:"sidebar-position",settingType:"enum",defaultValue:"auto",options:[{title:c(s.useHorizontalPanelLayout),text:c(s.horizontal),value:"bottom"},{title:c(s.useVerticalPanelLayout),text:c(s.vertical),value:"right"},{title:c(s.useAutomaticPanelLayout),text:c(s.auto),value:"auto"}]}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",settingName:"language",settingType:"enum",title:c(s.language),defaultValue:"en-US",options:[{value:"browserLanguage",title:c(s.browserLanguage),text:c(s.browserLanguage)},...o.i18n.getAllSupportedDevToolsLocales().sort().map((e=>{return{value:t=e,title:p(t),text:p(t)};var t}))],reloadRequired:!0}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:"mac"===t.Platform.platform()?c(s.enableShortcutToSwitchPanels):c(s.enableCtrlShortcutToSwitchPanels),settingName:"shortcut-panel-switch",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({category:"GLOBAL",settingName:"currentDockState",settingType:"enum",defaultValue:"right",options:[{value:"right",text:c(s.right),title:c(s.dockToRight)},{value:"bottom",text:c(s.bottom),title:c(s.dockToBottom)},{value:"left",text:c(s.left),title:c(s.dockToLeft)},{value:"undocked",text:c(s.undocked),title:c(s.undockIntoSeparateWindow)}]}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"active-keybind-set",settingType:"enum",defaultValue:"devToolsDefault",options:[{value:"devToolsDefault",title:c(s.devtoolsDefault),text:c(s.devtoolsDefault)},{value:"vsCode",title:o.i18n.lockedLazyString("Visual Studio Code"),text:o.i18n.lockedLazyString("Visual Studio Code")}]}),e.Settings.registerSettingExtension({category:"SYNC",settingName:"sync-preferences",settingType:"boolean",title:c(s.enableSync),defaultValue:!1,reloadRequired:!0}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"user-shortcuts",settingType:"array",defaultValue:[]}),e.Settings.registerSettingExtension({category:"GLOBAL",storageType:"Local",title:c(s.searchAsYouTypeSetting),settingName:"search-as-you-type",settingType:"boolean",order:3,defaultValue:!0,options:[{value:!0,title:c(s.searchAsYouTypeCommand)},{value:!1,title:c(s.searchOnEnterCommand)}]}),r.ViewManager.registerLocationResolver({name:"drawer-view",category:"DRAWER",loadResolver:async()=>r.InspectorView.InspectorView.instance()}),r.ViewManager.registerLocationResolver({name:"drawer-sidebar",category:"DRAWER_SIDEBAR",loadResolver:async()=>r.InspectorView.InspectorView.instance()}),r.ViewManager.registerLocationResolver({name:"panel",category:"PANEL",loadResolver:async()=>r.InspectorView.InspectorView.instance()}),r.ContextMenu.registerProvider({contextTypes:()=>[i.UISourceCode.UISourceCode,a.Resource.Resource,a.NetworkRequest.NetworkRequest],loadProvider:async()=>new n.Linkifier.ContentProviderContextMenuProvider,experiment:void 0}),r.ContextMenu.registerProvider({contextTypes:()=>[Node],loadProvider:async()=>new r.XLink.ContextMenuProvider,experiment:void 0}),r.ContextMenu.registerProvider({contextTypes:()=>[Node],loadProvider:async()=>new n.Linkifier.LinkContextMenuProvider,experiment:void 0}),r.Toolbar.registerToolbarItem({separator:!0,location:"main-toolbar-left",order:100}),r.Toolbar.registerToolbarItem({separator:!0,order:97,location:"main-toolbar-right"}),r.Toolbar.registerToolbarItem({loadItem:async()=>(await g()).MainImpl.SettingsButtonProvider.instance(),order:99,location:"main-toolbar-right"}),r.Toolbar.registerToolbarItem({loadItem:async()=>(await g()).MainImpl.MainMenuItem.instance(),order:100,location:"main-toolbar-right"}),r.Toolbar.registerToolbarItem({loadItem:async()=>r.DockController.CloseButtonProvider.instance(),order:101,location:"main-toolbar-right"}),e.AppProvider.registerAppProvider({loadAppProvider:async()=>(await g()).SimpleApp.SimpleAppProvider.instance(),order:10});
