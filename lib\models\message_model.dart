class Message {
  final String id;
  final String senderId;
  final String receiverId;
  final String content;
  final DateTime timestamp;
  final bool isRead;
  final String? productId;
  final String? orderId;

  Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    required this.timestamp,
    this.isRead = false,
    this.productId,
    this.orderId,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      senderId: json['senderId'],
      receiverId: json['receiverId'],
      content: json['content'],
      timestamp: DateTime.parse(json['timestamp']),
      isRead: json['isRead'] ?? false,
      productId: json['productId'],
      orderId: json['orderId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'receiverId': receiverId,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'productId': productId,
      'orderId': orderId,
    };
  }

  Message copyWith({
    String? id,
    String? senderId,
    String? receiverId,
    String? content,
    DateTime? timestamp,
    bool? isRead,
    String? productId,
    String? orderId,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      productId: productId ?? this.productId,
      orderId: orderId ?? this.orderId,
    );
  }
}

class Conversation {
  final String id;
  final String customerId;
  final String customerName;
  final String customerImage;
  final String sellerId;
  final String sellerName;
  final String sellerImage;
  final DateTime lastMessageTime;
  final String lastMessageContent;
  final int unreadCount;
  final String? productId;
  final String? orderId;

  Conversation({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.customerImage,
    required this.sellerId,
    required this.sellerName,
    required this.sellerImage,
    required this.lastMessageTime,
    required this.lastMessageContent,
    this.unreadCount = 0,
    this.productId,
    this.orderId,
  });

  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'],
      customerId: json['customerId'],
      customerName: json['customerName'],
      customerImage: json['customerImage'],
      sellerId: json['sellerId'],
      sellerName: json['sellerName'],
      sellerImage: json['sellerImage'],
      lastMessageTime: DateTime.parse(json['lastMessageTime']),
      lastMessageContent: json['lastMessageContent'],
      unreadCount: json['unreadCount'] ?? 0,
      productId: json['productId'],
      orderId: json['orderId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customerId': customerId,
      'customerName': customerName,
      'customerImage': customerImage,
      'sellerId': sellerId,
      'sellerName': sellerName,
      'sellerImage': sellerImage,
      'lastMessageTime': lastMessageTime.toIso8601String(),
      'lastMessageContent': lastMessageContent,
      'unreadCount': unreadCount,
      'productId': productId,
      'orderId': orderId,
    };
  }

  Conversation copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? customerImage,
    String? sellerId,
    String? sellerName,
    String? sellerImage,
    DateTime? lastMessageTime,
    String? lastMessageContent,
    int? unreadCount,
    String? productId,
    String? orderId,
  }) {
    return Conversation(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerImage: customerImage ?? this.customerImage,
      sellerId: sellerId ?? this.sellerId,
      sellerName: sellerName ?? this.sellerName,
      sellerImage: sellerImage ?? this.sellerImage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      lastMessageContent: lastMessageContent ?? this.lastMessageContent,
      unreadCount: unreadCount ?? this.unreadCount,
      productId: productId ?? this.productId,
      orderId: orderId ?? this.orderId,
    );
  }
}
