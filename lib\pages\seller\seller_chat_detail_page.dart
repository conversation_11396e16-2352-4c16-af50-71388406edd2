import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../../models/message_model.dart';
import '../../models/product_model.dart';
import '../../services/auth_service.dart';
import '../../services/message_service.dart';
import '../../services/product_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

class SellerChatDetailPage extends StatefulWidget {
  final Conversation conversation;

  const SellerChatDetailPage({Key? key, required this.conversation})
    : super(key: key);

  @override
  State<SellerChatDetailPage> createState() => _SellerChatDetailPageState();
}

class _SellerChatDetailPageState extends State<SellerChatDetailPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _showAttachmentOptions = false;
  bool _isTyping = false;
  bool _showProductSelector = false;
  Product? _selectedProduct;

  @override
  void initState() {
    super.initState();
    // Mark conversation as read when opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final messageService = Provider.of<MessageService>(
        context,
        listen: false,
      );
      final authService = Provider.of<AuthService>(context, listen: false);

      if (authService.currentUser != null) {
        messageService.markConversationAsRead(
          widget.conversation.id,
          authService.currentUser!.id,
        );
      }

      // Scroll to bottom
      _scrollToBottom();
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    final messageService = Provider.of<MessageService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);

    if (authService.currentUser == null) return;

    final newMessage = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      senderId: authService.currentUser!.id,
      receiverId: widget.conversation.customerId,
      content: _messageController.text,
      timestamp: DateTime.now(),
      productId: widget.conversation.productId,
      orderId: widget.conversation.orderId,
    );

    await messageService.sendMessage(newMessage);
    _messageController.clear();

    // Simulate typing indicator for customer
    setState(() {
      _isTyping = true;
    });

    // Simulate customer reply after a random delay
    Future.delayed(Duration(seconds: 1 + math.Random().nextInt(2)), () {
      if (mounted) {
        setState(() {
          _isTyping = false;
        });

        // Send mock reply
        final customerReply = Message(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          senderId: widget.conversation.customerId,
          receiverId: authService.currentUser!.id,
          content: _getRandomReply(),
          timestamp: DateTime.now(),
          productId: widget.conversation.productId,
          orderId: widget.conversation.orderId,
        );

        messageService.sendMessage(customerReply);

        // Scroll to bottom after reply
        Future.delayed(const Duration(milliseconds: 100), () {
          _scrollToBottom();
        });
      }
    });

    // Scroll to bottom after sending
    Future.delayed(const Duration(milliseconds: 100), () {
      _scrollToBottom();
    });
  }

  String _getRandomReply() {
    final replies = [
      'Thank you for your response!',
      'That\'s great to hear.',
      'I appreciate your help.',
      'When will my order arrive?',
      'Do you have this in other colors?',
      'Is there a discount for multiple items?',
      'I\'ll check that out, thanks!',
      'Can you send me more details?',
    ];
    return replies[math.Random().nextInt(replies.length)];
  }

  void _sendProductLink() {
    if (_selectedProduct == null) return;

    final product = _selectedProduct!;
    final messageText =
        'Check out this product: ${product.name} - ${product.price.toStringAsFixed(2)} MAD';

    _messageController.text = messageText;
    _showProductSelector = false;
    _showAttachmentOptions = false;
  }

  void _sendOfferOrCreateOrder() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Create Special Offer'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Discount Percentage',
                    hintText: 'e.g. 10',
                    suffixText: '%',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Offer Message',
                    hintText: 'e.g. Special discount just for you!',
                  ),
                  maxLines: 2,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _messageController.text =
                      'I\'ve created a special offer for you! 10% off your next purchase.';
                  _showAttachmentOptions = false;
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondary,
                ),
                child: const Text('Send Offer'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              backgroundImage: NetworkImage(widget.conversation.customerImage),
              radius: 16,
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.conversation.customerName,
                  style: const TextStyle(fontSize: 16),
                ),
                if (_isTyping)
                  const Text(
                    'typing...',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
              ],
            ),
          ],
        ),
        backgroundColor: AppColors.secondary,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              showModalBottomSheet(
                context: context,
                builder: (context) => _buildCustomerInfoSheet(),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              showModalBottomSheet(
                context: context,
                builder: (context) => _buildMoreOptionsSheet(),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Chat messages
          Expanded(
            child: Builder(
              builder: (context) {
                // Get message service and auth service
                final messageService = Provider.of<MessageService>(context);
                final authService = Provider.of<AuthService>(context);

                final messages = messageService.getMessagesForConversation(
                  widget.conversation.id,
                );

                if (messages.isEmpty) {
                  return const Center(child: Text('No messages yet'));
                }

                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    final isMe =
                        message.senderId == authService.currentUser?.id;

                    return _buildMessageBubble(message, isMe);
                  },
                );
              },
            ),
          ),

          // Product selector
          if (_showProductSelector)
            Container(
              height: 120,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 4,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Consumer<ProductService>(
                builder: (context, productService, _) {
                  final products = productService.products;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Select a product',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () {
                                setState(() {
                                  _showProductSelector = false;
                                });
                              },
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: products.length,
                          itemBuilder: (context, index) {
                            final product = products[index];
                            final isSelected =
                                _selectedProduct?.id == product.id;

                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedProduct = product;
                                });
                              },
                              child: Container(
                                width: 80,
                                margin: const EdgeInsets.only(right: 8),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color:
                                        isSelected
                                            ? AppColors.secondary
                                            : Colors.grey.shade300,
                                    width: isSelected ? 2 : 1,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  children: [
                                    Expanded(
                                      child: ClipRRect(
                                        borderRadius:
                                            const BorderRadius.vertical(
                                              top: Radius.circular(7),
                                            ),
                                        child: Image.network(
                                          product.imageUrl,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  const Icon(
                                                    Icons.image,
                                                    size: 40,
                                                  ),
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(4),
                                      child: Text(
                                        '${product.price.toStringAsFixed(0)} MAD',
                                        style: const TextStyle(fontSize: 10),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      if (_selectedProduct != null)
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 8,
                            right: 16,
                            left: 16,
                          ),
                          child: ElevatedButton(
                            onPressed: _sendProductLink,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.secondary,
                              minimumSize: const Size(double.infinity, 36),
                              padding: EdgeInsets.zero,
                            ),
                            child: const Text('Send Product'),
                          ),
                        ),
                    ],
                  );
                },
              ),
            ),

          // Attachment options
          if (_showAttachmentOptions)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 4,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildAttachmentOption(
                    icon: Icons.image,
                    label: 'Photo',
                    color: Colors.blue,
                    onTap: () {
                      setState(() {
                        _showAttachmentOptions = false;
                      });
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Photo upload not implemented in this demo',
                          ),
                        ),
                      );
                    },
                  ),
                  _buildAttachmentOption(
                    icon: Icons.inventory,
                    label: 'Product',
                    color: Colors.green,
                    onTap: () {
                      setState(() {
                        _showProductSelector = true;
                        _showAttachmentOptions = false;
                      });
                    },
                  ),
                  _buildAttachmentOption(
                    icon: Icons.local_offer,
                    label: 'Offer',
                    color: Colors.orange,
                    onTap: _sendOfferOrCreateOrder,
                  ),
                  _buildAttachmentOption(
                    icon: Icons.shopping_cart,
                    label: 'Order',
                    color: AppColors.secondary,
                    onTap: () {
                      setState(() {
                        _showAttachmentOptions = false;
                      });
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Create order not implemented in this demo',
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

          // Message input
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 4,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: Row(
              children: [
                IconButton(
                  icon: Icon(
                    _showAttachmentOptions ? Icons.close : Icons.add,
                    color: _showAttachmentOptions ? Colors.red : Colors.grey,
                  ),
                  onPressed: () {
                    setState(() {
                      _showAttachmentOptions = !_showAttachmentOptions;
                      if (_showAttachmentOptions) {
                        _showProductSelector = false;
                      }
                    });
                  },
                ),
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: 'Type a message...',
                      border: InputBorder.none,
                    ),
                    textCapitalization: TextCapitalization.sentences,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.send),
                  color: AppColors.secondary,
                  onPressed: _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(Message message, bool isMe) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              backgroundImage: NetworkImage(widget.conversation.customerImage),
              radius: 16,
            ),
            const SizedBox(width: 8),
          ],

          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                color: isMe ? AppColors.secondary : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: TextStyle(
                      color: isMe ? Colors.white : AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatMessageTime(message.timestamp),
                        style: TextStyle(
                          fontSize: 10,
                          color:
                              isMe
                                  ? Colors.white.withAlpha(179)
                                  : AppColors.textSecondary,
                        ),
                      ),
                      if (isMe) ...[
                        const SizedBox(width: 4),
                        Icon(
                          message.isRead ? Icons.done_all : Icons.done,
                          size: 12,
                          color: Colors.white.withAlpha(179),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),

          if (isMe) const SizedBox(width: 24),
        ],
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withAlpha(26),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color),
          ),
          const SizedBox(height: 4),
          Text(label, style: TextStyle(fontSize: 12, color: color)),
        ],
      ),
    );
  }

  Widget _buildCustomerInfoSheet() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircleAvatar(
            radius: 40,
            backgroundImage: NetworkImage(widget.conversation.customerImage),
          ),
          const SizedBox(height: 16),
          Text(widget.conversation.customerName, style: AppTextStyles.heading3),
          const SizedBox(height: 24),
          _buildInfoRow(Icons.calendar_today, 'Customer since', 'Jan 2023'),
          const Divider(height: 24),
          _buildInfoRow(Icons.shopping_bag, 'Orders', '5 orders'),
          const Divider(height: 24),
          _buildInfoRow(Icons.star, 'Rating', '4.8/5'),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.secondary,
              minimumSize: const Size(double.infinity, 48),
            ),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildMoreOptionsSheet() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildOptionTile(
            icon: Icons.block,
            title: 'Block Customer',
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Block functionality not implemented in this demo',
                  ),
                ),
              );
            },
          ),
          _buildOptionTile(
            icon: Icons.report,
            title: 'Report Conversation',
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Report functionality not implemented in this demo',
                  ),
                ),
              );
            },
          ),
          _buildOptionTile(
            icon: Icons.delete,
            title: 'Delete Conversation',
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Delete functionality not implemented in this demo',
                  ),
                ),
              );
            },
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: AppColors.secondary, size: 20),
        const SizedBox(width: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? AppColors.error : AppColors.secondary,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? AppColors.error : AppColors.textPrimary,
        ),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  String _formatMessageTime(DateTime time) {
    return '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
  }
}
