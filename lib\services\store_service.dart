import 'package:flutter/material.dart';
import '../models/store_model.dart';

class StoreService extends ChangeNotifier {
  final List<Store> _stores = [];
  bool _isLoading = false;
  
  List<Store> get stores => _stores;
  bool get isLoading => _isLoading;
  
  // Initialize with dummy data
  StoreService() {
    _loadDummyStores();
  }
  
  void _loadDummyStores() {
    _isLoading = true;
    notifyListeners();
    
    // Add dummy stores
    _stores.addAll([
      Store(
        id: '1',
        name: 'Fatima\'s Crafts',
        description: 'Handcrafted Moroccan items made with love and tradition.',
        imageUrl: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        sellerId: '2',
        productIds: ['1', '2', '7'],
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Store(
        id: '2',
        name: 'Moroccan Treasures',
        description: 'Authentic Moroccan home decor and furniture.',
        imageUrl: 'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        sellerId: '3',
        productIds: ['3', '4'],
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
      ),
      Store(
        id: '3',
        name: 'Marrakech Market',
        description: 'Bringing the flavors and scents of Marrakech to your home.',
        imageUrl: 'https://images.unsplash.com/photo-1532336414038-cf19250c5757?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        sellerId: '4',
        productIds: ['5', '6'],
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
      ),
      Store(
        id: '4',
        name: 'Casablanca Fashion',
        description: 'Modern Moroccan fashion with traditional influences.',
        imageUrl: 'https://images.unsplash.com/photo-1603073163308-9654c3fb70b5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        sellerId: '5',
        productIds: ['8'],
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
    ]);
    
    _isLoading = false;
    notifyListeners();
  }
  
  // Get store by ID
  Store? getStoreById(String storeId) {
    try {
      return _stores.firstWhere((store) => store.id == storeId);
    } catch (e) {
      return null;
    }
  }
  
  // Get store by seller ID
  Store? getStoreBySellerId(String sellerId) {
    try {
      return _stores.firstWhere((store) => store.sellerId == sellerId);
    } catch (e) {
      return null;
    }
  }
  
  // Create a new store
  Future<Store> createStore({
    required String name,
    required String description,
    required String imageUrl,
    required String sellerId,
  }) async {
    _isLoading = true;
    notifyListeners();
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    final newStore = Store(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      imageUrl: imageUrl,
      sellerId: sellerId,
      productIds: [],
      createdAt: DateTime.now(),
    );
    
    _stores.add(newStore);
    
    _isLoading = false;
    notifyListeners();
    
    return newStore;
  }
  
  // Update a store
  Future<void> updateStore(Store updatedStore) async {
    _isLoading = true;
    notifyListeners();
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    final index = _stores.indexWhere((store) => store.id == updatedStore.id);
    if (index != -1) {
      _stores[index] = updatedStore;
    }
    
    _isLoading = false;
    notifyListeners();
  }
  
  // Add product to store
  Future<void> addProductToStore(String storeId, String productId) async {
    final storeIndex = _stores.indexWhere((store) => store.id == storeId);
    if (storeIndex == -1) return;
    
    final store = _stores[storeIndex];
    if (!store.productIds.contains(productId)) {
      final updatedProductIds = List<String>.from(store.productIds)..add(productId);
      _stores[storeIndex] = store.copyWith(productIds: updatedProductIds);
      notifyListeners();
    }
  }
  
  // Remove product from store
  Future<void> removeProductFromStore(String storeId, String productId) async {
    final storeIndex = _stores.indexWhere((store) => store.id == storeId);
    if (storeIndex == -1) return;
    
    final store = _stores[storeIndex];
    if (store.productIds.contains(productId)) {
      final updatedProductIds = List<String>.from(store.productIds)..remove(productId);
      _stores[storeIndex] = store.copyWith(productIds: updatedProductIds);
      notifyListeners();
    }
  }
}
