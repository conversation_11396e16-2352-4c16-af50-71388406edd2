import 'package:flutter/material.dart';
import '../models/order_model.dart';

class OrderService extends ChangeNotifier {
  final List<Order> _orders = [];
  bool _isLoading = false;
  
  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  
  // Initialize with dummy data
  OrderService() {
    _loadDummyOrders();
  }
  
  void _loadDummyOrders() {
    _isLoading = true;
    notifyListeners();
    
    // Add dummy orders
    _orders.addAll([
      Order(
        id: '1',
        customerId: '1',
        customerName: 'Ahmed Customer',
        sellerId: '2',
        storeId: '1',
        storeName: 'Fatima\'s Crafts',
        items: [
          OrderItem(
            productId: '1',
            productName: 'Moroccan Ceramic Plate',
            productImage: 'https://images.unsplash.com/photo-1603006905393-c279c4320be5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            price: 250.0,
            quantity: 2,
          ),
          OrderItem(
            productId: '2',
            productName: 'Argan Oil (100ml)',
            productImage: 'https://images.unsplash.com/photo-1608571423902-eed4a5ad8108?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            price: 120.0,
            quantity: 1,
          ),
        ],
        status: OrderStatus.delivered,
        orderDate: DateTime.now().subtract(const Duration(days: 15)),
        totalAmount: 620.0,
        shippingAddress: '123 Casablanca Street, Rabat, Morocco',
      ),
      Order(
        id: '2',
        customerId: '1',
        customerName: 'Ahmed Customer',
        sellerId: '3',
        storeId: '2',
        storeName: 'Moroccan Treasures',
        items: [
          OrderItem(
            productId: '3',
            productName: 'Moroccan Leather Pouf',
            productImage: 'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            price: 450.0,
            quantity: 1,
          ),
        ],
        status: OrderStatus.shipped,
        orderDate: DateTime.now().subtract(const Duration(days: 5)),
        totalAmount: 450.0,
        shippingAddress: '123 Casablanca Street, Rabat, Morocco',
      ),
      Order(
        id: '3',
        customerId: '1',
        customerName: 'Ahmed Customer',
        sellerId: '4',
        storeId: '3',
        storeName: 'Marrakech Market',
        items: [
          OrderItem(
            productId: '5',
            productName: 'Moroccan Mint Tea Set',
            productImage: 'https://images.unsplash.com/photo-1577968897966-3d4325b36b61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            price: 350.0,
            quantity: 1,
          ),
          OrderItem(
            productId: '6',
            productName: 'Moroccan Spice Set',
            productImage: 'https://images.unsplash.com/photo-1532336414038-cf19250c5757?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            price: 180.0,
            quantity: 1,
          ),
        ],
        status: OrderStatus.processing,
        orderDate: DateTime.now().subtract(const Duration(days: 2)),
        totalAmount: 530.0,
        shippingAddress: '123 Casablanca Street, Rabat, Morocco',
      ),
      Order(
        id: '4',
        customerId: '6',
        customerName: 'Layla Benali',
        sellerId: '2',
        storeId: '1',
        storeName: 'Fatima\'s Crafts',
        items: [
          OrderItem(
            productId: '7',
            productName: 'Handmade Moroccan Lantern',
            productImage: 'https://images.unsplash.com/photo-1518509562904-e7ef99cdcc86?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            price: 280.0,
            quantity: 1,
          ),
        ],
        status: OrderStatus.pending,
        orderDate: DateTime.now().subtract(const Duration(hours: 6)),
        totalAmount: 280.0,
        shippingAddress: '456 Marrakech Avenue, Marrakech, Morocco',
      ),
    ]);
    
    _isLoading = false;
    notifyListeners();
  }
  
  // Get orders by customer ID
  List<Order> getOrdersByCustomerId(String customerId) {
    return _orders.where((order) => order.customerId == customerId).toList();
  }
  
  // Get orders by seller ID
  List<Order> getOrdersBySellerId(String sellerId) {
    return _orders.where((order) => order.sellerId == sellerId).toList();
  }
  
  // Get orders by store ID
  List<Order> getOrdersByStoreId(String storeId) {
    return _orders.where((order) => order.storeId == storeId).toList();
  }
  
  // Create a new order
  Future<Order> createOrder({
    required String customerId,
    required String customerName,
    required String sellerId,
    required String storeId,
    required String storeName,
    required List<OrderItem> items,
    required double totalAmount,
    required String shippingAddress,
  }) async {
    _isLoading = true;
    notifyListeners();
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    final newOrder = Order(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      customerId: customerId,
      customerName: customerName,
      sellerId: sellerId,
      storeId: storeId,
      storeName: storeName,
      items: items,
      status: OrderStatus.pending,
      orderDate: DateTime.now(),
      totalAmount: totalAmount,
      shippingAddress: shippingAddress,
    );
    
    _orders.add(newOrder);
    
    _isLoading = false;
    notifyListeners();
    
    return newOrder;
  }
  
  // Update order status
  Future<void> updateOrderStatus(String orderId, OrderStatus newStatus) async {
    _isLoading = true;
    notifyListeners();
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    final index = _orders.indexWhere((order) => order.id == orderId);
    if (index != -1) {
      _orders[index] = _orders[index].copyWith(status: newStatus);
    }
    
    _isLoading = false;
    notifyListeners();
  }
}
