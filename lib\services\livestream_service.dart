import 'package:flutter/material.dart';
import '../models/livestream_model.dart';

class LivestreamService extends ChangeNotifier {
  final List<LiveStream> _livestreams = [];
  bool _isLoading = false;
  
  List<LiveStream> get livestreams => _livestreams;
  bool get isLoading => _isLoading;
  
  // Get all active livestreams
  List<LiveStream> get activeLivestreams => 
      _livestreams.where((stream) => stream.status == LiveStreamStatus.live).toList();
  
  // Get all scheduled livestreams
  List<LiveStream> get scheduledLivestreams => 
      _livestreams.where((stream) => stream.status == LiveStreamStatus.scheduled).toList();
  
  // Initialize with dummy data
  LivestreamService() {
    _loadDummyLivestreams();
  }
  
  void _loadDummyLivestreams() {
    _isLoading = true;
    notifyListeners();
    
    // Add dummy livestreams
    _livestreams.addAll([
      LiveStream(
        id: '1',
        title: 'Handcrafted Ceramics Showcase',
        description: 'Join us for a live demonstration of traditional Moroccan ceramic making.',
        thumbnailUrl: 'https://images.unsplash.com/photo-1603006905393-c279c4320be5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        sellerId: '2',
        storeId: '1',
        storeName: 'Fatima\'s Crafts',
        storeImageUrl: 'https://randomuser.me/api/portraits/women/1.jpg',
        status: LiveStreamStatus.live,
        startTime: DateTime.now().subtract(const Duration(minutes: 30)),
        featuredProductIds: ['1', '7'],
        viewerCount: 128,
      ),
      LiveStream(
        id: '2',
        title: 'Moroccan Carpet Weaving',
        description: 'Learn about the art of Berber carpet weaving and our latest collection.',
        thumbnailUrl: 'https://images.unsplash.com/photo-1600166898405-da9535204843?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        sellerId: '3',
        storeId: '2',
        storeName: 'Moroccan Treasures',
        storeImageUrl: 'https://randomuser.me/api/portraits/men/3.jpg',
        status: LiveStreamStatus.scheduled,
        startTime: DateTime.now().add(const Duration(hours: 2)),
        featuredProductIds: ['3', '4'],
        viewerCount: 0,
      ),
      LiveStream(
        id: '3',
        title: 'Moroccan Cooking Class',
        description: 'Cook along with us as we prepare traditional Moroccan tagine.',
        thumbnailUrl: 'https://images.unsplash.com/photo-1577968897966-3d4325b36b61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        sellerId: '4',
        storeId: '3',
        storeName: 'Marrakech Market',
        storeImageUrl: 'https://randomuser.me/api/portraits/women/4.jpg',
        status: LiveStreamStatus.live,
        startTime: DateTime.now().subtract(const Duration(minutes: 15)),
        featuredProductIds: ['5', '6'],
        viewerCount: 85,
      ),
      LiveStream(
        id: '4',
        title: 'Modern Moroccan Fashion Show',
        description: 'Exclusive preview of our new collection of modern Moroccan fashion.',
        thumbnailUrl: 'https://images.unsplash.com/photo-1603073163308-9654c3fb70b5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        sellerId: '5',
        storeId: '4',
        storeName: 'Casablanca Fashion',
        storeImageUrl: 'https://randomuser.me/api/portraits/women/5.jpg',
        status: LiveStreamStatus.scheduled,
        startTime: DateTime.now().add(const Duration(days: 1)),
        featuredProductIds: ['8'],
        viewerCount: 0,
      ),
    ]);
    
    _isLoading = false;
    notifyListeners();
  }
  
  // Get livestreams by seller ID
  List<LiveStream> getLivestreamsBySellerId(String sellerId) {
    return _livestreams.where((stream) => stream.sellerId == sellerId).toList();
  }
  
  // Get livestreams by store ID
  List<LiveStream> getLivestreamsByStoreId(String storeId) {
    return _livestreams.where((stream) => stream.storeId == storeId).toList();
  }
  
  // Create a new livestream
  Future<LiveStream> createLivestream({
    required String title,
    required String description,
    required String thumbnailUrl,
    required String sellerId,
    required String storeId,
    required String storeName,
    required String storeImageUrl,
    required DateTime startTime,
    List<String> featuredProductIds = const [],
  }) async {
    _isLoading = true;
    notifyListeners();
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    final newLivestream = LiveStream(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      thumbnailUrl: thumbnailUrl,
      sellerId: sellerId,
      storeId: storeId,
      storeName: storeName,
      storeImageUrl: storeImageUrl,
      status: startTime.isBefore(DateTime.now()) 
          ? LiveStreamStatus.live 
          : LiveStreamStatus.scheduled,
      startTime: startTime,
      featuredProductIds: featuredProductIds,
    );
    
    _livestreams.add(newLivestream);
    
    _isLoading = false;
    notifyListeners();
    
    return newLivestream;
  }
  
  // Start a livestream
  Future<void> startLivestream(String livestreamId) async {
    _isLoading = true;
    notifyListeners();
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    final index = _livestreams.indexWhere((stream) => stream.id == livestreamId);
    if (index != -1) {
      _livestreams[index] = _livestreams[index].copyWith(
        status: LiveStreamStatus.live,
        startTime: DateTime.now(),
      );
    }
    
    _isLoading = false;
    notifyListeners();
  }
  
  // End a livestream
  Future<void> endLivestream(String livestreamId) async {
    _isLoading = true;
    notifyListeners();
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    final index = _livestreams.indexWhere((stream) => stream.id == livestreamId);
    if (index != -1) {
      _livestreams[index] = _livestreams[index].copyWith(
        status: LiveStreamStatus.ended,
        endTime: DateTime.now(),
      );
    }
    
    _isLoading = false;
    notifyListeners();
  }
  
  // Update viewer count
  void updateViewerCount(String livestreamId, int viewerCount) {
    final index = _livestreams.indexWhere((stream) => stream.id == livestreamId);
    if (index != -1) {
      _livestreams[index] = _livestreams[index].copyWith(viewerCount: viewerCount);
      notifyListeners();
    }
  }
}
