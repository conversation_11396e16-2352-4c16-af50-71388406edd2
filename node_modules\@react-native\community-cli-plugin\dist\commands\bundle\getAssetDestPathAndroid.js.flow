/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

import type { PackagerAsset } from "./assetPathUtils";

declare function getAssetDestPathAndroid(
  asset: PackagerAsset,
  scale: number
): string;

declare export default typeof getAssetDestPathAndroid;
