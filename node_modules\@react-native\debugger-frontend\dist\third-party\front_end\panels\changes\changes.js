import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as i from"../../core/i18n/i18n.js";import*as o from"../../models/workspace_diff/workspace_diff.js";import{PanelUtils as s}from"../utils/utils.js";import*as r from"../../third_party/diff/diff.js";import*as n from"../../ui/components/diff_view/diff_view.js";import*as a from"../../ui/legacy/legacy.js";import*as c from"../../ui/visual_logging/visual_logging.js";import*as l from"../../core/platform/platform.js";import*as d from"../../models/workspace/workspace.js";import*as h from"../../ui/components/icon_button/icon_button.js";import*as f from"../snippets/snippets.js";const u=new CSSStyleSheet;u.replaceSync(".tree-outline li{min-height:20px}devtools-icon{color:var(--icon-file-default)}.navigator-sm-script-tree-item devtools-icon,\n.navigator-script-tree-item devtools-icon,\n.navigator-snippet-tree-item devtools-icon{color:var(--icon-file-script)}.navigator-sm-stylesheet-tree-item devtools-icon,\n.navigator-stylesheet-tree-item devtools-icon{color:var(--icon-file-styles)}.navigator-image-tree-item devtools-icon{color:var(--icon-file-image)}.navigator-font-tree-item devtools-icon{color:var(--icon-file-font)}.tree-outline li:hover:not(.selected) .selection{display:block;& devtools-icon{color:var(--icon-default-hover)}}@media (forced-colors: active){li,\n  devtools-icon{forced-color-adjust:none;color:ButtonText!important}}\n/*# sourceURL=changesSidebar.css */\n");const p={sFromSourceMap:"{PH1} (from source map)"},m=i.i18n.registerUIStrings("panels/changes/ChangesSidebar.ts",p),g=i.i18n.getLocalizedString.bind(void 0,m);class S extends(e.ObjectWrapper.eventMixin(a.Widget.Widget)){treeoutline;treeElements;workspaceDiff;constructor(e){super(),this.treeoutline=new a.TreeOutline.TreeOutlineInShadow,this.treeoutline.setFocusable(!1),this.treeoutline.setComparator(((e,t)=>l.StringUtilities.compare(e.titleAsText(),t.titleAsText()))),this.treeoutline.addEventListener(a.TreeOutline.Events.ElementSelected,this.selectionChanged,this),a.ARIAUtils.markAsTablist(this.treeoutline.contentElement),this.element.appendChild(this.treeoutline.element),this.element.setAttribute("jslog",`${c.pane("sidebar").track({resize:!0})}`),this.treeElements=new Map,this.workspaceDiff=e,this.workspaceDiff.modifiedUISourceCodes().forEach(this.addUISourceCode.bind(this)),this.workspaceDiff.addEventListener("ModifiedStatusChanged",this.uiSourceCodeMofiedStatusChanged,this)}selectUISourceCode(e,t){const i=this.treeElements.get(e);i&&i.select(t)}selectedUISourceCode(){return this.treeoutline.selectedTreeElement?this.treeoutline.selectedTreeElement.uiSourceCode:null}selectionChanged(){this.dispatchEventToListeners("SelectedUISourceCodeChanged")}uiSourceCodeMofiedStatusChanged(e){e.data.isModified?this.addUISourceCode(e.data.uiSourceCode):this.removeUISourceCode(e.data.uiSourceCode)}removeUISourceCode(e){const t=this.treeElements.get(e);if(this.treeElements.delete(e),this.treeoutline.selectedTreeElement===t){const e=t.previousSibling||t.nextSibling;e?e.select(!0):(t.deselect(),this.selectionChanged())}t&&(this.treeoutline.removeChild(t),t.dispose()),0===this.treeoutline.rootElement().childCount()&&this.treeoutline.setFocusable(!1)}addUISourceCode(e){const t=new C(e);this.treeElements.set(e,t),this.treeoutline.setFocusable(!0),this.treeoutline.appendChild(t),this.treeoutline.selectedTreeElement||t.select(!0)}wasShown(){super.wasShown(),this.treeoutline.registerCSSFiles([u])}}class C extends a.TreeOutline.TreeElement{uiSourceCode;eventListeners;constructor(e){super(),this.uiSourceCode=e,this.listItemElement.classList.add("navigator-"+e.contentType().name()+"-tree-item"),a.ARIAUtils.markAsTab(this.listItemElement);let t="document";f.ScriptSnippetFileSystem.isSnippetsUISourceCode(this.uiSourceCode)&&(t="snippet");const i=h.Icon.create(t);this.setLeadingIcons([i]),this.eventListeners=[e.addEventListener(d.UISourceCode.Events.TitleChanged,this.updateTitle,this),e.addEventListener(d.UISourceCode.Events.WorkingCopyChanged,this.updateTitle,this),e.addEventListener(d.UISourceCode.Events.WorkingCopyCommitted,this.updateTitle,this)],this.updateTitle()}updateTitle(){let e=this.uiSourceCode.displayName();this.uiSourceCode.isDirty()&&(e="*"+e),this.title=e;let t=this.uiSourceCode.url();this.uiSourceCode.contentType().isFromSourceMap()&&(t=g(p.sFromSourceMap,{PH1:this.uiSourceCode.displayName()})),this.tooltip=t}dispose(){e.EventTarget.removeEventListeners(this.eventListeners)}}var b=Object.freeze({__proto__:null,ChangesSidebar:S,UISourceCodeTreeElement:C});const v=new CSSStyleSheet;v.replaceSync('[slot="insertion-point-main"]{flex-direction:column;display:flex}[slot="insertion-point-sidebar"]{overflow:auto}.diff-container{flex:1;overflow:auto}:focus.selected{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}.changes-toolbar{background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider)}\n/*# sourceURL=changesView.css */\n');const w={noChanges:"No changes",binaryData:"Binary data",sInsertions:"{n, plural, =1 {# insertion (+)} other {# insertions (+)}}",sDeletions:"{n, plural, =1 {# deletion (-)} other {# deletions (-)}}",copy:"Copy"},y=i.i18n.registerUIStrings("panels/changes/ChangesView.ts",w),I=i.i18n.getLocalizedString.bind(void 0,y);class T extends a.Widget.VBox{emptyWidget;workspaceDiff;changesSidebar;selectedUISourceCode;#e;diffContainer;toolbar;diffStats;diffView;constructor(){super(!0),this.element.setAttribute("jslog",`${c.panel("changes").track({resize:!0})}`);const e=new a.SplitWidget.SplitWidget(!0,!1),t=new a.Widget.Widget;e.setMainWidget(t),e.show(this.contentElement),this.emptyWidget=new a.EmptyWidget.EmptyWidget(""),this.emptyWidget.show(t.element),this.workspaceDiff=o.WorkspaceDiff.workspaceDiff(),this.changesSidebar=new S(this.workspaceDiff),this.changesSidebar.addEventListener("SelectedUISourceCodeChanged",this.selectedUISourceCodeChanged,this),e.setSidebarWidget(this.changesSidebar),this.selectedUISourceCode=null,this.diffContainer=t.element.createChild("div","diff-container"),a.ARIAUtils.markAsTabpanel(this.diffContainer),this.diffContainer.addEventListener("click",(e=>this.click(e))),this.diffView=this.diffContainer.appendChild(new n.DiffView.DiffView),this.toolbar=new a.Toolbar.Toolbar("changes-toolbar",t.element),this.toolbar.element.setAttribute("jslog",`${c.toolbar()}`),this.toolbar.appendToolbarItem(a.Toolbar.Toolbar.createActionButtonForId("changes.revert")),this.diffStats=new a.Toolbar.ToolbarText(""),this.toolbar.appendToolbarItem(this.diffStats),this.toolbar.appendToolbarItem(new a.Toolbar.ToolbarSeparator),this.toolbar.appendToolbarItem(a.Toolbar.Toolbar.createActionButtonForId("changes.copy",{showLabel:!0,label:()=>I(w.copy)})),this.hideDiff(I(w.noChanges)),this.selectedUISourceCodeChanged()}selectedUISourceCodeChanged(){this.revealUISourceCode(this.changesSidebar.selectedUISourceCode()),a.ActionRegistry.ActionRegistry.instance().getAction("changes.copy").setEnabled(this.selectedUISourceCode?.contentType()===e.ResourceType.resourceTypes.Stylesheet)}revert(){const e=this.selectedUISourceCode;e&&this.workspaceDiff.revertToOriginal(e)}async copy(){const e=this.selectedUISourceCode;if(!e)return;const i=await this.workspaceDiff.requestDiff(e,{shouldFormatDiff:!0});if(!i||i?.diff.length<2)return;const o=await s.formatCSSChangesFromDiff(i.diff);t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(o)}click(t){if(this.selectedUISourceCode)for(const i of t.composedPath()){if(!(i instanceof HTMLElement))continue;const o=i.ownerDocument.getSelection();if(o?.toString())break;if(i.classList.contains("diff-line-content")&&i.hasAttribute("data-line-number")){let o=Number(i.dataset.lineNumber)-1;this.#e&&(o=this.#e.formattedToOriginal(o,0)[0]),e.Revealer.reveal(this.selectedUISourceCode.uiLocation(o,0),!1),t.consume(!0);break}if(i.classList.contains("diff-listing"))break}}revealUISourceCode(e){this.selectedUISourceCode!==e&&(this.selectedUISourceCode&&this.workspaceDiff.unsubscribeFromDiffChange(this.selectedUISourceCode,this.refreshDiff,this),e&&this.isShowing()&&this.workspaceDiff.subscribeToDiffChange(e,this.refreshDiff,this),this.selectedUISourceCode=e,this.refreshDiff())}wasShown(){a.Context.Context.instance().setFlavor(T,this),this.registerCSSFiles([v]),super.wasShown(),this.refreshDiff()}willHide(){super.willHide(),a.Context.Context.instance().setFlavor(T,null)}async refreshDiff(){if(!this.isShowing())return;if(!this.selectedUISourceCode)return void this.renderDiffRows();const e=this.selectedUISourceCode;if(!e.contentType().isTextType())return void this.hideDiff(I(w.binaryData));const t=await this.workspaceDiff.requestDiff(e,{shouldFormatDiff:!0});this.selectedUISourceCode===e&&(this.#e=t?.formattedCurrentMapping,this.renderDiffRows(t?.diff))}hideDiff(e){this.diffStats.setText(""),this.toolbar.setEnabled(!1),this.diffContainer.style.display="none",this.emptyWidget.text=e,this.emptyWidget.showWidget()}renderDiffRows(e){if(!e||1===e.length&&e[0][0]===r.Diff.Operation.Equal)this.hideDiff(I(w.noChanges));else{this.diffStats.setText(function(e){const t=e.reduce(((e,t)=>e+(t[0]===r.Diff.Operation.Insert?t[1].length:0)),0),i=e.reduce(((e,t)=>e+(t[0]===r.Diff.Operation.Delete?t[1].length:0)),0),o=I(w.sDeletions,{n:i});return`${I(w.sInsertions,{n:t})}, ${o}`}(e)),this.toolbar.setEnabled(!0),this.emptyWidget.hideWidget();const t=this.selectedUISourceCode.mimeType();this.diffContainer.style.display="block",this.diffView.data={diff:e,mimeType:t}}}}var D=Object.freeze({__proto__:null,ChangesView:T,ActionDelegate:class{handleAction(e,t){const i=e.flavor(T);if(null===i)return!1;switch(t){case"changes.revert":return i.revert(),!0;case"changes.copy":return i.copy(),!0}return!1}}});export{b as ChangesSidebar,D as ChangesView};
